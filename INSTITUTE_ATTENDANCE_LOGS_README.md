# Institute Attendance Logs System for Examino

This document provides detailed information about the Institute Attendance Logs system implemented in Examino, including real-time tracking, reporting, and integration with Supabase.

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Database Schema](#database-schema)
4. [Components](#components)
5. [Supabase Integration](#supabase-integration)
6. [Usage Guide](#usage-guide)
7. [Bulk Attendance Marking](#bulk-attendance-marking)
8. [Student Attendance Reports](#student-attendance-reports)
9. [Security Considerations](#security-considerations)
10. [Performance Optimizations](#performance-optimizations)

## Overview

The Institute Attendance Logs system provides a comprehensive solution for institutes to track and manage student attendance. It integrates with Supabase for real-time updates, secure storage, and efficient querying, with features for bulk attendance marking, reporting, and visualization.

## Features

- **Real-time Attendance Tracking**: View attendance records as they are created
- **Bulk Attendance Marking**: Mark attendance for multiple students at once
- **Student Attendance Reports**: Generate detailed reports on student attendance
- **Filtering Capabilities**: Filter by date range, class, status, and student
- **Export to CSV**: Export attendance data for reporting and analysis
- **Calendar View**: View attendance records by date in a calendar format
- **List View**: View detailed attendance records in a list format
- **Statistics**: View attendance statistics including attendance rate
- **Dashboard Overview**: See attendance statistics and recent activity at a glance

## Database Schema

The institute attendance logs system uses the following database schema:

```sql
CREATE TABLE IF NOT EXISTS attendance_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  institute_id UUID REFERENCES users(id) ON DELETE CASCADE,
  student_id UUID REFERENCES students(id) ON DELETE CASCADE,
  class_id UUID REFERENCES classes(id) ON DELETE SET NULL,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  status BOOLEAN NOT NULL DEFAULT true, -- true = present, false = absent
  verification_method TEXT, -- 'face', 'manual', 'qr', etc.
  confidence FLOAT, -- confidence score for face verification (0-1)
  notes TEXT, -- optional notes about the attendance
  location JSONB, -- optional location data
  device_info JSONB, -- device information for audit
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Indexes

```sql
CREATE INDEX IF NOT EXISTS idx_attendance_logs_institute_id ON attendance_logs(institute_id);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_student_id ON attendance_logs(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_class_id ON attendance_logs(class_id);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_date ON attendance_logs(date);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_status ON attendance_logs(status);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_created_at ON attendance_logs(created_at);
```

### Database Functions

The system includes several database functions for retrieving and analyzing attendance data:

1. **get_institute_attendance_logs**: Retrieves attendance logs for an institute
2. **get_institute_attendance_statistics**: Calculates attendance statistics for an institute
3. **get_institute_daily_attendance_summary**: Generates daily attendance summary for an institute
4. **get_institute_student_attendance_summary**: Generates student attendance summary for an institute

## Components

### InstituteAttendanceLogs Component

The main component for displaying and managing attendance records in the institute dashboard. Features include:

- Filtering by date range, class, status, and student
- Exporting attendance data to CSV
- Viewing attendance statistics
- Managing attendance records (marking present/absent, deleting records)
- Calendar view for visualizing attendance by date

### BulkAttendanceMarking Component

A modal component for marking attendance for multiple students at once. Features include:

- Selecting a class and date
- Selecting multiple students
- Marking students as present or absent
- Adding notes and verification method

### StudentAttendanceReport Component

A component for generating detailed reports on student attendance. Features include:

- Filtering by date range, class, and student
- Exporting attendance data to CSV
- Viewing attendance statistics by student
- Visualizing attendance rates with progress bars

### InstituteDashboard Component

The main dashboard component for institute users. Features include:

- Overview of attendance statistics
- Recent activity feed
- Quick access to attendance logs, face verification, and reports
- Real-time updates for attendance records

## Supabase Integration

The system integrates with Supabase for real-time updates, secure storage, and efficient querying:

### Real-time Updates

```javascript
// Set up real-time subscription for attendance logs
const attendanceSubscription = supabase
  .channel('attendance-logs-changes')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'attendance_logs'
  }, (payload) => {
    // Handle update
  })
  .subscribe();
```

### Attendance Service

The `attendanceService.js` utility provides functions for interacting with the Supabase database:

```javascript
// Get attendance logs
const logs = await getAttendanceLogs({
  startDate,
  endDate,
  classId,
  status
});

// Get attendance statistics
const stats = await getAttendanceStatistics({
  startDate,
  endDate,
  classId
});

// Create attendance log
const log = await createAttendanceLog({
  studentId,
  classId,
  status,
  verificationMethod,
  notes,
  deviceInfo
});
```

### Row-Level Security

The system uses Supabase's Row-Level Security (RLS) policies to ensure that institutes can only access their own attendance logs:

```sql
CREATE POLICY "Institutes can view their own attendance logs"
ON attendance_logs FOR SELECT
USING (
  institute_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name = 'admin'
  )
);
```

## Usage Guide

### Viewing Attendance Logs

1. Navigate to the "Attendance Logs" tab in the Institute Dashboard
2. Use the filters to select a date range, class, status, or search for a specific student
3. Click "Apply Filters" to update the displayed records
4. Use the "Export CSV" button to download the filtered records
5. Toggle between list and calendar views using the view mode buttons

### Marking Attendance

#### Using Bulk Attendance Marking

1. Navigate to the "Attendance Logs" tab
2. Click the "Bulk Attendance" button
3. Select a class and date
4. Select the students you want to mark
5. Click "Mark as Present" or "Mark as Absent"

#### Manually

1. Navigate to the "Attendance Logs" tab
2. Find the student in the list
3. Click "Mark as Present" or "Mark as Absent" to update their attendance status

### Viewing Student Attendance Reports

1. Navigate to the "Student Report" tab
2. Use the filters to select a date range, class, or search for a specific student
3. View the attendance statistics for each student
4. Use the "Export CSV" button to download the report

## Bulk Attendance Marking

The Bulk Attendance Marking feature allows institutes to mark attendance for multiple students at once:

1. **Select Class**: Choose a class from the dropdown
2. **Select Date**: Choose a date for the attendance records
3. **Select Students**: Select individual students or use the "Select All" checkbox
4. **Mark Attendance**: Click "Mark as Present" or "Mark as Absent"
5. **Add Notes**: Optionally add notes about the attendance
6. **Select Verification Method**: Choose how the attendance was verified (manual, face recognition, etc.)

## Student Attendance Reports

The Student Attendance Reports feature provides detailed insights into student attendance:

1. **Attendance Rate**: View the percentage of days a student was present
2. **Present/Absent Days**: See how many days a student was present or absent
3. **Total Days**: See the total number of days attendance was recorded
4. **Visual Indicators**: Color-coded progress bars indicate attendance rates
5. **Filtering**: Filter by date range, class, or student
6. **Export**: Export the report to CSV for further analysis

## Security Considerations

The system implements several security measures:

1. **Row-Level Security**: Restrict access to attendance logs
2. **Audit Logging**: Track all attendance operations with device information
3. **Role-Based Access Control**: Different views for admin and institute users
4. **Verification Method Tracking**: Record how attendance was verified

## Performance Optimizations

The system includes several performance optimizations:

1. **Indexed Queries**: Use database indexes for faster queries
2. **Database Functions**: Use PostgreSQL functions for complex queries
3. **Real-time Subscriptions**: Use Supabase's real-time capabilities for instant updates
4. **Lazy Loading**: Load attendance records only when needed
5. **Filtering**: Filter data on the server side for better performance
