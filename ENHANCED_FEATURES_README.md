# Enhanced Features for Examino

This document outlines the enhanced features implemented for the Examino platform, focusing on secure student photo storage, face recognition, and real-time updates.

## Table of Contents

1. [Secure Student Photo Storage](#secure-student-photo-storage)
2. [Face Recognition Pipeline](#face-recognition-pipeline)
3. [Real-time Database Updates](#real-time-database-updates)
4. [Optimized Image Handling](#optimized-image-handling)
5. [Security Enhancements](#security-enhancements)
6. [Server-Side Face Recognition](#server-side-face-recognition)

## Secure Student Photo Storage

### Database Schema

The enhanced database schema includes:

- Vector storage for face embeddings using pgvector
- Secure storage buckets with proper permissions
- Quality metrics for stored photos
- Audit trails for all photo operations

```sql
-- Enable pgvector extension for face embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Update students table with enhanced photo storage fields
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS photo_path TEXT, -- Path to image in Storage
ADD COLUMN IF NOT EXISTS embedding vector(512), -- For face embeddings using pgvector
ADD COLUMN IF NOT EXISTS last_photo_update TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS photo_quality FLOAT; -- Quality score of the stored photo (0-1)
```

### Security Policies

Row-Level Security (RLS) policies ensure that:

- Admins can view all student photos
- Students can only view their own photos
- Photos are stored in private buckets with controlled access

## Face Recognition Pipeline

### Client-Side Processing

The face recognition pipeline uses face-api.js for client-side processing:

1. **Face Detection**: Detects faces in images or video streams
2. **Face Encoding**: Generates 512-dimensional face embeddings
3. **Quality Assessment**: Evaluates face image quality based on size, position, and clarity
4. **Secure Storage**: Encrypts and stores face data in Supabase

### Server-Side Processing

For enhanced security and performance, a Python Flask API provides server-side face recognition:

1. **API Endpoint**: `/verify_face` for comparing faces
2. **Face Recognition Library**: Uses the face_recognition Python library
3. **Rate Limiting**: Prevents abuse with IP-based rate limiting
4. **Error Handling**: Robust error handling and logging

## Real-time Database Updates

Real-time updates are implemented using Supabase's real-time capabilities:

1. **Channels**: Subscribe to database changes for immediate updates
2. **Notifications**: Display toast notifications for important events
3. **Update Types**: Support for photo updates, attendance events, and more

```javascript
// Subscribe to photo updates
const photoSubscription = supabase
  .channel('photo-updates')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'photo_updates'
  }, (payload) => {
    // Handle update
  })
  .subscribe();
```

## Optimized Image Handling

### Image Compression

Images are optimized for storage and transmission:

1. **WebP Format**: Uses WebP for smaller file sizes (30% smaller than JPEG)
2. **Resolution Reduction**: Automatically resizes images to appropriate dimensions
3. **Quality Control**: Adjustable compression quality (default: 70%)

### Face Quality Assessment

The system evaluates face image quality based on:

1. **Face Size**: Optimal face size relative to the image (15-25% of image area)
2. **Face Position**: Centered faces score higher
3. **Aspect Ratio**: Faces with natural proportions score higher

## Security Enhancements

### Data Encryption

Sensitive data is encrypted:

1. **Face Descriptors**: Encrypted using AES-256 before storage
2. **Storage Policies**: Strict access controls on storage buckets
3. **Environment Variables**: Sensitive configuration stored in environment variables

### Audit Logging

All operations are logged for security and compliance:

1. **Photo Updates**: Tracks all photo uploads and updates
2. **Access Logs**: Records who accessed what data and when
3. **Error Logs**: Detailed error logging for troubleshooting

## Server-Side Face Recognition

### Setup Instructions

To set up the server-side face recognition API:

1. Install dependencies:
   ```bash
   cd server
   pip install -r requirements.txt
   ```

2. Configure environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase credentials
   ```

3. Run the server:
   ```bash
   python app.py
   ```

### API Endpoints

- **POST /verify_face**: Verifies a face against a stored reference
  ```json
  {
    "student_id": "uuid",
    "captured_image_url": "https://example.com/image.jpg",
    "reference_image_url": "https://example.com/reference.jpg"
  }
  ```

- **GET /health**: Health check endpoint

## Integration

These enhanced features are integrated into the Examino platform through:

1. **FaceDataUpload Component**: Enhanced with quality indicators and optimized storage
2. **RealtimeUpdates Component**: Displays real-time notifications for database changes
3. **ImageUtils**: Provides optimized image handling functions
4. **FaceRecognitionService**: Unified API for both client-side and server-side processing

## Performance Considerations

- **Vector Indexing**: Uses IVF-Flat indexing for fast similarity searches
- **Caching**: Implements caching for frequently accessed data
- **Lazy Loading**: Models are loaded only when needed
- **Progressive Enhancement**: Falls back to simpler methods when advanced features are unavailable
