{"name": "examino", "private": true, "version": "0.0.0", "type": "commonjs", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@supabase/supabase-js": "^2.49.8", "bcryptjs": "^3.0.2", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.9", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "face-api.js": "^0.22.2", "framer-motion": "^12.10.5", "pako": "^2.1.0", "papaparse": "^5.5.2", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-countdown": "^2.3.6", "react-csv": "^2.2.2", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.3", "react-intersection-observer": "^9.16.0", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^7.5.3", "react-webcam": "^7.2.0", "screenfull": "^6.0.2", "uuid": "^11.1.0", "zxcvbn": "^4.4.2"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "madge": "^8.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.3.5", "vite": "^6.3.1"}}