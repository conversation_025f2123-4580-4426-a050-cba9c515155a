# Secure Login System

Examino implements a professional, secure login system with role-based access control and environment-aware features.

## Features

### Security Features

- **Password Strength Validation**: Uses zxcvbn to enforce strong passwords in production
- **Rate Limiting**: Locks accounts after 5 failed attempts for 15 minutes
- **Audit Logging**: Tracks all authentication events with IP address and metadata
- **Environment-Based Security**: Stricter security rules in production environments
- **Role-Based Access Control**: Different access levels for Admin, Institute, and Student roles

### User Experience

- **Password Strength Meter**: Visual feedback on password strength during login/signup
- **Role-Based Redirection**: Users are directed to appropriate dashboards based on their role
- **Secure Error Messages**: Non-revealing error messages that don't leak information
- **Development Mode Helpers**: Demo account access in development environments only

## Implementation Details

### Authentication Flow

1. **Login Request**: User submits email and password
2. **Rate Limit Check**: System checks if the account is temporarily locked
3. **Environment Check**: In production, blocks demo accounts and enforces password complexity
4. **Authentication**: Validates credentials against Supa<PERSON> Auth
5. **Role Resolution**: Determines user role from database
6. **Audit Logging**: Records successful or failed login attempt
7. **Redirection**: Routes user to appropriate dashboard based on role

### Database Schema

```sql
CREATE TABLE auth_logs (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  event TEXT NOT NULL,
  status TEXT NOT NULL,
  ip_address INET,
  metadata JSONB DEFAULT '{}'::jsonb,
  timestamp TIMESTAMPTZ DEFAULT NOW()
);
```

### Password Policy

- **Development**: Lenient validation for easier testing
- **Production**: Enforces:
  - Minimum zxcvbn score of 3 (Good)
  - Prevents common passwords
  - Checks against dictionary attacks

## Usage

### Demo Accounts (Development Only)

| Role      | Email                  | Password    | Access Level                |
|-----------|------------------------|-------------|----------------------------|
| Admin     | <EMAIL>       | password123 | Full system access         |
| Institute | <EMAIL>  | password123 | Manage students & exams    |
| Student   | <EMAIL>    | password123 | Take exams & attendance    |

### Production Access

In production, users must:
1. Be invited by an admin (for Admin/Institute roles)
2. Use institutional email addresses
3. Create strong passwords that meet security requirements
4. Complete facial recognition registration (for students)

## Security Best Practices

- **Never** hardcode credentials in source code
- Use environment variables for configuration
- Implement proper error handling that doesn't reveal sensitive information
- Log authentication events for audit purposes
- Enforce rate limiting to prevent brute force attacks
- Use HTTPS for all communications
