-- Role-Based Access Control (RBAC) Schema for Examino

-- Create user_roles table
CREATE TABLE IF NOT EXISTS user_roles (
  role_id SERIAL PRIMARY KEY,
  role_name TEXT NOT NULL UNIQUE -- 'admin', 'institute', 'student'
);

-- Insert default roles
INSERT INTO user_roles (role_name) VALUES 
  ('admin'),
  ('institute'),
  ('student')
ON CONFLICT (role_name) DO NOTHING;

-- Add role_id to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS role_id INTEGER REFERENCES user_roles(role_id);
-- Set default role to student for existing users
UPDATE users SET role_id = (SELECT role_id FROM user_roles WHERE role_name = 'student') WHERE role_id IS NULL;
-- Make role_id required
ALTER TABLE users ALTER COLUMN role_id SET NOT NULL;

-- Add institute-specific data
ALTER TABLE users ADD COLUMN IF NOT EXISTS institute_id TEXT DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE;

-- Create invite_codes table for admin/institute signup
CREATE TABLE IF NOT EXISTS invite_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code TEXT NOT NULL UNIQUE,
  role_id INTEGER REFERENCES user_roles(role_id) NOT NULL,
  email TEXT NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  used_at TIMESTAMPTZ DEFAULT NULL,
  used_by UUID REFERENCES auth.users(id) DEFAULT NULL
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_invite_codes_code ON invite_codes(code);

-- Enable Row-Level Security (RLS)
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE invite_codes ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_roles
CREATE POLICY "Anyone can view roles"
  ON user_roles FOR SELECT
  USING (true);

-- RLS Policies for invite_codes
CREATE POLICY "Admins can create invite codes"
  ON invite_codes FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role_id = (SELECT role_id FROM user_roles WHERE role_name = 'admin')
    )
  );

CREATE POLICY "Admins can view all invite codes"
  ON invite_codes FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role_id = (SELECT role_id FROM user_roles WHERE role_name = 'admin')
    )
  );

CREATE POLICY "Institutes can view their own invite codes"
  ON invite_codes FOR SELECT
  USING (
    created_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role_id = (SELECT role_id FROM user_roles WHERE role_name = 'institute')
    )
  );

CREATE POLICY "Public can use valid invite codes"
  ON invite_codes FOR SELECT
  USING (
    used_at IS NULL AND 
    expires_at > NOW()
  );

-- Update RLS policies for users table
CREATE POLICY "Admins can view all users"
  ON users FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = auth.uid() 
      AND u.role_id = (SELECT role_id FROM user_roles WHERE role_name = 'admin')
    )
  );

CREATE POLICY "Institutes can view their students"
  ON users FOR SELECT
  USING (
    (
      -- Institutes can view students in their institute
      EXISTS (
        SELECT 1 FROM users u
        WHERE u.id = auth.uid() 
        AND u.role_id = (SELECT role_id FROM user_roles WHERE role_name = 'institute')
      ) AND
      institute_id = (
        SELECT institute_id FROM users WHERE id = auth.uid()
      ) AND
      role_id = (SELECT role_id FROM user_roles WHERE role_name = 'student')
    ) OR
    -- Users can view themselves
    id = auth.uid()
  );

-- Create function to check user role
CREATE OR REPLACE FUNCTION check_user_role(user_id UUID, role_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = user_id AND user_roles.role_name = role_name
  );
END;
$$;

-- Create function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  role TEXT;
BEGIN
  SELECT user_roles.role_name INTO role
  FROM users
  JOIN user_roles ON users.role_id = user_roles.role_id
  WHERE users.id = user_id;
  
  RETURN role;
END;
$$;

-- Create function to generate invite code
CREATE OR REPLACE FUNCTION generate_invite_code(role_name TEXT, email TEXT, expiry_days INTEGER DEFAULT 7)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_code TEXT;
  role_id INTEGER;
BEGIN
  -- Check if user is admin
  IF NOT check_user_role(auth.uid(), 'admin') THEN
    RAISE EXCEPTION 'Only admins can generate invite codes';
  END IF;
  
  -- Get role_id
  SELECT user_roles.role_id INTO role_id
  FROM user_roles
  WHERE user_roles.role_name = role_name;
  
  IF role_id IS NULL THEN
    RAISE EXCEPTION 'Invalid role name';
  END IF;
  
  -- Generate random code
  new_code := encode(gen_random_bytes(6), 'hex');
  
  -- Insert into invite_codes
  INSERT INTO invite_codes (
    code,
    role_id,
    email,
    created_by,
    expires_at
  ) VALUES (
    new_code,
    role_id,
    email,
    auth.uid(),
    NOW() + (expiry_days || ' days')::INTERVAL
  );
  
  RETURN new_code;
END;
$$;

-- Create function to verify invite code
CREATE OR REPLACE FUNCTION verify_invite_code(code TEXT)
RETURNS TABLE (
  is_valid BOOLEAN,
  role_name TEXT,
  email TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (invite_codes.used_at IS NULL AND invite_codes.expires_at > NOW()) AS is_valid,
    user_roles.role_name,
    invite_codes.email
  FROM invite_codes
  JOIN user_roles ON invite_codes.role_id = user_roles.role_id
  WHERE invite_codes.code = verify_invite_code.code;
END;
$$;

-- Create function to use invite code
CREATE OR REPLACE FUNCTION use_invite_code(code TEXT, user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  invite_record RECORD;
BEGIN
  -- Get invite code record
  SELECT * INTO invite_record
  FROM invite_codes
  WHERE invite_codes.code = use_invite_code.code
  AND used_at IS NULL
  AND expires_at > NOW();
  
  IF invite_record IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Mark as used
  UPDATE invite_codes
  SET used_at = NOW(),
      used_by = user_id
  WHERE invite_codes.code = use_invite_code.code;
  
  -- Update user role
  UPDATE users
  SET role_id = invite_record.role_id,
      is_verified = TRUE
  WHERE id = user_id;
  
  RETURN TRUE;
END;
$$;
