-- Student Face Enrollment System
-- Enhanced schema for manual student enrollment with face capture

-- Create student_faces storage bucket policy (to be configured in Supabase dashboard)
-- This migration assumes the bucket 'student_faces' exists

-- Add enrollment tracking fields to students table
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS enrollment_date TIMESTAMPTZ DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS enrollment_method TEXT DEFAULT 'manual',
ADD COLUMN IF NOT EXISTS face_enrollment_status TEXT DEFAULT 'pending' CHECK (face_enrollment_status IN ('pending', 'enrolled', 'failed', 'retry_required')),
ADD COLUMN IF NOT EXISTS manual_override_allowed BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS enrollment_notes TEXT;

-- Create face enrollment attempts table
CREATE TABLE IF NOT EXISTS face_enrollment_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id TEXT NOT NULL,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  image_url TEXT,
  faces_detected INTEGER,
  face_detection_score FLOAT,
  image_quality_score FLOAT,
  enrollment_status TEXT NOT NULL CHECK (enrollment_status IN ('success', 'no_face', 'multiple_faces', 'poor_quality', 'upload_failed', 'validation_error')),
  error_message TEXT,
  image_dimensions JSONB,
  processing_time_ms INTEGER,
  ip_address INET DEFAULT inet_client_addr(),
  user_agent TEXT,
  enrolled_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Function to enroll student with face verification
CREATE OR REPLACE FUNCTION enroll_student_with_face(
  p_student_id TEXT,
  p_name TEXT,
  p_email TEXT,
  p_image_url TEXT,
  p_faces_detected INTEGER,
  p_face_detection_score FLOAT DEFAULT NULL,
  p_image_quality_score FLOAT DEFAULT NULL,
  p_image_dimensions JSONB DEFAULT NULL,
  p_processing_time_ms INTEGER DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  enrollment_status TEXT;
  result_message TEXT;
  attempt_id UUID;
  student_exists BOOLEAN;
  enrollment_success BOOLEAN := FALSE;
BEGIN
  -- Determine enrollment status based on face detection
  IF p_faces_detected = 0 THEN
    enrollment_status := 'no_face';
    result_message := 'No face found, please try again.';
  ELSIF p_faces_detected > 1 THEN
    enrollment_status := 'multiple_faces';
    result_message := 'Multiple faces detected. Please upload a photo with only one face.';
  ELSIF p_face_detection_score IS NOT NULL AND p_face_detection_score < 0.7 THEN
    enrollment_status := 'poor_quality';
    result_message := 'Face detection quality too low, please try again with better lighting.';
  ELSIF p_image_url IS NULL OR p_image_url = '' THEN
    enrollment_status := 'upload_failed';
    result_message := 'Image upload failed, please try again.';
  ELSE
    enrollment_status := 'success';
    result_message := 'Student enrolled successfully.';
    enrollment_success := TRUE;
  END IF;
  
  -- Log enrollment attempt
  INSERT INTO face_enrollment_attempts (
    student_id,
    name,
    email,
    image_url,
    faces_detected,
    face_detection_score,
    image_quality_score,
    enrollment_status,
    image_dimensions,
    processing_time_ms,
    user_agent,
    enrolled_by
  ) VALUES (
    p_student_id,
    p_name,
    p_email,
    p_image_url,
    p_faces_detected,
    p_face_detection_score,
    p_image_quality_score,
    enrollment_status,
    p_image_dimensions,
    p_processing_time_ms,
    p_user_agent,
    auth.uid()
  ) RETURNING id INTO attempt_id;
  
  -- If enrollment is successful, create or update student record
  IF enrollment_success THEN
    -- Check if student already exists
    SELECT EXISTS(SELECT 1 FROM students WHERE student_id = p_student_id) INTO student_exists;
    
    IF student_exists THEN
      -- Update existing student
      UPDATE students SET
        name = p_name,
        email = p_email,
        reference_image = p_image_url,
        face_detection_score = p_face_detection_score,
        enrollment_date = NOW(),
        enrollment_method = 'manual',
        face_enrollment_status = 'enrolled',
        manual_override_allowed = TRUE,
        enrollment_notes = 'Face enrolled via manual enrollment system',
        status = 'active',
        updated_at = NOW()
      WHERE student_id = p_student_id;
    ELSE
      -- Insert new student
      INSERT INTO students (
        student_id,
        name,
        email,
        reference_image,
        face_detection_score,
        enrollment_date,
        enrollment_method,
        face_enrollment_status,
        manual_override_allowed,
        enrollment_notes,
        status
      ) VALUES (
        p_student_id,
        p_name,
        p_email,
        p_image_url,
        p_face_detection_score,
        NOW(),
        'manual',
        'enrolled',
        TRUE,
        'Face enrolled via manual enrollment system',
        'active'
      );
    END IF;
    
    -- Additional success message
    result_message := result_message || ' Image uploaded. Data saved to Supabase.';
  END IF;
  
  -- Return comprehensive result
  RETURN jsonb_build_object(
    'success', enrollment_success,
    'enrollment_status', enrollment_status,
    'message', result_message,
    'attempt_id', attempt_id,
    'student_id', p_student_id,
    'name', p_name,
    'email', p_email,
    'image_url', p_image_url,
    'faces_detected', p_faces_detected,
    'face_detection_score', p_face_detection_score,
    'image_quality_score', p_image_quality_score,
    'processing_time_ms', p_processing_time_ms,
    'manual_override_allowed', TRUE
  );
END;
$$;

-- Function to get enrollment statistics
CREATE OR REPLACE FUNCTION get_enrollment_statistics(
  p_days_back INTEGER DEFAULT 30
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  stats JSONB;
BEGIN
  WITH enrollment_stats AS (
    SELECT 
      COUNT(*) as total_attempts,
      COUNT(CASE WHEN enrollment_status = 'success' THEN 1 END) as successful_enrollments,
      COUNT(CASE WHEN enrollment_status = 'no_face' THEN 1 END) as no_face_attempts,
      COUNT(CASE WHEN enrollment_status = 'multiple_faces' THEN 1 END) as multiple_faces_attempts,
      COUNT(CASE WHEN enrollment_status = 'poor_quality' THEN 1 END) as poor_quality_attempts,
      COUNT(CASE WHEN enrollment_status = 'upload_failed' THEN 1 END) as upload_failed_attempts,
      AVG(face_detection_score) as avg_detection_score,
      AVG(image_quality_score) as avg_quality_score,
      AVG(processing_time_ms) as avg_processing_time
    FROM face_enrollment_attempts
    WHERE created_at >= NOW() - INTERVAL '1 day' * p_days_back
  )
  SELECT jsonb_build_object(
    'total_attempts', total_attempts,
    'successful_enrollments', successful_enrollments,
    'no_face_attempts', no_face_attempts,
    'multiple_faces_attempts', multiple_faces_attempts,
    'poor_quality_attempts', poor_quality_attempts,
    'upload_failed_attempts', upload_failed_attempts,
    'success_rate', CASE 
      WHEN total_attempts > 0 THEN ROUND((successful_enrollments::FLOAT / total_attempts::FLOAT) * 100, 2)
      ELSE 0 
    END,
    'avg_detection_score', ROUND(avg_detection_score, 3),
    'avg_quality_score', ROUND(avg_quality_score, 3),
    'avg_processing_time_ms', ROUND(avg_processing_time)
  ) INTO stats
  FROM enrollment_stats;
  
  RETURN stats;
END;
$$;

-- Function to get student enrollment history
CREATE OR REPLACE FUNCTION get_student_enrollment_history(
  p_student_id TEXT
)
RETURNS TABLE (
  attempt_id UUID,
  name TEXT,
  email TEXT,
  image_url TEXT,
  faces_detected INTEGER,
  face_detection_score FLOAT,
  enrollment_status TEXT,
  error_message TEXT,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    fea.id,
    fea.name,
    fea.email,
    fea.image_url,
    fea.faces_detected,
    fea.face_detection_score,
    fea.enrollment_status,
    fea.error_message,
    fea.created_at
  FROM face_enrollment_attempts fea
  WHERE fea.student_id = p_student_id
  ORDER BY fea.created_at DESC;
END;
$$;

-- Function to enable manual override for failed enrollments
CREATE OR REPLACE FUNCTION enable_manual_override(
  p_student_id TEXT,
  p_image_url TEXT,
  p_override_reason TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  student_exists BOOLEAN;
BEGIN
  -- Check if student exists
  SELECT EXISTS(SELECT 1 FROM students WHERE student_id = p_student_id) INTO student_exists;
  
  IF student_exists THEN
    -- Update existing student with manual override
    UPDATE students SET
      reference_image = p_image_url,
      face_enrollment_status = 'enrolled',
      manual_override_allowed = TRUE,
      enrollment_notes = COALESCE(enrollment_notes, '') || ' | Manual override: ' || p_override_reason,
      updated_at = NOW()
    WHERE student_id = p_student_id;
  ELSE
    -- Cannot override non-existent student
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Student not found for manual override'
    );
  END IF;
  
  -- Log the manual override attempt
  INSERT INTO face_enrollment_attempts (
    student_id,
    name,
    email,
    image_url,
    faces_detected,
    enrollment_status,
    error_message,
    enrolled_by
  ) SELECT 
    s.student_id,
    s.name,
    s.email,
    p_image_url,
    -1, -- Special value indicating manual override
    'success',
    'Manual override: ' || p_override_reason,
    auth.uid()
  FROM students s
  WHERE s.student_id = p_student_id;
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'Manual override applied successfully',
    'student_id', p_student_id,
    'override_reason', p_override_reason
  );
END;
$$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_face_enrollment_attempts_student_id ON face_enrollment_attempts(student_id);
CREATE INDEX IF NOT EXISTS idx_face_enrollment_attempts_status ON face_enrollment_attempts(enrollment_status);
CREATE INDEX IF NOT EXISTS idx_face_enrollment_attempts_created_at ON face_enrollment_attempts(created_at);
CREATE INDEX IF NOT EXISTS idx_students_enrollment_status ON students(face_enrollment_status);
CREATE INDEX IF NOT EXISTS idx_students_enrollment_date ON students(enrollment_date);

-- Enable Row Level Security
ALTER TABLE face_enrollment_attempts ENABLE ROW LEVEL SECURITY;

-- RLS Policies for face enrollment attempts
CREATE POLICY "Face enrollment attempts read access" ON face_enrollment_attempts FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

CREATE POLICY "Face enrollment attempts insert" ON face_enrollment_attempts FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

-- Create view for enrollment dashboard
CREATE OR REPLACE VIEW enrollment_dashboard AS
SELECT 
  s.student_id,
  s.name,
  s.email,
  s.reference_image,
  s.face_detection_score,
  s.enrollment_date,
  s.face_enrollment_status,
  s.manual_override_allowed,
  s.enrollment_notes,
  COUNT(fea.id) as total_attempts,
  COUNT(CASE WHEN fea.enrollment_status = 'success' THEN 1 END) as successful_attempts,
  MAX(fea.created_at) as last_attempt_date,
  CASE 
    WHEN s.face_enrollment_status = 'enrolled' THEN 'Enrolled'
    WHEN COUNT(fea.id) = 0 THEN 'Not Attempted'
    ELSE 'Enrollment Failed'
  END as enrollment_display_status
FROM students s
LEFT JOIN face_enrollment_attempts fea ON s.student_id = fea.student_id
GROUP BY s.student_id, s.name, s.email, s.reference_image, s.face_detection_score, 
         s.enrollment_date, s.face_enrollment_status, s.manual_override_allowed, s.enrollment_notes
ORDER BY s.enrollment_date DESC NULLS LAST;

-- Insert sample enrollment data for testing
INSERT INTO face_enrollment_attempts (
  student_id, name, email, image_url, faces_detected, face_detection_score, 
  image_quality_score, enrollment_status, processing_time_ms
) VALUES
  ('E22273735500014', 'Anupam', '<EMAIL>', 'https://storage.supabase.co/student_faces/anupam.jpg', 1, 0.95, 0.92, 'success', 1250),
  ('E22273735500015', 'Prakhar', '<EMAIL>', 'https://storage.supabase.co/student_faces/prakhar.jpg', 1, 0.89, 0.87, 'success', 1180),
  ('E22273735500016', 'Aakash', '<EMAIL>', 'https://storage.supabase.co/student_faces/aakash.jpg', 1, 0.93, 0.91, 'success', 1320),
  ('E22273735500017', 'Test Multiple', '<EMAIL>', 'https://storage.supabase.co/student_faces/multiple.jpg', 2, 0.85, 0.80, 'multiple_faces', 980),
  ('E22273735500018', 'Test No Face', '<EMAIL>', 'https://storage.supabase.co/student_faces/noface.jpg', 0, 0.0, 0.45, 'no_face', 750)
ON CONFLICT DO NOTHING;
