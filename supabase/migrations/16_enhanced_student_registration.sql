-- Enhanced Student Registration System
-- Clean database with optimized schema and reliable face registration

-- 1. Data Cleanup & Fresh Start
-- Delete all test student entries
DELETE FROM students 
WHERE email IN (
  '<EMAIL>', 
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
) OR student_id LIKE 'TEST_%' OR student_id LIKE 'E_TEST_%';

-- Clean up test face data
DELETE FROM face_descriptors 
WHERE student_id IN (
  SELECT student_id FROM students 
  WHERE email LIKE '%<EMAIL>' OR student_id LIKE 'TEST_%'
);

-- Clean up test enrollment attempts
DELETE FROM face_enrollment_attempts 
WHERE student_id LIKE 'TEST_%' OR email LIKE '%<EMAIL>';

-- Clean up test verification results
DELETE FROM face_verification_results 
WHERE student_id LIKE 'TEST_%';

-- Clean up test registration metrics
DELETE FROM face_registration_metrics 
WHERE student_id LIKE 'TEST_%';

-- 2. Enhanced Students Table Schema
-- Drop existing table and recreate with enhanced structure
DROP TABLE IF EXISTS students_new;

CREATE TABLE students_new (
  id SERIAL PRIMARY KEY,
  first_name TEXT NOT NULL CHECK (length(first_name) >= 2),
  last_name TEXT NOT NULL CHECK (length(last_name) >= 2),
  email TEXT UNIQUE NOT NULL CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
  student_id TEXT UNIQUE NOT NULL CHECK (student_id ~* '^[A-Z]\d{12,14}$'),
  course TEXT NOT NULL,
  semester INTEGER NOT NULL CHECK (semester >= 1 AND semester <= 8),
  class TEXT NOT NULL,
  face_descriptor BYTEA,
  face_image_path TEXT,
  registration_status TEXT DEFAULT 'pending' CHECK (registration_status IN ('pending', 'completed', 'failed', 'requires_retry')),
  face_quality_score FLOAT CHECK (face_quality_score >= 0 AND face_quality_score <= 1),
  registration_attempts INTEGER DEFAULT 0,
  last_registration_attempt TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Migrate existing valid data
INSERT INTO students_new (
  first_name, last_name, email, student_id, course, semester, class,
  face_descriptor, face_image_path, registration_status, face_quality_score,
  created_at, updated_at
)
SELECT 
  COALESCE(name, 'Unknown') as first_name,
  '' as last_name,
  email,
  student_id,
  COALESCE(course, 'Computer Science') as course,
  COALESCE(semester, 1) as semester,
  COALESCE(class, 'Default') as class,
  NULL as face_descriptor,
  reference_image as face_image_path,
  CASE 
    WHEN face_enrollment_status = 'enrolled' THEN 'completed'
    ELSE 'pending'
  END as registration_status,
  face_detection_score as face_quality_score,
  created_at,
  updated_at
FROM students 
WHERE email NOT LIKE '%<EMAIL>' 
  AND student_id NOT LIKE 'TEST_%'
  AND email IS NOT NULL 
  AND student_id IS NOT NULL;

-- Replace old table
DROP TABLE students;
ALTER TABLE students_new RENAME TO students;

-- 3. Create courses and classes reference tables
CREATE TABLE IF NOT EXISTS courses (
  id SERIAL PRIMARY KEY,
  name TEXT UNIQUE NOT NULL,
  code TEXT UNIQUE NOT NULL,
  duration_semesters INTEGER DEFAULT 8,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS classes (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  course_id INTEGER REFERENCES courses(id),
  semester INTEGER NOT NULL,
  academic_year TEXT NOT NULL,
  capacity INTEGER DEFAULT 60,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(name, course_id, semester, academic_year)
);

-- Insert default courses
INSERT INTO courses (name, code, duration_semesters) VALUES
  ('Computer Science', 'CS', 8),
  ('Information Technology', 'IT', 8),
  ('Electronics Engineering', 'ECE', 8),
  ('Mechanical Engineering', 'ME', 8),
  ('Civil Engineering', 'CE', 8),
  ('Business Administration', 'MBA', 4),
  ('Data Science', 'DS', 6)
ON CONFLICT (code) DO NOTHING;

-- Insert default classes
INSERT INTO classes (name, course_id, semester, academic_year) 
SELECT 
  'Class ' || chr(64 + generate_series) as name,
  c.id as course_id,
  s.semester,
  '2024-25' as academic_year
FROM courses c
CROSS JOIN (SELECT generate_series(1, 8) as semester) s
CROSS JOIN (SELECT generate_series(1, 3)) -- 3 classes per semester
ON CONFLICT DO NOTHING;

-- 4. Enhanced face registration tracking
CREATE TABLE IF NOT EXISTS student_registration_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id TEXT NOT NULL,
  registration_step TEXT NOT NULL CHECK (registration_step IN ('form_submitted', 'camera_initialized', 'face_captured', 'face_processed', 'data_stored', 'completed', 'failed')),
  step_data JSONB,
  error_message TEXT,
  processing_time_ms INTEGER,
  user_agent TEXT,
  ip_address INET DEFAULT inet_client_addr(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. Function for enhanced student registration
CREATE OR REPLACE FUNCTION register_student_enhanced(
  p_first_name TEXT,
  p_last_name TEXT,
  p_email TEXT,
  p_student_id TEXT,
  p_course TEXT,
  p_semester INTEGER,
  p_class TEXT,
  p_face_descriptor BYTEA DEFAULT NULL,
  p_face_image_path TEXT DEFAULT NULL,
  p_face_quality_score FLOAT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  student_exists BOOLEAN;
  new_student_id INTEGER;
  result JSONB;
BEGIN
  -- Validate input data
  IF p_first_name IS NULL OR length(trim(p_first_name)) < 2 THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'First name must be at least 2 characters long'
    );
  END IF;
  
  IF p_student_id !~ '^[A-Z]\d{12,14}$' THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Student ID must follow format: Letter followed by 12-14 digits (e.g., E22273735500014)'
    );
  END IF;
  
  IF p_email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Please provide a valid email address'
    );
  END IF;
  
  -- Check if student already exists
  SELECT EXISTS(
    SELECT 1 FROM students 
    WHERE student_id = p_student_id OR email = p_email
  ) INTO student_exists;
  
  IF student_exists THEN
    -- Update existing student
    UPDATE students SET
      first_name = p_first_name,
      last_name = p_last_name,
      email = p_email,
      course = p_course,
      semester = p_semester,
      class = p_class,
      face_descriptor = COALESCE(p_face_descriptor, face_descriptor),
      face_image_path = COALESCE(p_face_image_path, face_image_path),
      face_quality_score = COALESCE(p_face_quality_score, face_quality_score),
      registration_status = CASE 
        WHEN p_face_descriptor IS NOT NULL THEN 'completed'
        ELSE registration_status
      END,
      registration_attempts = registration_attempts + 1,
      last_registration_attempt = NOW(),
      updated_at = NOW()
    WHERE student_id = p_student_id
    RETURNING id INTO new_student_id;
  ELSE
    -- Insert new student
    INSERT INTO students (
      first_name, last_name, email, student_id, course, semester, class,
      face_descriptor, face_image_path, face_quality_score,
      registration_status, registration_attempts, last_registration_attempt
    ) VALUES (
      p_first_name, p_last_name, p_email, p_student_id, p_course, p_semester, p_class,
      p_face_descriptor, p_face_image_path, p_face_quality_score,
      CASE WHEN p_face_descriptor IS NOT NULL THEN 'completed' ELSE 'pending' END,
      1, NOW()
    ) RETURNING id INTO new_student_id;
  END IF;
  
  -- Log registration step
  INSERT INTO student_registration_log (
    student_id, registration_step, step_data
  ) VALUES (
    p_student_id, 
    CASE WHEN p_face_descriptor IS NOT NULL THEN 'completed' ELSE 'form_submitted' END,
    jsonb_build_object(
      'student_id', new_student_id,
      'has_face_data', p_face_descriptor IS NOT NULL,
      'quality_score', p_face_quality_score
    )
  );
  
  -- Build success response
  result := jsonb_build_object(
    'success', true,
    'message', 'Student registered successfully',
    'student_id', p_student_id,
    'database_id', new_student_id,
    'registration_status', CASE WHEN p_face_descriptor IS NOT NULL THEN 'completed' ELSE 'pending' END,
    'requires_face_capture', p_face_descriptor IS NULL
  );
  
  RETURN result;
  
EXCEPTION
  WHEN unique_violation THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Student ID or email already exists'
    );
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$$;

-- 6. Function to get courses and classes
CREATE OR REPLACE FUNCTION get_courses_and_classes()
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  result JSONB;
BEGIN
  WITH course_data AS (
    SELECT 
      c.id,
      c.name,
      c.code,
      c.duration_semesters,
      json_agg(
        json_build_object(
          'id', cl.id,
          'name', cl.name,
          'semester', cl.semester,
          'academic_year', cl.academic_year,
          'capacity', cl.capacity
        ) ORDER BY cl.semester, cl.name
      ) as classes
    FROM courses c
    LEFT JOIN classes cl ON c.id = cl.course_id
    GROUP BY c.id, c.name, c.code, c.duration_semesters
  )
  SELECT json_agg(
    json_build_object(
      'id', id,
      'name', name,
      'code', code,
      'duration_semesters', duration_semesters,
      'classes', classes
    ) ORDER BY name
  ) INTO result
  FROM course_data;
  
  RETURN jsonb_build_object(
    'success', true,
    'courses', result
  );
END;
$$;

-- 7. Function to log registration steps
CREATE OR REPLACE FUNCTION log_registration_step(
  p_student_id TEXT,
  p_step TEXT,
  p_step_data JSONB DEFAULT NULL,
  p_error_message TEXT DEFAULT NULL,
  p_processing_time_ms INTEGER DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
  log_id UUID;
BEGIN
  INSERT INTO student_registration_log (
    student_id, registration_step, step_data, error_message, processing_time_ms, user_agent
  ) VALUES (
    p_student_id, p_step, p_step_data, p_error_message, p_processing_time_ms,
    current_setting('request.headers', true)::json->>'user-agent'
  ) RETURNING id INTO log_id;
  
  RETURN log_id;
END;
$$;

-- 8. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_students_student_id ON students(student_id);
CREATE INDEX IF NOT EXISTS idx_students_email ON students(email);
CREATE INDEX IF NOT EXISTS idx_students_registration_status ON students(registration_status);
CREATE INDEX IF NOT EXISTS idx_students_course_semester ON students(course, semester);
CREATE INDEX IF NOT EXISTS idx_registration_log_student_id ON student_registration_log(student_id);
CREATE INDEX IF NOT EXISTS idx_registration_log_step ON student_registration_log(registration_step);
CREATE INDEX IF NOT EXISTS idx_registration_log_created_at ON student_registration_log(created_at);

-- 9. Enable Row Level Security
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_registration_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Students read access" ON students FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

CREATE POLICY "Students write access" ON students FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

-- Public read access for courses and classes
CREATE POLICY "Courses public read" ON courses FOR SELECT USING (true);
CREATE POLICY "Classes public read" ON classes FOR SELECT USING (true);

-- Registration log access
CREATE POLICY "Registration log access" ON student_registration_log FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

-- 10. Insert provided student data (Anupam)
INSERT INTO students (
  first_name, last_name, email, student_id, course, semester, class, registration_status
) VALUES (
  'Anupam', '', '<EMAIL>', 'E22273735500014', 'Computer Science', 6, 'Class A', 'pending'
) ON CONFLICT (student_id) DO UPDATE SET
  first_name = EXCLUDED.first_name,
  email = EXCLUDED.email,
  course = EXCLUDED.course,
  semester = EXCLUDED.semester,
  updated_at = NOW();

-- Reset sequence to start from 1000
SELECT setval('students_id_seq', 1000, false);
