-- Attendance System Database Schema
-- Comprehensive attendance tracking with facial recognition support

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
-- Note: pgvector extension would need to be enabled in Supabase dashboard

-- Create students table with enhanced fields
CREATE TABLE IF NOT EXISTS students (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  student_id TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE,
  phone TEXT,
  class_id UUID REFERENCES classes(id),
  photo_url TEXT, -- URL to stored photo in Supabase Storage
  face_embedding FLOAT[], -- Face embedding vector (512 dimensions)
  enrollment_date DATE DEFAULT CURRENT_DATE,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'graduated')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create attendance table with comprehensive tracking
CREATE TABLE IF NOT EXISTS attendance (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id TEXT NOT NULL REFERENCES students(student_id) ON DELETE CASCADE,
  class_id UUID REFERENCES classes(id),
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  time_in TIMESTAMPTZ,
  time_out TIMESTAMPTZ,
  status TEXT NOT NULL DEFAULT 'present' CHECK (status IN ('present', 'absent', 'late', 'excused')),
  verification_method TEXT DEFAULT 'manual' CHECK (verification_method IN ('face_recognition', 'manual', 'qr_code', 'rfid')),
  confidence FLOAT CHECK (confidence >= 0 AND confidence <= 1),
  location_lat FLOAT,
  location_lng FLOAT,
  device_info JSONB,
  notes TEXT,
  marked_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure one attendance record per student per day per class
  UNIQUE(student_id, date, class_id)
);

-- Create attendance sessions for real-time tracking
CREATE TABLE IF NOT EXISTS attendance_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  class_id UUID NOT NULL REFERENCES classes(id),
  session_name TEXT NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT TRUE,
  attendance_window_minutes INTEGER DEFAULT 15, -- Grace period for late arrivals
  location_required BOOLEAN DEFAULT FALSE,
  geofence_radius_meters INTEGER DEFAULT 100,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create face recognition logs for debugging and analytics
CREATE TABLE IF NOT EXISTS face_recognition_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id TEXT REFERENCES students(student_id),
  session_id UUID REFERENCES attendance_sessions(id),
  confidence_score FLOAT,
  recognition_status TEXT CHECK (recognition_status IN ('success', 'failed', 'low_confidence')),
  image_url TEXT, -- URL to captured image for verification
  processing_time_ms INTEGER,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create attendance statistics view
CREATE OR REPLACE VIEW attendance_statistics AS
SELECT 
  s.id as student_id,
  s.name as student_name,
  s.student_id as student_number,
  c.name as class_name,
  COUNT(a.id) as total_sessions,
  COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
  COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
  COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
  ROUND(
    COUNT(CASE WHEN a.status = 'present' THEN 1 END) * 100.0 / 
    NULLIF(COUNT(a.id), 0), 2
  ) as attendance_rate,
  MAX(a.date) as last_attendance_date
FROM students s
LEFT JOIN classes c ON s.class_id = c.id
LEFT JOIN attendance a ON s.student_id = a.student_id
GROUP BY s.id, s.name, s.student_id, c.name;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_students_student_id ON students(student_id);
CREATE INDEX IF NOT EXISTS idx_students_class_id ON students(class_id);
CREATE INDEX IF NOT EXISTS idx_students_status ON students(status);
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(date);
CREATE INDEX IF NOT EXISTS idx_attendance_class_id ON attendance(class_id);
CREATE INDEX IF NOT EXISTS idx_attendance_status ON attendance(status);
CREATE INDEX IF NOT EXISTS idx_attendance_created_at ON attendance(created_at);
CREATE INDEX IF NOT EXISTS idx_face_logs_student_id ON face_recognition_logs(student_id);
CREATE INDEX IF NOT EXISTS idx_face_logs_created_at ON face_recognition_logs(created_at);

-- Function to calculate attendance rate
CREATE OR REPLACE FUNCTION calculate_attendance_rate(
  p_student_id TEXT,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL
)
RETURNS FLOAT
LANGUAGE plpgsql
AS $$
DECLARE
  total_sessions INTEGER;
  present_sessions INTEGER;
  attendance_rate FLOAT;
BEGIN
  -- Set default date range if not provided
  IF p_start_date IS NULL THEN
    p_start_date := CURRENT_DATE - INTERVAL '30 days';
  END IF;
  
  IF p_end_date IS NULL THEN
    p_end_date := CURRENT_DATE;
  END IF;
  
  -- Count total sessions
  SELECT COUNT(*) INTO total_sessions
  FROM attendance
  WHERE student_id = p_student_id
    AND date BETWEEN p_start_date AND p_end_date;
  
  -- Count present sessions
  SELECT COUNT(*) INTO present_sessions
  FROM attendance
  WHERE student_id = p_student_id
    AND date BETWEEN p_start_date AND p_end_date
    AND status IN ('present', 'late');
  
  -- Calculate rate
  IF total_sessions > 0 THEN
    attendance_rate := (present_sessions::FLOAT / total_sessions::FLOAT) * 100;
  ELSE
    attendance_rate := 0;
  END IF;
  
  RETURN ROUND(attendance_rate, 2);
END;
$$;

-- Function to mark attendance
CREATE OR REPLACE FUNCTION mark_attendance(
  p_student_id TEXT,
  p_class_id UUID,
  p_status TEXT DEFAULT 'present',
  p_verification_method TEXT DEFAULT 'manual',
  p_confidence FLOAT DEFAULT NULL,
  p_location_lat FLOAT DEFAULT NULL,
  p_location_lng FLOAT DEFAULT NULL,
  p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
  attendance_id UUID;
  current_time TIMESTAMPTZ := NOW();
BEGIN
  -- Insert or update attendance record
  INSERT INTO attendance (
    student_id,
    class_id,
    date,
    time_in,
    status,
    verification_method,
    confidence,
    location_lat,
    location_lng,
    notes
  ) VALUES (
    p_student_id,
    p_class_id,
    CURRENT_DATE,
    current_time,
    p_status,
    p_verification_method,
    p_confidence,
    p_location_lat,
    p_location_lng,
    p_notes
  )
  ON CONFLICT (student_id, date, class_id)
  DO UPDATE SET
    time_in = CASE WHEN attendance.time_in IS NULL THEN current_time ELSE attendance.time_in END,
    time_out = CASE WHEN p_status = 'present' AND attendance.status = 'present' THEN current_time ELSE attendance.time_out END,
    status = p_status,
    verification_method = p_verification_method,
    confidence = p_confidence,
    location_lat = p_location_lat,
    location_lng = p_location_lng,
    notes = COALESCE(p_notes, attendance.notes),
    updated_at = current_time
  RETURNING id INTO attendance_id;
  
  RETURN attendance_id;
END;
$$;

-- Function to get attendance summary
CREATE OR REPLACE FUNCTION get_attendance_summary(
  p_class_id UUID DEFAULT NULL,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
  student_id TEXT,
  student_name TEXT,
  total_days INTEGER,
  present_days INTEGER,
  absent_days INTEGER,
  late_days INTEGER,
  attendance_rate FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  -- Set default date range
  IF p_start_date IS NULL THEN
    p_start_date := CURRENT_DATE - INTERVAL '30 days';
  END IF;
  
  IF p_end_date IS NULL THEN
    p_end_date := CURRENT_DATE;
  END IF;
  
  RETURN QUERY
  SELECT 
    s.student_id,
    s.name as student_name,
    COUNT(a.id)::INTEGER as total_days,
    COUNT(CASE WHEN a.status = 'present' THEN 1 END)::INTEGER as present_days,
    COUNT(CASE WHEN a.status = 'absent' THEN 1 END)::INTEGER as absent_days,
    COUNT(CASE WHEN a.status = 'late' THEN 1 END)::INTEGER as late_days,
    ROUND(
      COUNT(CASE WHEN a.status IN ('present', 'late') THEN 1 END) * 100.0 / 
      NULLIF(COUNT(a.id), 0), 2
    ) as attendance_rate
  FROM students s
  LEFT JOIN attendance a ON s.student_id = a.student_id
    AND a.date BETWEEN p_start_date AND p_end_date
    AND (p_class_id IS NULL OR a.class_id = p_class_id)
  WHERE (p_class_id IS NULL OR s.class_id = p_class_id)
    AND s.status = 'active'
  GROUP BY s.student_id, s.name
  ORDER BY s.name;
END;
$$;

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_students_updated_at
BEFORE UPDATE ON students
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_attendance_updated_at
BEFORE UPDATE ON attendance
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE face_recognition_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for students
CREATE POLICY "Students read access" ON students FOR SELECT USING (true);
CREATE POLICY "Admin students write" ON students FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

-- RLS Policies for attendance
CREATE POLICY "Attendance read access" ON attendance FOR SELECT USING (true);
CREATE POLICY "Attendance insert" ON attendance FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM students WHERE student_id = attendance.student_id)
);
CREATE POLICY "Admin attendance write" ON attendance FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

-- RLS Policies for attendance sessions
CREATE POLICY "Sessions read access" ON attendance_sessions FOR SELECT USING (true);
CREATE POLICY "Admin sessions write" ON attendance_sessions FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

-- RLS Policies for face recognition logs
CREATE POLICY "Face logs read access" ON face_recognition_logs FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);
CREATE POLICY "Face logs insert" ON face_recognition_logs FOR INSERT WITH CHECK (true);
