-- Enable pgvector extension for face embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Update students table with face embedding support
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS face_embedding vector(128),
ADD COLUMN IF NOT EXISTS face_quality FLOAT,
ADD COLUMN IF NOT EXISTS last_verification TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS verification_count INTEGER DEFAULT 0;

-- Create index for faster similarity search
CREATE INDEX IF NOT EXISTS idx_students_face_embedding 
ON students USING ivfflat (face_embedding vector_l2_ops)
WITH (lists = 100);

-- Create function to find similar faces
CREATE OR REPLACE FUNCTION find_similar_faces(
  search_embedding vector(128),
  similarity_threshold FLOAT DEFAULT 0.6,
  max_results INT DEFAULT 5
)
RETURNS TABLE (
  student_id UUID,
  name TEXT,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id as student_id,
    s.first_name || ' ' || s.last_name as name,
    1 - (s.face_embedding <=> search_embedding) as similarity
  FROM 
    students s
  WHERE 
    s.face_embedding IS NOT NULL
    AND 1 - (s.face_embedding <=> search_embedding) > similarity_threshold
  ORDER BY 
    similarity DESC
  LIMIT 
    max_results;
END;
$$;

-- Create table for face verification logs
CREATE TABLE IF NOT EXISTS face_verification_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID REFERENCES students(id) ON DELETE CASCADE,
  verified BOOLEAN NOT NULL,
  confidence FLOAT,
  device_info JSONB,
  ip_address TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_face_verification_logs_student_id 
ON face_verification_logs(student_id);

-- Enable RLS on face_verification_logs
ALTER TABLE face_verification_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for face_verification_logs
CREATE POLICY "Admins can view all verification logs"
ON face_verification_logs FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
  )
);

-- Create policy for students to view their own logs
CREATE POLICY "Students can view their own verification logs"
ON face_verification_logs FOR SELECT
USING (
  student_id = (
    SELECT id FROM students WHERE auth_id = auth.uid()
  )
);
