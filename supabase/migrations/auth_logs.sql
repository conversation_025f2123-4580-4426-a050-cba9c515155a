-- Auth logs table for tracking authentication events
CREATE TABLE IF NOT EXISTS auth_logs (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  event TEXT NOT NULL,
  status TEXT NOT NULL,
  ip_address INET,
  metadata JSONB DEFAULT '{}'::jsonb,
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Index for faster queries on user_id
CREATE INDEX IF NOT EXISTS auth_logs_user_id_idx ON auth_logs(user_id);

-- Index for faster queries on event type
CREATE INDEX IF NOT EXISTS auth_logs_event_idx ON auth_logs(event);

-- Index for faster queries on status
CREATE INDEX IF NOT EXISTS auth_logs_status_idx ON auth_logs(status);

-- Index for faster timestamp range queries
CREATE INDEX IF NOT EXISTS auth_logs_timestamp_idx ON auth_logs(timestamp);

-- Function to check if a user is rate limited
CREATE OR REPLACE FUNCTION is_rate_limited(p_email TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  failed_attempts INTEGER;
BEGIN
  -- Count failed login attempts in the last 15 minutes
  SELECT COUNT(*) INTO failed_attempts
  FROM auth_logs
  WHERE 
    status = 'failed' 
    AND event = 'login'
    AND timestamp > NOW() - INTERVAL '15 minutes'
    AND metadata::jsonb->>'email' = p_email;
  
  -- Rate limit after 5 failed attempts
  RETURN failed_attempts >= 5;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies
ALTER TABLE auth_logs ENABLE ROW LEVEL SECURITY;

-- Only admins can see all logs
CREATE POLICY admin_all_access ON auth_logs
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.role_id
      WHERE users.id = auth.uid() AND user_roles.role_name = 'admin'
    )
  );

-- Users can only see their own logs
CREATE POLICY user_own_logs ON auth_logs
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- Comment explaining the security model
COMMENT ON TABLE auth_logs IS 'Tracks authentication events with rate limiting support. Admins can see all logs, users can only see their own.';
