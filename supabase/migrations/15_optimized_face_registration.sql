-- Optimized Face Registration Pipeline
-- High-performance database operations for <1s face registration

-- Create optimized face registry storage bucket (configure in Supabase dashboard)
-- Bucket name: 'face-registry'
-- Public: false
-- File size limit: 5MB
-- Allowed MIME types: image/webp, image/jpeg, image/png

-- Create face descriptors table for optimized storage
CREATE TABLE IF NOT EXISTS face_descriptors (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id TEXT NOT NULL REFERENCES students(student_id) ON DELETE CASCADE,
  descriptor FLOAT8[] NOT NULL, -- 128-dimensional face descriptor
  detection_score FLOAT NOT NULL CHECK (detection_score >= 0 AND detection_score <= 1),
  quality_score FLOAT NOT NULL CHECK (quality_score >= 0 AND quality_score <= 1),
  face_box JSONB NOT NULL, -- Bounding box coordinates
  processing_time_ms INTEGER,
  registration_method TEXT DEFAULT 'optimized',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create performance metrics table
CREATE TABLE IF NOT EXISTS face_registration_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id TEXT NOT NULL,
  total_time_ms INTEGER NOT NULL,
  compression_time_ms INTEGER,
  detection_time_ms INTEGER,
  upload_time_ms INTEGER,
  database_time_ms INTEGER,
  target_achieved BOOLEAN NOT NULL,
  compression_ratio FLOAT,
  original_size_bytes INTEGER,
  compressed_size_bytes INTEGER,
  detection_method TEXT,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Optimized function for atomic face registration
CREATE OR REPLACE FUNCTION register_face_optimized(
  p_student_id TEXT,
  p_name TEXT,
  p_email TEXT,
  p_face_descriptor FLOAT8[],
  p_detection_score FLOAT,
  p_quality_score FLOAT,
  p_image_url TEXT,
  p_face_box JSONB,
  p_processing_time INTEGER DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  student_exists BOOLEAN;
  descriptor_id UUID;
  result JSONB;
BEGIN
  -- Start transaction for atomic operations
  
  -- Check if student exists
  SELECT EXISTS(SELECT 1 FROM students WHERE student_id = p_student_id) INTO student_exists;
  
  IF student_exists THEN
    -- Update existing student
    UPDATE students SET
      name = p_name,
      email = p_email,
      reference_image = p_image_url,
      face_detection_score = p_detection_score,
      face_enrollment_status = 'enrolled',
      updated_at = NOW()
    WHERE student_id = p_student_id;
    
    -- Update or insert face descriptor
    INSERT INTO face_descriptors (
      student_id,
      descriptor,
      detection_score,
      quality_score,
      face_box,
      processing_time_ms
    ) VALUES (
      p_student_id,
      p_face_descriptor,
      p_detection_score,
      p_quality_score,
      p_face_box,
      p_processing_time
    )
    ON CONFLICT (student_id) 
    DO UPDATE SET
      descriptor = EXCLUDED.descriptor,
      detection_score = EXCLUDED.detection_score,
      quality_score = EXCLUDED.quality_score,
      face_box = EXCLUDED.face_box,
      processing_time_ms = EXCLUDED.processing_time_ms,
      updated_at = NOW()
    RETURNING id INTO descriptor_id;
  ELSE
    -- Insert new student
    INSERT INTO students (
      student_id,
      name,
      email,
      reference_image,
      face_detection_score,
      face_enrollment_status,
      enrollment_method,
      status
    ) VALUES (
      p_student_id,
      p_name,
      p_email,
      p_image_url,
      p_detection_score,
      'enrolled',
      'optimized',
      'active'
    );
    
    -- Insert face descriptor
    INSERT INTO face_descriptors (
      student_id,
      descriptor,
      detection_score,
      quality_score,
      face_box,
      processing_time_ms
    ) VALUES (
      p_student_id,
      p_face_descriptor,
      p_detection_score,
      p_quality_score,
      p_face_box,
      p_processing_time
    ) RETURNING id INTO descriptor_id;
  END IF;
  
  -- Build result
  result := jsonb_build_object(
    'success', true,
    'student_id', p_student_id,
    'descriptor_id', descriptor_id,
    'detection_score', p_detection_score,
    'quality_score', p_quality_score,
    'processing_time_ms', p_processing_time,
    'timestamp', NOW()
  );
  
  RETURN result;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Return error information
    RETURN jsonb_build_object(
      'success', false,
      'error', SQLERRM,
      'error_code', SQLSTATE
    );
END;
$$;

-- Function to log performance metrics
CREATE OR REPLACE FUNCTION log_registration_metrics(
  p_student_id TEXT,
  p_total_time_ms INTEGER,
  p_compression_time_ms INTEGER DEFAULT NULL,
  p_detection_time_ms INTEGER DEFAULT NULL,
  p_upload_time_ms INTEGER DEFAULT NULL,
  p_database_time_ms INTEGER DEFAULT NULL,
  p_target_achieved BOOLEAN DEFAULT false,
  p_compression_ratio FLOAT DEFAULT NULL,
  p_original_size_bytes INTEGER DEFAULT NULL,
  p_compressed_size_bytes INTEGER DEFAULT NULL,
  p_detection_method TEXT DEFAULT NULL,
  p_error_message TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
  metric_id UUID;
BEGIN
  INSERT INTO face_registration_metrics (
    student_id,
    total_time_ms,
    compression_time_ms,
    detection_time_ms,
    upload_time_ms,
    database_time_ms,
    target_achieved,
    compression_ratio,
    original_size_bytes,
    compressed_size_bytes,
    detection_method,
    error_message
  ) VALUES (
    p_student_id,
    p_total_time_ms,
    p_compression_time_ms,
    p_detection_time_ms,
    p_upload_time_ms,
    p_database_time_ms,
    p_target_achieved,
    p_compression_ratio,
    p_original_size_bytes,
    p_compressed_size_bytes,
    p_detection_method,
    p_error_message
  ) RETURNING id INTO metric_id;
  
  RETURN metric_id;
END;
$$;

-- Function to get performance analytics
CREATE OR REPLACE FUNCTION get_performance_analytics(
  p_days_back INTEGER DEFAULT 7
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  analytics JSONB;
BEGIN
  WITH performance_stats AS (
    SELECT 
      COUNT(*) as total_registrations,
      COUNT(CASE WHEN target_achieved THEN 1 END) as successful_fast_registrations,
      AVG(total_time_ms) as avg_total_time,
      PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY total_time_ms) as median_total_time,
      PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY total_time_ms) as p95_total_time,
      AVG(compression_time_ms) as avg_compression_time,
      AVG(detection_time_ms) as avg_detection_time,
      AVG(upload_time_ms) as avg_upload_time,
      AVG(database_time_ms) as avg_database_time,
      AVG(compression_ratio) as avg_compression_ratio,
      COUNT(CASE WHEN detection_method = 'worker' THEN 1 END) as worker_detections,
      COUNT(CASE WHEN detection_method = 'fallback' THEN 1 END) as fallback_detections,
      COUNT(CASE WHEN error_message IS NOT NULL THEN 1 END) as error_count
    FROM face_registration_metrics
    WHERE created_at >= NOW() - INTERVAL '1 day' * p_days_back
  )
  SELECT jsonb_build_object(
    'total_registrations', total_registrations,
    'success_rate', CASE 
      WHEN total_registrations > 0 THEN ROUND((successful_fast_registrations::FLOAT / total_registrations::FLOAT) * 100, 2)
      ELSE 0 
    END,
    'performance', jsonb_build_object(
      'avg_total_time_ms', ROUND(avg_total_time),
      'median_total_time_ms', ROUND(median_total_time),
      'p95_total_time_ms', ROUND(p95_total_time),
      'target_achievement_rate', CASE 
        WHEN total_registrations > 0 THEN ROUND((successful_fast_registrations::FLOAT / total_registrations::FLOAT) * 100, 2)
        ELSE 0 
      END
    ),
    'breakdown', jsonb_build_object(
      'avg_compression_time_ms', ROUND(avg_compression_time),
      'avg_detection_time_ms', ROUND(avg_detection_time),
      'avg_upload_time_ms', ROUND(avg_upload_time),
      'avg_database_time_ms', ROUND(avg_database_time)
    ),
    'optimization', jsonb_build_object(
      'avg_compression_ratio', ROUND(avg_compression_ratio, 2),
      'worker_usage_rate', CASE 
        WHEN (worker_detections + fallback_detections) > 0 
        THEN ROUND((worker_detections::FLOAT / (worker_detections + fallback_detections)::FLOAT) * 100, 2)
        ELSE 0 
      END
    ),
    'reliability', jsonb_build_object(
      'error_rate', CASE 
        WHEN total_registrations > 0 THEN ROUND((error_count::FLOAT / total_registrations::FLOAT) * 100, 2)
        ELSE 0 
      END,
      'error_count', error_count
    )
  ) INTO analytics
  FROM performance_stats;
  
  RETURN analytics;
END;
$$;

-- Function for face similarity search (optimized)
CREATE OR REPLACE FUNCTION find_similar_faces(
  p_query_descriptor FLOAT8[],
  p_similarity_threshold FLOAT DEFAULT 0.6,
  p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
  student_id TEXT,
  similarity_score FLOAT,
  detection_score FLOAT,
  quality_score FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    fd.student_id,
    1.0 - (fd.descriptor <-> p_query_descriptor) as similarity_score,
    fd.detection_score,
    fd.quality_score
  FROM face_descriptors fd
  WHERE 1.0 - (fd.descriptor <-> p_query_descriptor) >= p_similarity_threshold
  ORDER BY fd.descriptor <-> p_query_descriptor
  LIMIT p_limit;
END;
$$;

-- Create indexes for optimal performance
CREATE INDEX IF NOT EXISTS idx_face_descriptors_student_id ON face_descriptors(student_id);
CREATE INDEX IF NOT EXISTS idx_face_descriptors_created_at ON face_descriptors(created_at);
CREATE INDEX IF NOT EXISTS idx_face_descriptors_quality ON face_descriptors(quality_score DESC);

-- Vector similarity index for face descriptors (requires pgvector extension)
-- CREATE INDEX IF NOT EXISTS idx_face_descriptors_vector ON face_descriptors USING ivfflat (descriptor vector_cosine_ops);

CREATE INDEX IF NOT EXISTS idx_registration_metrics_student_id ON face_registration_metrics(student_id);
CREATE INDEX IF NOT EXISTS idx_registration_metrics_created_at ON face_registration_metrics(created_at);
CREATE INDEX IF NOT EXISTS idx_registration_metrics_performance ON face_registration_metrics(target_achieved, total_time_ms);

-- Add unique constraint for one descriptor per student
ALTER TABLE face_descriptors ADD CONSTRAINT unique_student_descriptor UNIQUE (student_id);

-- Enable Row Level Security
ALTER TABLE face_descriptors ENABLE ROW LEVEL SECURITY;
ALTER TABLE face_registration_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for face descriptors
CREATE POLICY "Face descriptors read access" ON face_descriptors FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

CREATE POLICY "Face descriptors write access" ON face_descriptors FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

-- RLS Policies for performance metrics
CREATE POLICY "Performance metrics access" ON face_registration_metrics FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

-- Create view for registration dashboard
CREATE OR REPLACE VIEW optimized_registration_dashboard AS
SELECT 
  s.student_id,
  s.name,
  s.email,
  s.reference_image,
  s.face_detection_score,
  s.face_enrollment_status,
  fd.quality_score,
  fd.processing_time_ms,
  fd.registration_method,
  fd.created_at as registration_date,
  CASE 
    WHEN fd.quality_score >= 0.9 THEN 'Excellent'
    WHEN fd.quality_score >= 0.8 THEN 'Good'
    WHEN fd.quality_score >= 0.7 THEN 'Fair'
    ELSE 'Poor'
  END as quality_rating
FROM students s
LEFT JOIN face_descriptors fd ON s.student_id = fd.student_id
WHERE s.face_enrollment_status = 'enrolled'
ORDER BY fd.created_at DESC;

-- Insert sample performance data for testing
INSERT INTO face_registration_metrics (
  student_id, total_time_ms, compression_time_ms, detection_time_ms, 
  upload_time_ms, database_time_ms, target_achieved, compression_ratio,
  original_size_bytes, compressed_size_bytes, detection_method
) VALUES
  ('E22273735500014', 850, 150, 400, 200, 100, true, 75.5, 2048000, 512000, 'worker'),
  ('E22273735500015', 920, 180, 450, 190, 100, true, 78.2, 1843200, 401000, 'worker'),
  ('E22273735500016', 1150, 200, 600, 250, 100, false, 72.1, 2304000, 643000, 'fallback'),
  ('E22273735500017', 780, 120, 350, 210, 100, true, 80.3, 1920000, 378000, 'worker'),
  ('E22273735500018', 1350, 250, 700, 300, 100, false, 65.8, 2560000, 876000, 'fallback')
ON CONFLICT DO NOTHING;
