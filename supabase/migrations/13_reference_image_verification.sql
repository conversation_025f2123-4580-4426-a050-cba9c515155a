-- Reference Image Verification System
-- Enhanced schema for storing and verifying student reference images

-- Add reference image fields to students table
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS reference_image TEXT,
ADD COLUMN IF NOT EXISTS reference_image_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS face_detection_score FLOAT,
ADD COLUMN IF NOT EXISTS image_upload_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected', 'needs_retake'));

-- Create face verification results table
CREATE TABLE IF NOT EXISTS face_verification_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id TEXT NOT NULL REFERENCES students(student_id),
  image_url TEXT NOT NULL,
  faces_detected INTEGER NOT NULL,
  face_detection_score FLOAT,
  image_quality_score FLOAT,
  verification_status TEXT NOT NULL CHECK (verification_status IN ('valid', 'no_face', 'multiple_faces', 'poor_quality', 'error')),
  error_message TEXT,
  image_dimensions JSONB,
  processing_time_ms INTEGER,
  verified_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Function to verify and store student face image
CREATE OR REPLACE FUNCTION verify_and_store_face_image(
  p_student_id TEXT,
  p_name TEXT,
  p_email TEXT,
  p_image_url TEXT,
  p_faces_detected INTEGER,
  p_face_detection_score FLOAT DEFAULT NULL,
  p_image_quality_score FLOAT DEFAULT NULL,
  p_image_dimensions JSONB DEFAULT NULL,
  p_processing_time_ms INTEGER DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  verification_status TEXT;
  result_message TEXT;
  verification_id UUID;
  student_exists BOOLEAN;
BEGIN
  -- Determine verification status based on face detection
  IF p_faces_detected = 0 THEN
    verification_status := 'no_face';
    result_message := 'No face found, please try again.';
  ELSIF p_faces_detected > 1 THEN
    verification_status := 'multiple_faces';
    result_message := 'Multiple faces detected, please upload a photo with only one face.';
  ELSIF p_face_detection_score IS NOT NULL AND p_face_detection_score < 0.8 THEN
    verification_status := 'poor_quality';
    result_message := 'Face detection quality too low, please try again with better lighting.';
  ELSE
    verification_status := 'valid';
    result_message := 'Student face successfully verified and stored.';
  END IF;
  
  -- Log verification attempt
  INSERT INTO face_verification_results (
    student_id,
    image_url,
    faces_detected,
    face_detection_score,
    image_quality_score,
    verification_status,
    image_dimensions,
    processing_time_ms,
    verified_by
  ) VALUES (
    p_student_id,
    p_image_url,
    p_faces_detected,
    p_face_detection_score,
    p_image_quality_score,
    verification_status,
    p_image_dimensions,
    p_processing_time_ms,
    auth.uid()
  ) RETURNING id INTO verification_id;
  
  -- If verification is valid, update or insert student record
  IF verification_status = 'valid' THEN
    -- Check if student exists
    SELECT EXISTS(SELECT 1 FROM students WHERE student_id = p_student_id) INTO student_exists;
    
    IF student_exists THEN
      -- Update existing student
      UPDATE students SET
        name = p_name,
        email = p_email,
        reference_image = p_image_url,
        reference_image_verified = TRUE,
        face_detection_score = p_face_detection_score,
        image_upload_date = NOW(),
        verification_status = 'verified',
        updated_at = NOW()
      WHERE student_id = p_student_id;
    ELSE
      -- Insert new student
      INSERT INTO students (
        student_id,
        name,
        email,
        reference_image,
        reference_image_verified,
        face_detection_score,
        image_upload_date,
        verification_status,
        status
      ) VALUES (
        p_student_id,
        p_name,
        p_email,
        p_image_url,
        TRUE,
        p_face_detection_score,
        NOW(),
        'verified',
        'active'
      );
    END IF;
  END IF;
  
  -- Return result
  RETURN jsonb_build_object(
    'success', verification_status = 'valid',
    'verification_id', verification_id,
    'verification_status', verification_status,
    'message', result_message,
    'student_id', p_student_id,
    'faces_detected', p_faces_detected,
    'face_detection_score', p_face_detection_score,
    'image_quality_score', p_image_quality_score,
    'processing_time_ms', p_processing_time_ms
  );
END;
$$;

-- Function to get verification statistics
CREATE OR REPLACE FUNCTION get_verification_statistics(
  p_days_back INTEGER DEFAULT 30
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  stats JSONB;
BEGIN
  WITH verification_stats AS (
    SELECT 
      COUNT(*) as total_attempts,
      COUNT(CASE WHEN verification_status = 'valid' THEN 1 END) as valid_attempts,
      COUNT(CASE WHEN verification_status = 'no_face' THEN 1 END) as no_face_attempts,
      COUNT(CASE WHEN verification_status = 'multiple_faces' THEN 1 END) as multiple_faces_attempts,
      COUNT(CASE WHEN verification_status = 'poor_quality' THEN 1 END) as poor_quality_attempts,
      AVG(face_detection_score) as avg_detection_score,
      AVG(image_quality_score) as avg_quality_score,
      AVG(processing_time_ms) as avg_processing_time
    FROM face_verification_results
    WHERE created_at >= NOW() - INTERVAL '1 day' * p_days_back
  )
  SELECT jsonb_build_object(
    'total_attempts', total_attempts,
    'valid_attempts', valid_attempts,
    'no_face_attempts', no_face_attempts,
    'multiple_faces_attempts', multiple_faces_attempts,
    'poor_quality_attempts', poor_quality_attempts,
    'success_rate', CASE 
      WHEN total_attempts > 0 THEN ROUND((valid_attempts::FLOAT / total_attempts::FLOAT) * 100, 2)
      ELSE 0 
    END,
    'avg_detection_score', ROUND(avg_detection_score, 3),
    'avg_quality_score', ROUND(avg_quality_score, 3),
    'avg_processing_time_ms', ROUND(avg_processing_time)
  ) INTO stats
  FROM verification_stats;
  
  RETURN stats;
END;
$$;

-- Function to get student verification history
CREATE OR REPLACE FUNCTION get_student_verification_history(
  p_student_id TEXT
)
RETURNS TABLE (
  verification_id UUID,
  image_url TEXT,
  faces_detected INTEGER,
  face_detection_score FLOAT,
  verification_status TEXT,
  error_message TEXT,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    fvr.id,
    fvr.image_url,
    fvr.faces_detected,
    fvr.face_detection_score,
    fvr.verification_status,
    fvr.error_message,
    fvr.created_at
  FROM face_verification_results fvr
  WHERE fvr.student_id = p_student_id
  ORDER BY fvr.created_at DESC;
END;
$$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_face_verification_results_student_id ON face_verification_results(student_id);
CREATE INDEX IF NOT EXISTS idx_face_verification_results_status ON face_verification_results(verification_status);
CREATE INDEX IF NOT EXISTS idx_face_verification_results_created_at ON face_verification_results(created_at);
CREATE INDEX IF NOT EXISTS idx_students_reference_image ON students(reference_image) WHERE reference_image IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_students_verification_status ON students(verification_status);

-- Enable Row Level Security
ALTER TABLE face_verification_results ENABLE ROW LEVEL SECURITY;

-- RLS Policies for face verification results
CREATE POLICY "Face verification results read access" ON face_verification_results FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

CREATE POLICY "Face verification results insert" ON face_verification_results FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

-- Create view for verification dashboard
CREATE OR REPLACE VIEW verification_dashboard AS
SELECT 
  s.student_id,
  s.name,
  s.email,
  s.reference_image,
  s.reference_image_verified,
  s.face_detection_score,
  s.image_upload_date,
  s.verification_status,
  COUNT(fvr.id) as total_attempts,
  COUNT(CASE WHEN fvr.verification_status = 'valid' THEN 1 END) as successful_attempts,
  MAX(fvr.created_at) as last_attempt_date
FROM students s
LEFT JOIN face_verification_results fvr ON s.student_id = fvr.student_id
GROUP BY s.student_id, s.name, s.email, s.reference_image, s.reference_image_verified, 
         s.face_detection_score, s.image_upload_date, s.verification_status
ORDER BY s.image_upload_date DESC NULLS LAST;

-- Insert sample verification data for testing
INSERT INTO face_verification_results (
  student_id, image_url, faces_detected, face_detection_score, 
  image_quality_score, verification_status, processing_time_ms
) VALUES
  ('S1001', 'https://example.com/anupam_ref.jpg', 1, 0.95, 0.92, 'valid', 1250),
  ('S1002', 'https://example.com/prakhar_ref.jpg', 1, 0.89, 0.87, 'valid', 1180),
  ('S1003', 'https://example.com/aakash_ref.jpg', 1, 0.93, 0.91, 'valid', 1320),
  ('TEST001', 'https://example.com/test_multiple.jpg', 2, 0.85, 0.80, 'multiple_faces', 980),
  ('TEST002', 'https://example.com/test_no_face.jpg', 0, 0.0, 0.45, 'no_face', 750)
ON CONFLICT DO NOTHING;
