-- Update students table to include face descriptor
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS face_descriptor BYTEA,
ADD COLUMN IF NOT EXISTS reference_image_url TEXT;

-- Create attendance logs table
CREATE TABLE IF NOT EXISTS attendance (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID REFERENCES students(id) ON DELETE CASCADE,
  status BOOLEAN DEFAULT TRUE,  -- TRUE = Present
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  confidence FLOAT,  -- Match confidence score (0-1)
  device_info JSONB,  -- <PERSON><PERSON><PERSON>, <PERSON>, etc.
  ip_address TEXT
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_students_user_id ON students(id);
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_timestamp ON attendance(timestamp);

-- Enable Row-Level Security (RLS)
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;

-- Students: Only the student themselves or admins can access face data
CREATE POLICY IF NOT EXISTS "Students can access their own face data"
  ON students
  USING (auth.uid() = id OR auth.jwt() ->> 'is_admin' = 'true');

-- Attendance: Students can only view their own attendance, admins can view all
CREATE POLICY IF NOT EXISTS "Students can view their own attendance"
  ON attendance
  FOR SELECT
  USING (auth.uid() = student_id OR auth.jwt() ->> 'is_admin' = 'true');

CREATE POLICY IF NOT EXISTS "Students can create their own attendance"
  ON attendance
  FOR INSERT
  WITH CHECK (auth.uid() = student_id);

-- Create function to get attendance statistics
CREATE OR REPLACE FUNCTION get_student_attendance_stats(
  student_uuid UUID,
  start_date TIMESTAMPTZ DEFAULT NULL,
  end_date TIMESTAMPTZ DEFAULT NOW()
)
RETURNS TABLE (
  total_days INTEGER,
  present_days INTEGER,
  attendance_rate FLOAT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(DISTINCT DATE(timestamp))::INTEGER AS total_days,
    COUNT(DISTINCT DATE(timestamp)) FILTER (WHERE status = TRUE)::INTEGER AS present_days,
    CASE
      WHEN COUNT(DISTINCT DATE(timestamp)) = 0 THEN 0
      ELSE COUNT(DISTINCT DATE(timestamp)) FILTER (WHERE status = TRUE)::FLOAT / 
           COUNT(DISTINCT DATE(timestamp))::FLOAT * 100
    END AS attendance_rate
  FROM
    attendance
  WHERE
    student_id = student_uuid
    AND (start_date IS NULL OR timestamp >= start_date)
    AND (timestamp <= end_date);
END;
$$;
