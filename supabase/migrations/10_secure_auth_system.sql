-- Secure Authentication System for Examino
-- <NAME_EMAIL> with enhanced security

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create auth_users table for secure authentication
CREATE TABLE IF NOT EXISTS auth_users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL CHECK (email = '<EMAIL>'),
  encrypted_password TEXT NOT NULL,
  password_salt TEXT NOT NULL,
  last_login TIMESTAMPTZ,
  failed_attempts INT DEFAULT 0,
  account_locked_until TIMESTAMPTZ,
  password_reset_token TEXT,
  password_reset_expires TIMESTAMPTZ,
  session_token TEXT,
  session_expires TIMESTAMPTZ,
  two_factor_secret TEXT,
  two_factor_enabled BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create login_attempts table for rate limiting and analytics
CREATE TABLE IF NOT EXISTS login_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  success BOOLEAN NOT NULL,
  failure_reason TEXT,
  location JSONB, -- Store geolocation data
  device_info JSONB, -- Store device fingerprint
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create password_history table for password rotation tracking
CREATE TABLE IF NOT EXISTS password_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth_users(id) ON DELETE CASCADE,
  encrypted_password TEXT NOT NULL,
  password_salt TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create admin_sessions table for session management
CREATE TABLE IF NOT EXISTS admin_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth_users(id) ON DELETE CASCADE,
  session_token TEXT UNIQUE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  expires_at TIMESTAMPTZ NOT NULL,
  last_activity TIMESTAMPTZ DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_auth_users_email ON auth_users(email);
CREATE INDEX IF NOT EXISTS idx_auth_users_session_token ON auth_users(session_token);
CREATE INDEX IF NOT EXISTS idx_login_attempts_email ON login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_login_attempts_ip ON login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_login_attempts_created_at ON login_attempts(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_token ON admin_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_user_id ON admin_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_expires_at ON admin_sessions(expires_at);

-- Function to hash passwords with salt
CREATE OR REPLACE FUNCTION hash_password(password TEXT, salt TEXT DEFAULT NULL)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  password_salt TEXT;
  hashed_password TEXT;
BEGIN
  -- Generate salt if not provided
  IF salt IS NULL THEN
    password_salt := encode(gen_random_bytes(32), 'base64');
  ELSE
    password_salt := salt;
  END IF;
  
  -- Hash password with salt using crypt
  hashed_password := crypt(password, password_salt);
  
  RETURN jsonb_build_object(
    'hash', hashed_password,
    'salt', password_salt
  );
END;
$$;

-- Function to verify password
CREATE OR REPLACE FUNCTION verify_password(password TEXT, hash TEXT, salt TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN crypt(password, salt) = hash;
END;
$$;

-- Function to check if account is locked
CREATE OR REPLACE FUNCTION is_account_locked(user_email TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  user_record auth_users%ROWTYPE;
BEGIN
  SELECT * INTO user_record FROM auth_users WHERE email = user_email;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Check if account is locked and lock period has expired
  IF user_record.account_locked_until IS NOT NULL AND user_record.account_locked_until > NOW() THEN
    RETURN TRUE;
  END IF;
  
  -- Reset lock if expired
  IF user_record.account_locked_until IS NOT NULL AND user_record.account_locked_until <= NOW() THEN
    UPDATE auth_users 
    SET account_locked_until = NULL, failed_attempts = 0 
    WHERE email = user_email;
  END IF;
  
  RETURN FALSE;
END;
$$;

-- Function to handle failed login attempts
CREATE OR REPLACE FUNCTION handle_failed_login(
  user_email TEXT,
  client_ip INET DEFAULT NULL,
  client_user_agent TEXT DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
DECLARE
  current_attempts INT;
  lock_duration INTERVAL;
BEGIN
  -- Insert failed login attempt
  INSERT INTO login_attempts (email, ip_address, user_agent, success, failure_reason)
  VALUES (user_email, client_ip, client_user_agent, FALSE, 'Invalid credentials');
  
  -- Update failed attempts count
  UPDATE auth_users 
  SET failed_attempts = failed_attempts + 1,
      updated_at = NOW()
  WHERE email = user_email
  RETURNING failed_attempts INTO current_attempts;
  
  -- Lock account if too many failed attempts
  IF current_attempts >= 5 THEN
    -- Progressive lockout: 15 minutes for 5 attempts, 1 hour for 10+
    IF current_attempts >= 10 THEN
      lock_duration := INTERVAL '1 hour';
    ELSE
      lock_duration := INTERVAL '15 minutes';
    END IF;
    
    UPDATE auth_users 
    SET account_locked_until = NOW() + lock_duration
    WHERE email = user_email;
  END IF;
END;
$$;

-- Function to handle successful login
CREATE OR REPLACE FUNCTION handle_successful_login(
  user_email TEXT,
  client_ip INET DEFAULT NULL,
  client_user_agent TEXT DEFAULT NULL
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
  session_token TEXT;
  user_id UUID;
BEGIN
  -- Generate session token
  session_token := encode(gen_random_bytes(32), 'base64');
  
  -- Get user ID
  SELECT id INTO user_id FROM auth_users WHERE email = user_email;
  
  -- Insert successful login attempt
  INSERT INTO login_attempts (email, ip_address, user_agent, success)
  VALUES (user_email, client_ip, client_user_agent, TRUE);
  
  -- Update user record
  UPDATE auth_users 
  SET last_login = NOW(),
      failed_attempts = 0,
      account_locked_until = NULL,
      session_token = session_token,
      session_expires = NOW() + INTERVAL '24 hours',
      updated_at = NOW()
  WHERE email = user_email;
  
  -- Create admin session
  INSERT INTO admin_sessions (user_id, session_token, ip_address, user_agent, expires_at)
  VALUES (user_id, session_token, client_ip, client_user_agent, NOW() + INTERVAL '24 hours');
  
  RETURN session_token;
END;
$$;

-- Function to validate session
CREATE OR REPLACE FUNCTION validate_session(token TEXT)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  session_record admin_sessions%ROWTYPE;
  user_record auth_users%ROWTYPE;
BEGIN
  -- Get session
  SELECT * INTO session_record 
  FROM admin_sessions 
  WHERE session_token = token 
    AND expires_at > NOW() 
    AND is_active = TRUE;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object('valid', FALSE, 'reason', 'Invalid or expired session');
  END IF;
  
  -- Get user
  SELECT * INTO user_record FROM auth_users WHERE id = session_record.user_id;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object('valid', FALSE, 'reason', 'User not found');
  END IF;
  
  -- Update last activity
  UPDATE admin_sessions 
  SET last_activity = NOW() 
  WHERE session_token = token;
  
  RETURN jsonb_build_object(
    'valid', TRUE,
    'user_id', user_record.id,
    'email', user_record.email,
    'last_login', user_record.last_login
  );
END;
$$;

-- Function to get login analytics
CREATE OR REPLACE FUNCTION get_login_analytics(days_back INT DEFAULT 30)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  total_attempts INT;
  successful_logins INT;
  failed_attempts INT;
  unique_ips INT;
  recent_attempts JSONB;
BEGIN
  -- Get total attempts
  SELECT COUNT(*) INTO total_attempts
  FROM login_attempts
  WHERE created_at >= NOW() - (days_back || ' days')::INTERVAL;
  
  -- Get successful logins
  SELECT COUNT(*) INTO successful_logins
  FROM login_attempts
  WHERE created_at >= NOW() - (days_back || ' days')::INTERVAL
    AND success = TRUE;
  
  -- Get failed attempts
  failed_attempts := total_attempts - successful_logins;
  
  -- Get unique IPs
  SELECT COUNT(DISTINCT ip_address) INTO unique_ips
  FROM login_attempts
  WHERE created_at >= NOW() - (days_back || ' days')::INTERVAL;
  
  -- Get recent attempts
  SELECT jsonb_agg(
    jsonb_build_object(
      'timestamp', created_at,
      'success', success,
      'ip_address', ip_address,
      'user_agent', user_agent,
      'failure_reason', failure_reason
    ) ORDER BY created_at DESC
  ) INTO recent_attempts
  FROM (
    SELECT * FROM login_attempts
    WHERE created_at >= NOW() - (days_back || ' days')::INTERVAL
    ORDER BY created_at DESC
    LIMIT 50
  ) recent;
  
  RETURN jsonb_build_object(
    'total_attempts', total_attempts,
    'successful_logins', successful_logins,
    'failed_attempts', failed_attempts,
    'success_rate', CASE WHEN total_attempts > 0 THEN (successful_logins::FLOAT / total_attempts * 100) ELSE 0 END,
    'unique_ips', unique_ips,
    'recent_attempts', COALESCE(recent_attempts, '[]'::jsonb)
  );
END;
$$;

-- Function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INT
LANGUAGE plpgsql
AS $$
DECLARE
  deleted_count INT;
BEGIN
  -- Deactivate expired sessions
  UPDATE admin_sessions 
  SET is_active = FALSE 
  WHERE expires_at <= NOW() AND is_active = TRUE;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Clean up old login attempts (keep last 90 days)
  DELETE FROM login_attempts 
  WHERE created_at < NOW() - INTERVAL '90 days';
  
  RETURN deleted_count;
END;
$$;

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_auth_users_updated_at
BEFORE UPDATE ON auth_users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE auth_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE login_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE password_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for auth_users (only the specific user can access)
CREATE POLICY "Users can only access their own data"
ON auth_users FOR ALL
USING (email = '<EMAIL>');

-- RLS Policies for login_attempts (only for the specific email)
CREATE POLICY "Login attempts for authorized email only"
ON login_attempts FOR ALL
USING (email = '<EMAIL>');

-- RLS Policies for password_history
CREATE POLICY "Password history for authorized user only"
ON password_history FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM auth_users 
    WHERE auth_users.id = password_history.user_id 
    AND auth_users.email = '<EMAIL>'
  )
);

-- RLS Policies for admin_sessions
CREATE POLICY "Admin sessions for authorized user only"
ON admin_sessions FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM auth_users 
    WHERE auth_users.id = admin_sessions.user_id 
    AND auth_users.email = '<EMAIL>'
  )
);

-- Insert the authorized user (password will be set via application)
-- This is a placeholder - the actual password should be set securely via the application
INSERT INTO auth_users (email, encrypted_password, password_salt) 
VALUES (
  '<EMAIL>', 
  'placeholder_hash', 
  'placeholder_salt'
) ON CONFLICT (email) DO NOTHING;
