-- Enhanced Photo Storage Schema for Examino

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pgvector extension for face embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Create a dedicated storage bucket for student photos
INSERT INTO storage.buckets (id, name, public)
VALUES ('student_photos', 'Student Photos', false)
ON CONFLICT (id) DO NOTHING;

-- Update students table with enhanced photo storage fields
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS photo_path TEXT, -- Path to image in Storage
ADD COLUMN IF NOT EXISTS embedding vector(512), -- For face embeddings using pgvector
ADD COLUMN IF NOT EXISTS last_photo_update TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS photo_quality FLOAT; -- Quality score of the stored photo (0-1)

-- Create index on face embeddings for faster similarity search
CREATE INDEX IF NOT EXISTS idx_students_embedding ON students USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- <PERSON>reate function to find similar faces
CREATE OR REPLACE FUNCTION find_similar_faces(
  search_embedding vector(512),
  similarity_threshold FLOAT DEFAULT 0.8,
  max_results INT DEFAULT 5
)
RETURNS TABLE (
  student_id UUID,
  first_name TEXT,
  last_name TEXT,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id as student_id,
    s.first_name,
    s.last_name,
    1 - (s.embedding <=> search_embedding) as similarity
  FROM 
    students s
  WHERE 
    s.embedding IS NOT NULL
    AND 1 - (s.embedding <=> search_embedding) > similarity_threshold
  ORDER BY 
    similarity DESC
  LIMIT 
    max_results;
END;
$$;

-- Create function to update student photo
CREATE OR REPLACE FUNCTION update_student_photo(
  p_student_id UUID,
  p_photo_path TEXT,
  p_embedding vector(512),
  p_quality FLOAT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE students
  SET 
    photo_path = p_photo_path,
    embedding = p_embedding,
    last_photo_update = NOW(),
    photo_quality = p_quality
  WHERE 
    id = p_student_id;
    
  RETURN FOUND;
END;
$$;

-- Storage policies for student photos
CREATE POLICY "Student photos are private"
ON storage.objects FOR SELECT
USING (
  bucket_id = 'student_photos' AND (
    -- Admins can view all photos
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.role_id
      WHERE users.id = auth.uid() AND user_roles.role_name = 'admin'
    )
    OR
    -- Students can only view their own photos
    (storage.foldername(name) = auth.uid()::text)
  )
);

CREATE POLICY "Only admins and owners can upload photos"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'student_photos' AND (
    -- Admins can upload to any folder
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.role_id
      WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
    )
    OR
    -- Students can only upload to their own folder
    (storage.foldername(name) = auth.uid()::text)
  )
);

CREATE POLICY "Only admins and owners can update photos"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'student_photos' AND (
    -- Admins can update any photo
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.role_id
      WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
    )
    OR
    -- Students can only update their own photos
    (storage.foldername(name) = auth.uid()::text)
  )
);

-- Create a table for real-time photo updates
CREATE TABLE IF NOT EXISTS photo_updates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID REFERENCES students(id) ON DELETE CASCADE,
  update_type TEXT NOT NULL, -- 'upload', 'update', 'delete'
  photo_path TEXT,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  processed BOOLEAN DEFAULT FALSE
);

-- Enable RLS on photo_updates
ALTER TABLE photo_updates ENABLE ROW LEVEL SECURITY;

-- Create policy for photo_updates
CREATE POLICY "Admins can view all photo updates"
ON photo_updates FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
  )
);

CREATE POLICY "Students can view their own photo updates"
ON photo_updates FOR SELECT
USING (
  student_id = (
    SELECT id FROM students WHERE id = auth.uid()
  )
);

-- Create trigger to notify on photo updates
CREATE OR REPLACE FUNCTION notify_photo_update()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM pg_notify(
    'photo_updates',
    json_build_object(
      'id', NEW.id,
      'student_id', NEW.student_id,
      'update_type', NEW.update_type,
      'timestamp', NEW.timestamp
    )::text
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER photo_update_notify
AFTER INSERT ON photo_updates
FOR EACH ROW
EXECUTE FUNCTION notify_photo_update();

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_photo_updates_student_id ON photo_updates(student_id);
CREATE INDEX IF NOT EXISTS idx_photo_updates_processed ON photo_updates(processed);
