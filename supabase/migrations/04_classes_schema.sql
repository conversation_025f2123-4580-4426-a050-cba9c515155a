-- Create classes table
CREATE TABLE IF NOT EXISTS classes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- Create student_classes junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS student_classes (
  student_id UUID REFERENCES students(id) ON DELETE CASCADE,
  class_id UUID REFERENCES classes(id) ON DELETE CASCADE,
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (student_id, class_id)
);

-- Update exams table to support class assignment
ALTER TABLE exams 
ADD COLUMN IF NOT EXISTS class_ids UUID[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS scheduled_at TIMESTAMPTZ;

-- Add temporary_password field to students table for CSV imports
ALTER TABLE students
ADD COLUMN IF NOT EXISTS temporary_password TEXT,
ADD COLUMN IF NOT EXISTS email TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;

-- Enable Row-Level Security (RLS)
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_classes ENABLE ROW LEVEL SECURITY;

-- RLS Policies for classes
CREATE POLICY "Admins can create classes"
  ON classes FOR INSERT
  WITH CHECK (auth.jwt() ->> 'is_admin' = 'true');

CREATE POLICY "Admins can update classes"
  ON classes FOR UPDATE
  USING (auth.jwt() ->> 'is_admin' = 'true');

CREATE POLICY "Admins can delete classes"
  ON classes FOR DELETE
  USING (auth.jwt() ->> 'is_admin' = 'true');

CREATE POLICY "Everyone can view classes"
  ON classes FOR SELECT
  USING (true);

-- RLS Policies for student_classes
CREATE POLICY "Admins can manage student_classes"
  ON student_classes
  USING (auth.jwt() ->> 'is_admin' = 'true');

CREATE POLICY "Students can view their own classes"
  ON student_classes FOR SELECT
  USING (auth.uid() = student_id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_student_classes_student ON student_classes(student_id);
CREATE INDEX IF NOT EXISTS idx_student_classes_class ON student_classes(class_id);
CREATE INDEX IF NOT EXISTS idx_classes_created_by ON classes(created_by);
CREATE INDEX IF NOT EXISTS idx_students_email ON students(email);

-- Create function to get students in a class
CREATE OR REPLACE FUNCTION get_students_in_class(class_uuid UUID)
RETURNS TABLE (
  student_id UUID,
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  student_id_text TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    s.id,
    s.first_name,
    s.last_name,
    s.email,
    s.student_id
  FROM
    students s
    JOIN student_classes sc ON s.id = sc.student_id
  WHERE
    sc.class_id = class_uuid
  ORDER BY
    s.last_name, s.first_name;
END;
$$;
