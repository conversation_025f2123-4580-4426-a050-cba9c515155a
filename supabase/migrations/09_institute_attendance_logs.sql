-- Institute Attendance Logs Schema for Examino

-- Create attendance_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS attendance_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  institute_id UUID REFERENCES users(id) ON DELETE CASCADE,
  student_id UUID REFERENCES students(id) ON DELETE CASCADE,
  class_id UUID REFERENCES classes(id) ON DELETE SET NULL,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  status BOOLEAN NOT NULL DEFAULT true, -- true = present, false = absent
  verification_method TEXT, -- 'face', 'manual', 'qr', etc.
  confidence FLOAT, -- confidence score for face verification (0-1)
  notes TEXT, -- optional notes about the attendance
  location JSONB, -- optional location data
  device_info JSONB, -- device information for audit
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_attendance_logs_institute_id ON attendance_logs(institute_id);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_student_id ON attendance_logs(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_class_id ON attendance_logs(class_id);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_date ON attendance_logs(date);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_status ON attendance_logs(status);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_created_at ON attendance_logs(created_at);

-- Create function to get attendance logs by date range for an institute
CREATE OR REPLACE FUNCTION get_institute_attendance_logs(
  p_institute_id UUID,
  p_start_date DATE,
  p_end_date DATE,
  p_class_id UUID DEFAULT NULL,
  p_status BOOLEAN DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  student_id UUID,
  student_name TEXT,
  student_id_text TEXT,
  class_id UUID,
  class_name TEXT,
  date DATE,
  status BOOLEAN,
  verification_method TEXT,
  confidence FLOAT,
  notes TEXT,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    al.id,
    al.student_id,
    (s.first_name || ' ' || s.last_name) AS student_name,
    s.student_id AS student_id_text,
    al.class_id,
    c.name AS class_name,
    al.date,
    al.status,
    al.verification_method,
    al.confidence,
    al.notes,
    al.created_at
  FROM 
    attendance_logs al
  JOIN 
    students s ON al.student_id = s.id
  LEFT JOIN 
    classes c ON al.class_id = c.id
  WHERE 
    al.institute_id = p_institute_id
    AND al.date BETWEEN p_start_date AND p_end_date
    AND (p_class_id IS NULL OR al.class_id = p_class_id)
    AND (p_status IS NULL OR al.status = p_status)
  ORDER BY 
    al.date DESC, al.created_at DESC;
END;
$$;

-- Create function to get attendance statistics for an institute
CREATE OR REPLACE FUNCTION get_institute_attendance_statistics(
  p_institute_id UUID,
  p_start_date DATE,
  p_end_date DATE,
  p_class_id UUID DEFAULT NULL
)
RETURNS TABLE (
  total_records BIGINT,
  present_count BIGINT,
  absent_count BIGINT,
  attendance_rate FLOAT,
  class_id UUID,
  class_name TEXT,
  date DATE
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) AS total_records,
    SUM(CASE WHEN al.status = true THEN 1 ELSE 0 END) AS present_count,
    SUM(CASE WHEN al.status = false THEN 1 ELSE 0 END) AS absent_count,
    CASE 
      WHEN COUNT(*) > 0 THEN 
        (SUM(CASE WHEN al.status = true THEN 1 ELSE 0 END)::FLOAT / COUNT(*)::FLOAT) * 100
      ELSE 0
    END AS attendance_rate,
    al.class_id,
    c.name AS class_name,
    al.date
  FROM 
    attendance_logs al
  LEFT JOIN
    classes c ON al.class_id = c.id
  WHERE 
    al.institute_id = p_institute_id
    AND al.date BETWEEN p_start_date AND p_end_date
    AND (p_class_id IS NULL OR al.class_id = p_class_id)
  GROUP BY 
    al.class_id, c.name, al.date
  ORDER BY 
    al.date DESC;
END;
$$;

-- Create function to get daily attendance summary for an institute
CREATE OR REPLACE FUNCTION get_institute_daily_attendance_summary(
  p_institute_id UUID,
  p_start_date DATE,
  p_end_date DATE
)
RETURNS TABLE (
  date DATE,
  total_students BIGINT,
  present_count BIGINT,
  absent_count BIGINT,
  attendance_rate FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    al.date,
    COUNT(DISTINCT al.student_id) AS total_students,
    SUM(CASE WHEN al.status = true THEN 1 ELSE 0 END) AS present_count,
    SUM(CASE WHEN al.status = false THEN 1 ELSE 0 END) AS absent_count,
    CASE 
      WHEN COUNT(*) > 0 THEN 
        (SUM(CASE WHEN al.status = true THEN 1 ELSE 0 END)::FLOAT / COUNT(*)::FLOAT) * 100
      ELSE 0
    END AS attendance_rate
  FROM 
    attendance_logs al
  WHERE 
    al.institute_id = p_institute_id
    AND al.date BETWEEN p_start_date AND p_end_date
  GROUP BY 
    al.date
  ORDER BY 
    al.date DESC;
END;
$$;

-- Create function to get student attendance summary for an institute
CREATE OR REPLACE FUNCTION get_institute_student_attendance_summary(
  p_institute_id UUID,
  p_start_date DATE,
  p_end_date DATE,
  p_class_id UUID DEFAULT NULL
)
RETURNS TABLE (
  student_id UUID,
  student_name TEXT,
  student_id_text TEXT,
  total_days BIGINT,
  present_days BIGINT,
  absent_days BIGINT,
  attendance_rate FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id AS student_id,
    (s.first_name || ' ' || s.last_name) AS student_name,
    s.student_id AS student_id_text,
    COUNT(DISTINCT al.date) AS total_days,
    SUM(CASE WHEN al.status = true THEN 1 ELSE 0 END) AS present_days,
    SUM(CASE WHEN al.status = false THEN 1 ELSE 0 END) AS absent_days,
    CASE 
      WHEN COUNT(*) > 0 THEN 
        (SUM(CASE WHEN al.status = true THEN 1 ELSE 0 END)::FLOAT / COUNT(*)::FLOAT) * 100
      ELSE 0
    END AS attendance_rate
  FROM 
    students s
  LEFT JOIN 
    attendance_logs al ON s.id = al.student_id
    AND al.institute_id = p_institute_id
    AND al.date BETWEEN p_start_date AND p_end_date
    AND (p_class_id IS NULL OR al.class_id = p_class_id)
  WHERE
    EXISTS (
      SELECT 1 FROM student_classes sc
      WHERE sc.student_id = s.id
      AND (p_class_id IS NULL OR sc.class_id = p_class_id)
    )
  GROUP BY 
    s.id, s.first_name, s.last_name, s.student_id
  ORDER BY 
    attendance_rate DESC;
END;
$$;

-- Create trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_attendance_logs_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_attendance_logs_timestamp
BEFORE UPDATE ON attendance_logs
FOR EACH ROW
EXECUTE FUNCTION update_attendance_logs_timestamp();

-- Enable RLS on attendance_logs
ALTER TABLE attendance_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for attendance_logs
CREATE POLICY "Institutes can view their own attendance logs"
ON attendance_logs FOR SELECT
USING (
  institute_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name = 'admin'
  )
);

CREATE POLICY "Institutes can insert their own attendance logs"
ON attendance_logs FOR INSERT
WITH CHECK (
  institute_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name = 'admin'
  )
);

CREATE POLICY "Institutes can update their own attendance logs"
ON attendance_logs FOR UPDATE
USING (
  institute_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name = 'admin'
  )
);

CREATE POLICY "Institutes can delete their own attendance logs"
ON attendance_logs FOR DELETE
USING (
  institute_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name = 'admin'
  )
);

-- Create policy for students to view their own attendance logs
CREATE POLICY "Students can view their own attendance logs"
ON attendance_logs FOR SELECT
USING (
  student_id = (
    SELECT id FROM students WHERE auth_id = auth.uid()
  )
);
