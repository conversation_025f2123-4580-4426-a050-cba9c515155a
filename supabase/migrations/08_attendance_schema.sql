-- Enhanced Attendance Schema for Examino

-- Create attendance table if it doesn't exist
CREATE TABLE IF NOT EXISTS attendance (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
  class_id UUID REFERENCES classes(id) ON DELETE SET NULL,
  status BOOLEAN NOT NULL DEFAULT true, -- true = present, false = absent
  verification_method TEXT, -- 'face', 'manual', etc.
  confidence FLOAT, -- confidence score for face verification (0-1)
  device_info JSONB, -- device information for audit
  date DATE DEFAULT CURRENT_DATE, -- date of attendance
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_class_id ON attendance(class_id);
CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(date);
CREATE INDEX IF NOT EXISTS idx_attendance_created_at ON attendance(created_at);

-- Create function to get attendance by date range
CREATE OR REPLACE FUNCTION get_attendance_by_date_range(
  start_date DATE,
  end_date DATE,
  class_id UUID DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  student_id UUID,
  student_name TEXT,
  student_id_text TEXT,
  class_id UUID,
  class_name TEXT,
  status BOOLEAN,
  verification_method TEXT,
  confidence FLOAT,
  date DATE,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.student_id,
    (s.first_name || ' ' || s.last_name) AS student_name,
    s.student_id AS student_id_text,
    a.class_id,
    c.name AS class_name,
    a.status,
    a.verification_method,
    a.confidence,
    a.date,
    a.created_at
  FROM 
    attendance a
  JOIN 
    students s ON a.student_id = s.id
  LEFT JOIN 
    classes c ON a.class_id = c.id
  WHERE 
    a.date BETWEEN start_date AND end_date
    AND (class_id IS NULL OR a.class_id = class_id)
  ORDER BY 
    a.created_at DESC;
END;
$$;

-- Create function to get attendance statistics
CREATE OR REPLACE FUNCTION get_attendance_statistics(
  start_date DATE,
  end_date DATE,
  class_id UUID DEFAULT NULL
)
RETURNS TABLE (
  total_records BIGINT,
  present_count BIGINT,
  absent_count BIGINT,
  attendance_rate FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) AS total_records,
    SUM(CASE WHEN status = true THEN 1 ELSE 0 END) AS present_count,
    SUM(CASE WHEN status = false THEN 1 ELSE 0 END) AS absent_count,
    CASE 
      WHEN COUNT(*) > 0 THEN 
        (SUM(CASE WHEN status = true THEN 1 ELSE 0 END)::FLOAT / COUNT(*)::FLOAT) * 100
      ELSE 0
    END AS attendance_rate
  FROM 
    attendance
  WHERE 
    date BETWEEN start_date AND end_date
    AND (class_id IS NULL OR class_id = class_id);
END;
$$;

-- Create trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_attendance_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_attendance_timestamp
BEFORE UPDATE ON attendance
FOR EACH ROW
EXECUTE FUNCTION update_attendance_timestamp();

-- Enable RLS on attendance
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;

-- Create policies for attendance
CREATE POLICY "Admins and institutes can view all attendance records"
ON attendance FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
  )
);

CREATE POLICY "Admins and institutes can insert attendance records"
ON attendance FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
  )
);

CREATE POLICY "Admins and institutes can update attendance records"
ON attendance FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
  )
);

CREATE POLICY "Admins and institutes can delete attendance records"
ON attendance FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM users
    JOIN user_roles ON users.role_id = user_roles.role_id
    WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
  )
);

CREATE POLICY "Students can view their own attendance records"
ON attendance FOR SELECT
USING (
  student_id = (
    SELECT id FROM students WHERE auth_id = auth.uid()
  )
);
