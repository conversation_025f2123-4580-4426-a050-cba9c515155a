-- Multi-Student Face Verification System
-- Enhanced database schema for face embeddings and verification

-- Enable pgvector extension for face embeddings (if not already enabled)
-- Note: This needs to be enabled in Supabase dashboard
-- CREATE EXTENSION IF NOT EXISTS vector;

-- Add face embedding columns to students table
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS original_face_embedding FLOAT[],
ADD COLUMN IF NOT EXISTS verification_photos TEXT[],
ADD COLUMN IF NOT EXISTS face_registration_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS face_quality_score FLOAT,
ADD COLUMN IF NOT EXISTS anti_spoofing_score FLOAT;

-- Create face verification logs table
CREATE TABLE IF NOT EXISTS face_verification_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id TEXT NOT NULL REFERENCES students(student_id),
  verification_image_url TEXT,
  similarity_score FLOAT NOT NULL,
  confidence_percentage FLOAT NOT NULL,
  is_match BOOLEAN NOT NULL,
  verification_method TEXT DEFAULT 'face_recognition',
  device_info JSONB,
  ip_address INET,
  user_agent TEXT,
  anti_spoofing_passed BOOLEAN DEFAULT TRUE,
  detection_quality FLOAT,
  verification_time_ms INTEGER,
  verified_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert sample students with initial data
INSERT INTO students (name, student_id, email, status, enrollment_date) VALUES
  ('Anupam', 'S1001', '<EMAIL>', 'active', CURRENT_DATE),
  ('Prakhar', 'S1002', '<EMAIL>', 'active', CURRENT_DATE),
  ('Aakash', 'S1003', '<EMAIL>', 'active', CURRENT_DATE)
ON CONFLICT (student_id) DO UPDATE SET
  name = EXCLUDED.name,
  email = EXCLUDED.email,
  status = EXCLUDED.status;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_face_verification_student_id ON face_verification_attempts(student_id);
CREATE INDEX IF NOT EXISTS idx_face_verification_created_at ON face_verification_attempts(created_at);
CREATE INDEX IF NOT EXISTS idx_face_verification_is_match ON face_verification_attempts(is_match);
CREATE INDEX IF NOT EXISTS idx_students_face_registration ON students(face_registration_date) WHERE face_registration_date IS NOT NULL;

-- Function to calculate face similarity (placeholder for actual implementation)
CREATE OR REPLACE FUNCTION calculate_face_similarity(
  embedding1 FLOAT[],
  embedding2 FLOAT[]
)
RETURNS FLOAT
LANGUAGE plpgsql
AS $$
DECLARE
  similarity FLOAT;
  i INTEGER;
  sum_squares FLOAT := 0;
BEGIN
  -- Simple Euclidean distance calculation
  -- In production, this would be replaced with proper vector similarity
  
  IF array_length(embedding1, 1) != array_length(embedding2, 1) THEN
    RETURN 0;
  END IF;
  
  FOR i IN 1..array_length(embedding1, 1) LOOP
    sum_squares := sum_squares + POWER(embedding1[i] - embedding2[i], 2);
  END LOOP;
  
  similarity := SQRT(sum_squares);
  
  -- Convert to similarity score (0-1, where 1 is perfect match)
  RETURN GREATEST(0, 1 - (similarity / 2));
END;
$$;

-- Function to register face embedding
CREATE OR REPLACE FUNCTION register_face_embedding(
  p_student_id TEXT,
  p_face_embedding FLOAT[],
  p_photo_url TEXT,
  p_quality_score FLOAT DEFAULT NULL,
  p_anti_spoofing_score FLOAT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  -- Update student with face data
  UPDATE students 
  SET 
    original_face_embedding = p_face_embedding,
    verification_photos = COALESCE(verification_photos, '{}') || ARRAY[p_photo_url],
    face_registration_date = NOW(),
    face_quality_score = p_quality_score,
    anti_spoofing_score = p_anti_spoofing_score,
    updated_at = NOW()
  WHERE student_id = p_student_id;
  
  RETURN FOUND;
END;
$$;

-- Function to verify face against stored embedding
CREATE OR REPLACE FUNCTION verify_face_embedding(
  p_student_id TEXT,
  p_live_embedding FLOAT[],
  p_verification_image_url TEXT DEFAULT NULL,
  p_device_info JSONB DEFAULT NULL,
  p_threshold FLOAT DEFAULT 0.6
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  student_record RECORD;
  similarity_score FLOAT;
  confidence_percentage FLOAT;
  is_match BOOLEAN;
  verification_id UUID;
BEGIN
  -- Get student's stored face embedding
  SELECT name, original_face_embedding, face_quality_score
  INTO student_record
  FROM students 
  WHERE student_id = p_student_id AND original_face_embedding IS NOT NULL;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Student not found or face not registered'
    );
  END IF;
  
  -- Calculate similarity
  similarity_score := calculate_face_similarity(
    student_record.original_face_embedding,
    p_live_embedding
  );
  
  confidence_percentage := similarity_score * 100;
  is_match := similarity_score >= p_threshold;
  
  -- Log verification attempt
  INSERT INTO face_verification_attempts (
    student_id,
    verification_image_url,
    similarity_score,
    confidence_percentage,
    is_match,
    device_info,
    ip_address,
    user_agent
  ) VALUES (
    p_student_id,
    p_verification_image_url,
    similarity_score,
    confidence_percentage,
    is_match,
    p_device_info,
    inet_client_addr(),
    current_setting('request.headers', true)::json->>'user-agent'
  ) RETURNING id INTO verification_id;
  
  -- Return verification result
  RETURN jsonb_build_object(
    'success', true,
    'verification_id', verification_id,
    'student_name', student_record.name,
    'is_match', is_match,
    'confidence', confidence_percentage,
    'similarity_score', similarity_score,
    'threshold_used', p_threshold
  );
END;
$$;

-- Function to get face verification statistics
CREATE OR REPLACE FUNCTION get_face_verification_stats(
  p_student_id TEXT DEFAULT NULL,
  p_days_back INTEGER DEFAULT 30
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  stats JSONB;
BEGIN
  WITH verification_stats AS (
    SELECT 
      COUNT(*) as total_attempts,
      COUNT(CASE WHEN is_match THEN 1 END) as successful_matches,
      COUNT(CASE WHEN NOT is_match THEN 1 END) as failed_matches,
      AVG(confidence_percentage) as avg_confidence,
      MAX(confidence_percentage) as max_confidence,
      MIN(confidence_percentage) as min_confidence,
      AVG(verification_time_ms) as avg_verification_time
    FROM face_verification_attempts
    WHERE 
      (p_student_id IS NULL OR student_id = p_student_id)
      AND created_at >= NOW() - INTERVAL '1 day' * p_days_back
  )
  SELECT jsonb_build_object(
    'total_attempts', total_attempts,
    'successful_matches', successful_matches,
    'failed_matches', failed_matches,
    'success_rate', CASE 
      WHEN total_attempts > 0 THEN ROUND((successful_matches::FLOAT / total_attempts::FLOAT) * 100, 2)
      ELSE 0 
    END,
    'avg_confidence', ROUND(avg_confidence, 2),
    'max_confidence', ROUND(max_confidence, 2),
    'min_confidence', ROUND(min_confidence, 2),
    'avg_verification_time_ms', ROUND(avg_verification_time)
  ) INTO stats
  FROM verification_stats;
  
  RETURN stats;
END;
$$;

-- Enable Row Level Security
ALTER TABLE face_verification_attempts ENABLE ROW LEVEL SECURITY;

-- RLS Policies for face verification attempts
CREATE POLICY "Face verification read access" ON face_verification_attempts FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

CREATE POLICY "Face verification insert" ON face_verification_attempts FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
);

-- Policy for face data access (enhanced security)
CREATE POLICY "Face data access" ON students FOR SELECT USING (
  -- Allow access to face data only for authorized users
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.user_id = auth.uid() 
    AND user_profiles.role_id IN (1, 2) -- Admin or Institute
  )
  OR
  -- Allow students to access their own data
  student_id IN (
    SELECT student_id FROM user_profiles 
    WHERE user_id = auth.uid() AND role_id = 3 -- Student role
  )
);

-- Create trigger to update student updated_at timestamp
CREATE OR REPLACE FUNCTION update_student_face_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_student_face_updated_at
BEFORE UPDATE ON students
FOR EACH ROW
WHEN (OLD.original_face_embedding IS DISTINCT FROM NEW.original_face_embedding)
EXECUTE FUNCTION update_student_face_timestamp();

-- Insert sample face embeddings (mock data for testing)
-- In production, these would be generated by face-api.js
UPDATE students SET 
  original_face_embedding = ARRAY[
    0.12, 0.34, 0.56, 0.78, 0.90, 0.11, 0.23, 0.45, 0.67, 0.89,
    0.13, 0.35, 0.57, 0.79, 0.91, 0.12, 0.24, 0.46, 0.68, 0.80
  ],
  face_quality_score = 0.95,
  anti_spoofing_score = 0.92,
  face_registration_date = NOW()
WHERE student_id = 'S1001';

UPDATE students SET 
  original_face_embedding = ARRAY[
    0.23, 0.45, 0.67, 0.89, 0.01, 0.22, 0.34, 0.56, 0.78, 0.90,
    0.24, 0.46, 0.68, 0.80, 0.02, 0.23, 0.35, 0.57, 0.79, 0.91
  ],
  face_quality_score = 0.93,
  anti_spoofing_score = 0.89,
  face_registration_date = NOW()
WHERE student_id = 'S1002';

UPDATE students SET 
  original_face_embedding = ARRAY[
    0.34, 0.56, 0.78, 0.90, 0.12, 0.33, 0.45, 0.67, 0.89, 0.01,
    0.35, 0.57, 0.79, 0.91, 0.13, 0.34, 0.46, 0.68, 0.80, 0.02
  ],
  face_quality_score = 0.91,
  anti_spoofing_score = 0.94,
  face_registration_date = NOW()
WHERE student_id = 'S1003';

-- Create view for face verification dashboard
CREATE OR REPLACE VIEW face_verification_dashboard AS
SELECT 
  s.student_id,
  s.name,
  s.face_registration_date,
  s.face_quality_score,
  s.anti_spoofing_score,
  COUNT(fva.id) as total_verifications,
  COUNT(CASE WHEN fva.is_match THEN 1 END) as successful_verifications,
  AVG(fva.confidence_percentage) as avg_confidence,
  MAX(fva.created_at) as last_verification
FROM students s
LEFT JOIN face_verification_attempts fva ON s.student_id = fva.student_id
WHERE s.original_face_embedding IS NOT NULL
GROUP BY s.student_id, s.name, s.face_registration_date, s.face_quality_score, s.anti_spoofing_score
ORDER BY s.name;
