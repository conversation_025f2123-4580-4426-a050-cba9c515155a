-- Class Management Schema

-- Classes table
CREATE TABLE IF NOT EXISTS classes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Student-Class relationship table
CREATE TABLE IF NOT EXISTS student_classes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
  class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(student_id, class_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_classes_name ON classes(name);
CREATE INDEX IF NOT EXISTS idx_student_classes_student_id ON student_classes(student_id);
CREATE INDEX IF NOT EXISTS idx_student_classes_class_id ON student_classes(class_id);

-- Function to get students in a class
CREATE OR REPLACE FUNCTION get_students_in_class(class_uuid UUID)
RETURNS TABLE (
  id UUID,
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  student_id_text TEXT,
  course TEXT,
  semester TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id,
    s.first_name,
    s.last_name,
    s.email,
    s.student_id AS student_id_text,
    s.course,
    s.semester
  FROM 
    students s
  JOIN 
    student_classes sc ON s.id = sc.student_id
  WHERE 
    sc.class_id = class_uuid
  ORDER BY 
    s.last_name, s.first_name;
END;
$$ LANGUAGE plpgsql;

-- RLS Policies
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_classes ENABLE ROW LEVEL SECURITY;

-- Admins and institutes can see all classes
CREATE POLICY admin_institute_select_classes ON classes
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.role_id
      WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
    )
  );

-- Admins and institutes can insert classes
CREATE POLICY admin_institute_insert_classes ON classes
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.role_id
      WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
    )
  );

-- Admins and institutes can update classes
CREATE POLICY admin_institute_update_classes ON classes
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.role_id
      WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
    )
  );

-- Admins and institutes can delete classes
CREATE POLICY admin_institute_delete_classes ON classes
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.role_id
      WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
    )
  );

-- Similar policies for student_classes table
CREATE POLICY admin_institute_all_student_classes ON student_classes
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.role_id
      WHERE users.id = auth.uid() AND user_roles.role_name IN ('admin', 'institute')
    )
  );

-- Students can see their own classes
CREATE POLICY student_select_own_classes ON student_classes
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.role_id
      WHERE users.id = auth.uid() AND user_roles.role_name = 'student'
      AND student_id = (SELECT id FROM students WHERE auth_id = auth.uid())
    )
  );

-- Add trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_classes_timestamp
BEFORE UPDATE ON classes
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();
