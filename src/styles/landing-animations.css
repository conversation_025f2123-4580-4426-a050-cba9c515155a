/* Scan line animation */
@keyframes scan {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

.face-scan::after {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  left: 0;
  background: rgba(74, 222, 128, 0.7);
  animation: scan 1.5s infinite;
  pointer-events: none;
}

/* Floating animation for logo */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.float {
  animation: float 3s ease-in-out infinite;
}

/* Pulse animation */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Bar chart animation */
@keyframes fillUp {
  from { height: 0; top: 100%; }
  to { height: var(--bar-height); top: calc(100% - var(--bar-height)); }
}

.bar-chart .bar {
  position: absolute;
  bottom: 0;
  width: 100%;
  background: var(--bar-color);
  height: 0;
  transition: height 1s ease-out;
}

.bar-chart.animate .bar {
  height: var(--bar-height);
}

/* Feature card hover effects */
.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.feature-card .icon-container {
  transition: transform 0.3s ease;
}

.feature-card:hover .icon-container {
  transform: scale(1.1);
}

/* Glassmorphism effect */
.glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(90deg, #3B82F6, #8B5CF6);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Examino name styling */
.examino-name {
  letter-spacing: 0.1em;
  text-transform: uppercase;
  font-weight: 700;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Staggered animation for features */
.stagger-item {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.stagger-item.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Shield animation */
@keyframes shield-pulse {
  0% { opacity: 0.7; }
  50% { opacity: 0.9; }
  100% { opacity: 0.7; }
}

.shield-pulse {
  animation: shield-pulse 2s infinite;
}

/* Responsive image container */
.responsive-image {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%; /* 1:1 Aspect Ratio */
  overflow: hidden;
}

.responsive-image img,
.responsive-image svg {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

@media (max-width: 768px) {
  .responsive-image {
    padding-bottom: 75%; /* 4:3 Aspect Ratio for mobile */
  }
}
