/* Welcome Page Specific Styles */

/* Dark mode support */
.dark {
  color-scheme: dark;
}

/* Gradient text */
.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #3b82f6, #8b5cf6);
}

/* Animated underline for links */
.animated-underline {
  position: relative;
  display: inline-block;
}

.animated-underline::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-image: linear-gradient(to right, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.animated-underline:hover::after {
  width: 100%;
}

/* Glow effect for cards */
.glow-on-hover:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

/* Particle background */
.particle-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

/* Responsive video container */
.video-container {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
}

.video-container iframe,
.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse-animation {
  animation: pulse 3s ease-in-out infinite;
}

/* Media queries for better mobile experience */
@media (max-width: 768px) {
  .hero-heading {
    font-size: 2.5rem;
  }
  
  .hero-subheading {
    font-size: 1.25rem;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
}

/* Utility classes */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
