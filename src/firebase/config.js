import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyAVdsvA2MCelfnJFujw4XC8g9V6HXWNt1I",
  authDomain: "examino-81828.firebaseapp.com",
  projectId: "examino-81828",
  storageBucket: "examino-app.appspot.com",
  messagingSenderId: "141585669014",
  appId: "1:141585669014:web:a23456789abcdef123456",
  measurementId: "G-ABCDEF1234"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);

export { auth, db, storage };
