import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import Header from '../components/Header';
import StudentManagement from '../components/admin/StudentManagement';
import EnhancedStudentManagement from '../components/admin/EnhancedStudentManagement';
import StudentUpload from '../components/admin/StudentUpload';
import StudentForm from '../components/admin/StudentForm';
import InviteGenerator from '../components/admin/InviteGenerator';
import ClassManagement from '../components/admin/ClassManagement';
import StudentVerification from '../components/admin/StudentVerification';

export default function AdminDashboard() {
  const { currentUser, userProfile, userRole, isAdmin, isInstitute } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [studentAdded, setStudentAdded] = useState(false);

  // Determine dashboard title based on user role
  const dashboardTitle = isAdmin ? 'Admin Dashboard' : isInstitute ? 'Institute Dashboard' : 'Dashboard';

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-6">
        <h1 className="text-2xl font-semibold mb-6">{dashboardTitle}</h1>

        {/* Tabs */}
        <div className="flex flex-wrap mb-6 border-b">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-4 py-2 font-medium ${activeTab === 'overview' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('students')}
            className={`px-4 py-2 font-medium ${activeTab === 'students' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Student Management
          </button>
          <button
            onClick={() => setActiveTab('enhanced-students')}
            className={`px-4 py-2 font-medium ${activeTab === 'enhanced-students' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Enhanced Students
          </button>
          <button
            onClick={() => setActiveTab('classes')}
            className={`px-4 py-2 font-medium ${activeTab === 'classes' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Class Management
          </button>
          <button
            onClick={() => setActiveTab('upload')}
            className={`px-4 py-2 font-medium ${activeTab === 'upload' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Bulk Upload
          </button>
          <button
            onClick={() => setActiveTab('attendance')}
            className={`px-4 py-2 font-medium ${activeTab === 'attendance' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Attendance Logs
          </button>
          <button
            onClick={() => setActiveTab('verification')}
            className={`px-4 py-2 font-medium ${activeTab === 'verification' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Face Verification
          </button>
          <button
            onClick={() => setActiveTab('exams')}
            className={`px-4 py-2 font-medium ${activeTab === 'exams' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Exam Management
          </button>
          {isAdmin && (
            <button
              onClick={() => setActiveTab('invites')}
              className={`px-4 py-2 font-medium ${activeTab === 'invites' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
            >
              Manage Invites
            </button>
          )}
        </div>

        {activeTab === 'overview' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Welcome to the Admin Dashboard</h2>

            <div className="mb-6">
              <p className="text-gray-700">
                You are logged in as: <span className="font-semibold">{currentUser?.email}</span>
              </p>
              {userProfile && (
                <p className="text-gray-700 mt-2">
                  Name: <span className="font-semibold">{userProfile.first_name} {userProfile.last_name}</span>
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-800 mb-3">Exam Management</h3>
                <p className="text-blue-700 mb-4">Create and manage exams, view results, and analyze student performance.</p>
                <button
                  onClick={() => setActiveTab('exams')}
                  className="text-blue-600 hover:text-blue-800 font-medium"
                >
                  Go to Exam Management →
                </button>
              </div>

              <div className="bg-green-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-green-800 mb-3">Student Management</h3>
                <p className="text-green-700 mb-4">Manage student profiles, register face data, and view attendance records.</p>
                <button
                  onClick={() => setActiveTab('students')}
                  className="text-green-600 hover:text-green-800 font-medium"
                >
                  Go to Student Management →
                </button>
              </div>

              <div className="bg-purple-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-purple-800 mb-3">Enhanced Student Management</h3>
                <p className="text-purple-700 mb-4">Register students with face recognition and optimized photo storage.</p>
                <button
                  onClick={() => setActiveTab('enhanced-students')}
                  className="text-purple-600 hover:text-purple-800 font-medium"
                >
                  Go to Enhanced Students →
                </button>
              </div>

              <div className="bg-indigo-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-indigo-800 mb-3">Class Management</h3>
                <p className="text-indigo-700 mb-4">Create and manage classes, assign students, and organize your institution.</p>
                <button
                  onClick={() => setActiveTab('classes')}
                  className="text-indigo-600 hover:text-indigo-800 font-medium"
                >
                  Go to Class Management →
                </button>
              </div>

              <div className="bg-purple-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-purple-800 mb-3">Attendance Logs</h3>
                <p className="text-purple-700 mb-4">View and export attendance records, filter by date and student.</p>
                <button
                  onClick={() => setActiveTab('attendance')}
                  className="text-purple-600 hover:text-purple-800 font-medium"
                >
                  View Attendance Logs →
                </button>
              </div>

              <div className="bg-yellow-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-yellow-800 mb-3">Analytics Dashboard</h3>
                <p className="text-yellow-700 mb-4">View attendance rates, exam performance, and other key metrics.</p>
                <p className="text-sm text-yellow-600">Coming soon</p>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'students' && (
          <div className="space-y-6">
            <StudentForm
              onSuccess={() => {
                setStudentAdded(true);
                setTimeout(() => setStudentAdded(false), 3000);
              }}
            />

            {studentAdded && (
              <div className="bg-green-100 text-green-700 p-3 rounded-md">
                Student added successfully!
              </div>
            )}

            <StudentManagement />
          </div>
        )}

        {activeTab === 'attendance' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Attendance Logs</h2>
            <p className="text-gray-500">This feature is coming soon.</p>
          </div>
        )}

        {activeTab === 'exams' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Exam Management</h2>
            <p className="text-gray-500">This feature is coming soon.</p>
          </div>
        )}

        {activeTab === 'upload' && <StudentUpload />}

        {activeTab === 'classes' && (
          <ClassManagement />
        )}

        {activeTab === 'enhanced-students' && (
          <EnhancedStudentManagement />
        )}

        {activeTab === 'verification' && (
          <StudentVerification />
        )}

        {activeTab === 'invites' && isAdmin && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Invite Management</h2>
            <InviteGenerator />
          </div>
        )}
      </main>
    </div>
  );
}
