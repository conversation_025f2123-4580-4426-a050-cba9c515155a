import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ShieldExclamationIcon } from '@heroicons/react/24/outline';

export default function Forbidden() {
  const { userRole, isAdmin, isInstitute, isStudent } = useAuth();
  
  // Determine appropriate redirect based on user role
  const getRedirectPath = () => {
    if (isAdmin) return '/admin';
    if (isInstitute) return '/institute';
    if (isStudent) return '/student';
    return '/dashboard';
  };
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <ShieldExclamationIcon className="mx-auto h-16 w-16 text-red-500" />
          <h1 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            403 Forbidden
          </h1>
          <p className="mt-2 text-center text-sm text-gray-600">
            You don't have permission to access this page.
          </p>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ShieldExclamationIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Access Denied</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>
                  Your current role ({userRole || 'unknown'}) does not have permission to view this page.
                  Please contact an administrator if you believe this is an error.
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6">
          <Link
            to={getRedirectPath()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Go to your dashboard
          </Link>
        </div>
        
        <div className="mt-4">
          <Link
            to="/"
            className="text-sm font-medium text-blue-600 hover:text-blue-500"
          >
            Return to home page
          </Link>
        </div>
      </div>
    </div>
  );
}
