import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion, useAnimation } from 'framer-motion';
import { Canvas } from '@react-three/fiber';
import { useInView } from 'react-intersection-observer';
import HeroScene from '../components/welcome/HeroScene';
import FeatureCard from '../components/welcome/FeatureCard';
import TestimonialCarousel from '../components/welcome/TestimonialCarousel';
import VideoDemo from '../components/welcome/VideoDemo';
import ParticleBackground from '../components/welcome/ParticleBackground';
import Footer from '../components/welcome/Footer';

export default function Welcome() {
  const [darkMode, setDarkMode] = useState(false);
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle('dark');
  };

  const features = [
    {
      id: 1,
      title: 'AI Attendance',
      icon: '👁️',
      description: 'Automated roll calls via webcam with 98% accuracy.',
      animation: 'blink',
    },
    {
      id: 2,
      title: 'Exam Proctoring',
      icon: '⏱️',
      description: 'AI monitors tab switching, ensuring exam integrity.',
      animation: 'pulse',
    },
    {
      id: 3,
      title: 'Analytics',
      icon: '📊',
      description: 'Real-time insights on class performance.',
      animation: 'grow',
    },
  ];

  const testimonials = [
    {
      id: 1,
      quote: "Examino reduced our attendance time from 15 mins to 30 seconds!",
      author: "Dr. Sarah K.",
      institution: "University of Tech",
      verified: true,
    },
    {
      id: 2,
      quote: "The facial recognition feature has transformed how we track attendance.",
      author: "Prof. James Wilson",
      institution: "State College",
      verified: true,
    },
    {
      id: 3,
      quote: "Students love the modern interface and we love the time saved!",
      author: "Dean Robert Chen",
      institution: "Pacific Institute",
      verified: true,
    },
  ];

  const partnerLogos = [
    { id: 1, name: 'University of Tech', logo: '/logos/utech.svg' },
    { id: 2, name: 'State College', logo: '/logos/statecollege.svg' },
    { id: 3, name: 'Pacific Institute', logo: '/logos/pacific.svg' },
    { id: 4, name: 'Global Academy', logo: '/logos/global.svg' },
  ];

  return (
    <div className={`min-h-screen ${darkMode ? 'dark bg-gray-900 text-white' : 'bg-white text-gray-900'}`}>
      {/* Dark Mode Toggle */}
      <div className="fixed top-4 right-4 z-50">
        <button
          onClick={toggleDarkMode}
          className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 transition-colors"
          aria-label="Toggle dark mode"
        >
          {darkMode ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          )}
        </button>
      </div>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Canvas className="w-full h-full">
            <ambientLight intensity={0.5} />
            <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} />
            <HeroScene />
          </Canvas>
        </div>

        <div className="container mx-auto px-4 z-10 text-center">
          <motion.h1
            className="text-4xl md:text-6xl font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            Transform Education with AI-Driven Attendance & Smarter Exams
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <em>Save 80% admin time with automated facial recognition and cheat-proof online testing.</em>
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link
              to="/signup"
              className="inline-block px-8 py-4 text-lg font-semibold text-white rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 shadow-lg"
            >
              Try Examino Free →
            </Link>
            <Link
              to="/signup-with-face"
              className="inline-block px-8 py-4 text-lg font-semibold text-white rounded-lg bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg"
            >
              Sign Up with Face ID
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Key Features Section */}
      <section ref={ref} className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-center mb-16"
            initial={{ opacity: 0 }}
            animate={controls}
            variants={{
              visible: { opacity: 1, y: 0 },
              hidden: { opacity: 0, y: 50 }
            }}
            transition={{ duration: 0.5 }}
          >
            Powerful Features for Modern Education
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <FeatureCard
                key={feature.id}
                feature={feature}
                index={index}
                controls={controls}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Trusted by Leading Institutions
          </motion.h2>

          <TestimonialCarousel testimonials={testimonials} />

          <div className="mt-16 flex flex-wrap justify-center items-center gap-8">
            {partnerLogos.map(partner => (
              <motion.div
                key={partner.id}
                className="grayscale hover:grayscale-0 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
              >
                <img
                  src={partner.logo}
                  alt={partner.name}
                  className="h-12 md:h-16"
                  onError={(e) => {
                    e.target.src = 'https://via.placeholder.com/150x50?text=' + partner.name;
                  }}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Video Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            See Examino in Action
          </motion.h2>

          <VideoDemo />
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 overflow-hidden">
        <ParticleBackground />

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <motion.h2
              className="text-3xl md:text-5xl font-bold mb-8"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Ready to Modernize Your Classroom?
            </motion.h2>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/signup"
                className="inline-block px-8 py-4 text-lg font-semibold text-white rounded-lg bg-gradient-to-r from-purple-600 to-blue-500 hover:from-purple-700 hover:to-blue-600 transform hover:scale-105 transition-all duration-300 shadow-lg"
              >
                Get Started for Free
              </Link>

              <Link
                to="/signup-with-face"
                className="inline-block px-8 py-4 text-lg font-semibold text-white rounded-lg bg-gradient-to-r from-green-600 to-blue-500 hover:from-green-700 hover:to-blue-600 transform hover:scale-105 transition-all duration-300 shadow-lg"
              >
                Sign Up with Face ID
              </Link>

              <Link
                to="/demo"
                className="inline-block px-8 py-4 text-lg font-semibold rounded-lg border-2 border-purple-500 dark:border-blue-400 text-purple-600 dark:text-blue-400 hover:bg-purple-50 dark:hover:bg-gray-800 transform hover:scale-105 transition-all duration-300"
              >
                Book a Demo
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer darkMode={darkMode} />
    </div>
  );
}
