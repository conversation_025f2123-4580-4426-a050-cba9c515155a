import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import Header from '../components/Header';
import InstituteAttendanceLogs from '../components/institute/InstituteAttendanceLogs';
import StudentVerification from '../components/admin/StudentVerification';
import ClassManagement from '../components/admin/ClassManagement';

export default function InstituteDashboard() {
  const { currentUser, userProfile, userRole, isAdmin, isInstitute } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen bg-gray-100">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800">Institute Dashboard</h1>
          <div className="text-sm text-gray-600">
            Welcome, {userProfile?.name || currentUser?.email}
          </div>
        </div>
        
        {/* Tabs */}
        <div className="flex flex-wrap mb-6 border-b">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-4 py-2 font-medium ${activeTab === 'overview' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('attendance')}
            className={`px-4 py-2 font-medium ${activeTab === 'attendance' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Attendance Logs
          </button>
          <button
            onClick={() => setActiveTab('verification')}
            className={`px-4 py-2 font-medium ${activeTab === 'verification' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Face Verification
          </button>
          <button
            onClick={() => setActiveTab('classes')}
            className={`px-4 py-2 font-medium ${activeTab === 'classes' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Class Management
          </button>
          <button
            onClick={() => setActiveTab('reports')}
            className={`px-4 py-2 font-medium ${activeTab === 'reports' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
          >
            Reports
          </button>
        </div>
        
        {/* Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-blue-800 mb-3">Attendance Logs</h3>
                    <p className="text-blue-700 mb-4">View and manage student attendance records.</p>
                    <button
                      onClick={() => setActiveTab('attendance')}
                      className="text-blue-600 hover:text-blue-800 font-medium"
                    >
                      Go to Attendance Logs →
                    </button>
                  </div>
                  
                  <div className="bg-amber-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-amber-800 mb-3">Face Verification</h3>
                    <p className="text-amber-700 mb-4">Verify student identities using facial recognition and mark attendance.</p>
                    <button
                      onClick={() => setActiveTab('verification')}
                      className="text-amber-600 hover:text-amber-800 font-medium"
                    >
                      Go to Face Verification →
                    </button>
                  </div>
                  
                  <div className="bg-indigo-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-indigo-800 mb-3">Class Management</h3>
                    <p className="text-indigo-700 mb-4">Create and manage classes, assign students, and organize your institution.</p>
                    <button
                      onClick={() => setActiveTab('classes')}
                      className="text-indigo-600 hover:text-indigo-800 font-medium"
                    >
                      Go to Class Management →
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
              <div className="space-y-4">
                <div className="border-l-4 border-green-500 pl-4 py-2">
                  <p className="text-sm text-gray-600">Today</p>
                  <p className="font-medium">12 students marked present</p>
                </div>
                <div className="border-l-4 border-red-500 pl-4 py-2">
                  <p className="text-sm text-gray-600">Today</p>
                  <p className="font-medium">3 students marked absent</p>
                </div>
                <div className="border-l-4 border-blue-500 pl-4 py-2">
                  <p className="text-sm text-gray-600">Yesterday</p>
                  <p className="font-medium">New class "Computer Science 101" created</p>
                </div>
                <div className="border-l-4 border-purple-500 pl-4 py-2">
                  <p className="text-sm text-gray-600">2 days ago</p>
                  <p className="font-medium">5 new students registered</p>
                </div>
              </div>
              <button
                className="mt-4 text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                View All Activity
              </button>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Attendance Overview</h2>
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium">Today's Attendance</span>
                    <span className="text-sm font-medium">80%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div className="bg-green-600 h-2.5 rounded-full" style={{ width: '80%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium">This Week</span>
                    <span className="text-sm font-medium">75%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: '75%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium">This Month</span>
                    <span className="text-sm font-medium">82%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div className="bg-purple-600 h-2.5 rounded-full" style={{ width: '82%' }}></div>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <h3 className="text-lg font-medium mb-2">Class Attendance</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Computer Science 101</span>
                      <span className="text-sm font-medium text-green-600">92%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Mathematics 202</span>
                      <span className="text-sm font-medium text-yellow-600">78%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Physics 101</span>
                      <span className="text-sm font-medium text-red-600">65%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'attendance' && (
          <InstituteAttendanceLogs />
        )}
        
        {activeTab === 'verification' && (
          <StudentVerification />
        )}
        
        {activeTab === 'classes' && (
          <ClassManagement />
        )}
        
        {activeTab === 'reports' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Reports</h2>
            <p className="text-gray-500">This feature is coming soon.</p>
          </div>
        )}
      </div>
    </div>
  );
}
