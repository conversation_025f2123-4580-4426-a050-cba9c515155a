import React, { useState, useEffect } from 'react';
import { supabase } from '../utils/supabaseClient';

export default function Test() {
  const [tests, setTests] = useState({
    react: true,
    vite: true,
    tailwind: true,
    supabase: false,
    auth: false,
    routing: true
  });
  
  const [loading, setLoading] = useState(true);
  const [supabaseInfo, setSupabaseInfo] = useState('');

  useEffect(() => {
    // Test Supabase connection
    const testSupabase = async () => {
      try {
        // Test if supabase client is available
        if (supabase) {
          setTests(prev => ({ ...prev, supabase: true }));
          
          // Check if we're using mock or real Supabase
          const useMock = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true';
          setSupabaseInfo(useMock ? 'Mock Supabase (Development)' : 'Real Supabase');
          
          // Test auth functionality
          try {
            const { data, error } = await supabase.auth.getSession();
            setTests(prev => ({ ...prev, auth: true }));
          } catch (authError) {
            console.log('Auth test:', authError);
          }
        }
      } catch (error) {
        console.error('Supabase test failed:', error);
      } finally {
        setLoading(false);
      }
    };

    testSupabase();
  }, []);

  const testItems = [
    { key: 'react', label: 'React Framework', icon: '⚛️' },
    { key: 'vite', label: 'Vite Dev Server', icon: '⚡' },
    { key: 'tailwind', label: 'Tailwind CSS', icon: '🎨' },
    { key: 'routing', label: 'React Router', icon: '🛣️' },
    { key: 'supabase', label: 'Supabase Client', icon: '🗄️' },
    { key: 'auth', label: 'Authentication', icon: '🔐' }
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600 p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-2xl w-full">
        <h1 className="text-3xl font-bold text-center text-gray-800 mb-2">
          🎉 Examino System Status
        </h1>
        <p className="text-gray-600 text-center mb-6">
          Comprehensive system health check
        </p>
        
        {/* System Info */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-gray-700 mb-2">System Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            <div>🌐 Server: http://localhost:5174</div>
            <div>🔧 Mode: {import.meta.env.DEV ? 'Development' : 'Production'}</div>
            <div>🗄️ Database: {supabaseInfo}</div>
            <div>📦 Build Tool: Vite</div>
          </div>
        </div>

        {/* Test Results */}
        <div className="space-y-3 mb-6">
          {testItems.map(({ key, label, icon }) => (
            <div
              key={key}
              className={`flex items-center justify-between p-3 rounded-lg border ${
                tests[key]
                  ? 'bg-green-50 border-green-200 text-green-800'
                  : loading
                  ? 'bg-yellow-50 border-yellow-200 text-yellow-800'
                  : 'bg-red-50 border-red-200 text-red-800'
              }`}
            >
              <div className="flex items-center">
                <span className="text-lg mr-3">{icon}</span>
                <span className="font-medium">{label}</span>
              </div>
              <div className="flex items-center">
                {loading && key === 'supabase' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
                ) : tests[key] ? (
                  <span className="text-green-600 font-bold">✅</span>
                ) : (
                  <span className="text-red-600 font-bold">❌</span>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Navigation */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <a 
            href="/" 
            className="inline-block bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded text-center transition-colors"
          >
            🏠 Home
          </a>
          <a 
            href="/login" 
            className="inline-block bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded text-center transition-colors"
          >
            🔐 Login
          </a>
          <button 
            onClick={() => window.location.reload()}
            className="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            🔄 Refresh
          </button>
        </div>

        {/* Debug Info */}
        <details className="mt-6">
          <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
            🔍 Debug Information
          </summary>
          <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono">
            <div>Environment: {JSON.stringify({
              DEV: import.meta.env.DEV,
              VITE_USE_MOCK_SUPABASE: import.meta.env.VITE_USE_MOCK_SUPABASE,
              VITE_DEV_MODE: import.meta.env.VITE_DEV_MODE
            }, null, 2)}</div>
          </div>
        </details>
      </div>
    </div>
  );
}
