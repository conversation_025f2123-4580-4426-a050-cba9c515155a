import React from 'react';

export default function Test() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
        <h1 className="text-3xl font-bold text-center text-gray-800 mb-4">
          🎉 Examino is Working!
        </h1>
        <p className="text-gray-600 text-center mb-6">
          Your application is running successfully on localhost.
        </p>
        <div className="space-y-4">
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            ✅ React is working
          </div>
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            ✅ Vite dev server is running
          </div>
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            ✅ Tailwind CSS is working
          </div>
          <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
            🔗 Server: http://localhost:5174
          </div>
        </div>
        <div className="mt-6 text-center">
          <a 
            href="/login" 
            className="inline-block bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            Go to Login
          </a>
        </div>
      </div>
    </div>
  );
}
