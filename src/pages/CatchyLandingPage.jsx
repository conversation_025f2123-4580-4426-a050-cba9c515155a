import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Header from '../components/Header';
import Footer from '../components/welcome/Footer';
import '../styles/landing-animations.css';

export default function CatchyLandingPage() {
  // Animation controls
  const heroControls = useAnimation();
  const featuresControls = useAnimation();
  const ctaControls = useAnimation();

  // Intersection observers
  const [heroRef, heroInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [featuresRef, featuresInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [ctaRef, ctaInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  // Start animations when sections come into view
  useEffect(() => {
    if (heroInView) heroControls.start('visible');
    if (featuresInView) featuresControls.start('visible');
    if (ctaInView) ctaControls.start('visible');
  }, [heroInView, featuresInView, ctaInView, heroControls, featuresControls, ctaControls]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Examino name banner */}
      <div className="bg-gradient-to-r from-blue-900 to-indigo-900 py-3 shadow-md">
        <div className="container mx-auto px-4">
          <motion.h1
            className="text-2xl font-bold text-center"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent examino-name">EXAMINO</span>
          </motion.h1>
        </div>
      </div>

      {/* Hero Section with Animated Heading */}
      <section
        ref={heroRef}
        className="relative overflow-hidden bg-gradient-to-br from-blue-900 to-indigo-800 py-20"
      >

        <div className="container mx-auto px-4 relative z-10 mt-8">
          {/* Animated Examino Logo */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={heroControls}
            variants={{
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
            className="flex justify-center"
          >
            <img
              src="/examino-logo.svg"
              alt="Examino"
              width={300}
              height={80}
              className="float"
            />
          </motion.div>

          {/* Dynamic Tagline */}
          <motion.h1
            className="mt-6 text-center text-4xl md:text-6xl font-bold text-white"
            initial={{ opacity: 0, y: 20 }}
            animate={heroControls}
            variants={{
              visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.3 } }
            }}
          >
            Smarter <span className="text-yellow-300">Attendance</span>,
            <br />
            Fairer <span className="text-green-300">Exams</span>
          </motion.h1>

          <motion.p
            className="mt-6 text-center text-xl text-blue-100 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={heroControls}
            variants={{
              visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.5 } }
            }}
          >
            Seamless attendance tracking and secure exam management with cutting-edge facial recognition technology.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={heroControls}
            variants={{
              visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.7 } }
            }}
            className="mt-10 flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link
              to="/signup"
              className="inline-block px-8 py-4 text-lg font-semibold text-white rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 shadow-lg"
            >
              Try Examino Free →
            </Link>
            <Link
              to="/signup-with-face"
              className="inline-block px-8 py-4 text-lg font-semibold text-white rounded-lg bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg"
            >
              Sign Up with Face ID
            </Link>
          </motion.div>
        </div>

        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-10 left-10 w-20 h-20 rounded-full bg-blue-500 opacity-10"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 rounded-full bg-purple-500 opacity-10"></div>
          <div className="absolute top-1/2 left-1/4 w-40 h-40 rounded-full bg-green-500 opacity-5"></div>
        </div>
      </section>

      {/* Feature Showcase Graphics Section */}
      <section
        ref={featuresRef}
        className="py-20 bg-white"
      >
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={featuresControls}
            variants={{
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Powerful Features</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">Everything you need to streamline your educational process.</p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Face Recognition Illustration */}
            <motion.div
              className="feature-card rounded-xl overflow-hidden shadow-lg bg-white p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={featuresControls}
              variants={{
                visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.2 } }
              }}
            >
              <div className="mb-6 relative face-scan responsive-image">
                <img
                  src="/face-recognition.svg"
                  alt="Face Recognition"
                  className="w-full h-auto"
                />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Facial Recognition Attendance</h3>
              <p className="text-gray-600">1-second attendance with AI face matching. No more time-consuming roll calls.</p>
            </motion.div>

            {/* Exam Proctoring Graphic */}
            <motion.div
              className="feature-card rounded-xl overflow-hidden shadow-lg bg-white p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={featuresControls}
              variants={{
                visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.4 } }
              }}
            >
              <div className="mb-6 responsive-image">
                <img
                  src="/exam-proctoring.svg"
                  alt="Exam Proctoring"
                  className="w-full h-auto shield-pulse"
                />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Cheat-Proof Exams</h3>
              <p className="text-gray-600">Secure exams with tab monitoring and advanced proctoring features.</p>
            </motion.div>

            {/* Analytics Dashboard */}
            <motion.div
              className="feature-card rounded-xl overflow-hidden shadow-lg bg-white p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={featuresControls}
              variants={{
                visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.6 } }
              }}
            >
              <div className="mb-6 responsive-image">
                <img
                  src="/analytics-dashboard.svg"
                  alt="Analytics Dashboard"
                  className="w-full h-auto"
                />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Real-time Analytics</h3>
              <p className="text-gray-600">Instant insights into attendance patterns, exam performance, and student engagement.</p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-16">How Examino Works</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">1</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Register Your Face</h3>
              <p className="text-gray-600">Create an account and register your face for secure authentication.</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">2</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Mark Attendance</h3>
              <p className="text-gray-600">Simply look at the camera to mark your attendance in seconds.</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">3</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Take Secure Exams</h3>
              <p className="text-gray-600">Complete exams in a secure environment with anti-cheating measures.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-16">What Our Users Say</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-50 p-6 rounded-lg shadow">
              <p className="text-gray-600 mb-4">"Examino reduced our attendance time from 15 minutes to just 30 seconds. It's revolutionary!"</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-600 rounded-full mr-3"></div>
                <div>
                  <h4 className="font-bold text-gray-900">Dr. Sarah Johnson</h4>
                  <p className="text-sm text-gray-500">University Professor</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg shadow">
              <p className="text-gray-600 mb-4">"The facial recognition feature has transformed how we track attendance. No more buddy punching!"</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-purple-600 rounded-full mr-3"></div>
                <div>
                  <h4 className="font-bold text-gray-900">Prof. James Wilson</h4>
                  <p className="text-sm text-gray-500">Department Head</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg shadow">
              <p className="text-gray-600 mb-4">"Students love the modern interface and we love the time saved. It's a win-win for everyone."</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-green-600 rounded-full mr-3"></div>
                <div>
                  <h4 className="font-bold text-gray-900">Dean Robert Chen</h4>
                  <p className="text-sm text-gray-500">Academic Dean</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section with Glassmorphism */}
      <section
        ref={ctaRef}
        className="py-20 relative overflow-hidden"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-700"></div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute top-10 right-10 w-40 h-40 rounded-full bg-blue-400 opacity-10"></div>
          <div className="absolute bottom-10 left-10 w-60 h-60 rounded-full bg-purple-400 opacity-10"></div>
          <div className="absolute top-1/3 right-1/4 w-20 h-20 rounded-full bg-green-400 opacity-10 pulse"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <motion.div
            className="glassmorphism p-10 rounded-2xl max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={ctaControls}
            variants={{
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
          >
            <motion.h2
              className="text-3xl md:text-4xl font-bold text-white mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={ctaControls}
              variants={{
                visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.2 } }
              }}
            >
              Ready to Transform Your Educational Experience?
            </motion.h2>
            <motion.p
              className="text-xl text-blue-100 mb-10 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={ctaControls}
              variants={{
                visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.4 } }
              }}
            >
              Join thousands of educators and students who are already using Examino.
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={ctaControls}
              variants={{
                visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.6 } }
              }}
            >
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/signup"
                  className="inline-block px-8 py-4 text-lg font-semibold text-white rounded-lg bg-gradient-to-r from-purple-600 to-blue-500 hover:from-purple-700 hover:to-blue-600 transform hover:scale-105 transition-all duration-300 shadow-lg"
                >
                  Get Started for Free
                </Link>

                <Link
                  to="/signup-with-face"
                  className="inline-block px-8 py-4 text-lg font-semibold text-white rounded-lg bg-gradient-to-r from-green-600 to-blue-500 hover:from-green-700 hover:to-blue-600 transform hover:scale-105 transition-all duration-300 shadow-lg"
                >
                  Sign Up with Face ID
                </Link>

                <Link
                  to="/demo"
                  className="inline-block px-8 py-4 text-lg font-semibold rounded-lg border-2 border-white text-white hover:bg-white hover:bg-opacity-10 transform hover:scale-105 transition-all duration-300"
                >
                  Book a Demo
                </Link>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
