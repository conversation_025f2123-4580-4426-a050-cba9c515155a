import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../utils/supabaseClient';
import { encryptData } from '../utils/encryption';

// Components
import SignupLayout from '../components/auth/SignupLayout';
import EmailPasswordForm from '../components/auth/EmailPasswordForm';
import FaceCaptureModal from '../components/auth/FaceCaptureModal';
import ConsentCheckbox from '../components/auth/ConsentCheckbox';
import SuccessScreen from '../components/auth/SuccessScreen';

export default function SignupWithFace() {
  const [currentStep, setCurrentStep] = useState(1);
  const [userData, setUserData] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    faceDescriptor: null,
    faceImage: null
  });
  const [error, setError] = useState('');
  const navigate = useNavigate();

  // Handle form submission for step 1 (email/password)
  const handleCredentials = async (formData) => {
    try {
      setError('');

      // Update user data
      setUserData(prev => ({
        ...prev,
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName
      }));

      // Move to next step
      setCurrentStep(2);
    } catch (error) {
      console.error('Error in step 1:', error);
      setError(error.message || 'An error occurred');
    }
  };

  // Handle face capture for step 2
  const handleFaceCapture = async (faceData) => {
    try {
      setError('');

      // Check if there was an error during face capture
      if (faceData.error) {
        setError(faceData.error);
        return;
      }

      // Update user data with face descriptor and image
      setUserData(prev => ({
        ...prev,
        faceDescriptor: faceData.descriptor,
        faceImage: faceData.image
      }));

      // Move to next step
      setCurrentStep(3);
    } catch (error) {
      console.error('Error in step 2:', error);
      setError(error.message || 'An error occurred during face capture');
    }
  };

  // Handle consent submission for step 3
  const handleConsent = async (consentData) => {
    try {
      setError('');

      if (!consentData.consent) {
        setError('You must agree to the terms to continue');
        return;
      }

      // Create user account
      await createUserAccount();

      // Move to success step
      setCurrentStep(4);
    } catch (error) {
      console.error('Error in step 3:', error);
      setError(error.message || 'An error occurred while creating your account');
    }
  };

  // Create user account in Supabase
  const createUserAccount = async () => {
    try {
      // Check if we're in development mode
      const isDevelopment = import.meta.env.DEV || !import.meta.env.PROD;

      let userId;

      if (isDevelopment) {
        console.log('Development mode: Simulating user signup');
        // Generate a mock user ID
        userId = 'dev-' + Math.random().toString(36).substring(2, 15);
      } else {
        // 1. Sign up the user with Supabase Auth
        const { data: authData, error: authError } = await supabase.auth.signUp({
          email: userData.email,
          password: userData.password,
          options: {
            data: {
              first_name: userData.firstName,
              last_name: userData.lastName
            }
          }
        });

        if (authError) throw authError;

        userId = authData.user.id;
      }

      // 2. Convert face descriptor to array and encrypt it
      let encryptedDescriptor, imageUrl;

      if (isDevelopment) {
        // In development, create mock data
        console.log('Development mode: Creating mock face data');
        encryptedDescriptor = 'mock-encrypted-descriptor';
        imageUrl = 'https://example.com/mock-face-image.webp';
      } else {
        // In production, process real data
        const descriptorArray = Array.from(userData.faceDescriptor);
        encryptedDescriptor = encryptData(JSON.stringify(descriptorArray));

        // 3. Upload face image to Supabase Storage
        const fileName = `${userId}_${Date.now()}.webp`;
        const filePath = `reference_faces/${fileName}`;

        // Convert data URL to Blob
        const base64Data = userData.faceImage.split(',')[1];
        const blob = await fetch(`data:image/webp;base64,${base64Data}`).then(res => res.blob());

        // Upload to Supabase Storage
        const { error: uploadError, data: uploadData } = await supabase.storage
          .from('reference')
          .upload(filePath, blob, {
            contentType: 'image/webp',
            upsert: true
          });

        if (uploadError) throw uploadError;

        // Get the public URL
        const { data: urlData } = supabase.storage
          .from('reference')
          .getPublicUrl(filePath);

        imageUrl = urlData.publicUrl;
      }

      // 4. Create student record with face data
      if (isDevelopment) {
        console.log('Development mode: Simulating database insert');
        console.log('Mock student record created with ID:', userId);
      } else {
        const { error: studentError } = await supabase
          .from('students')
          .insert({
            id: userId,
            first_name: userData.firstName,
            last_name: userData.lastName,
            student_id: `S${Math.floor(100000 + Math.random() * 900000)}`, // Generate random student ID
            course: 'Not specified',
            semester: 'Not specified',
            face_descriptor: encryptedDescriptor,
            reference_image_url: imageUrl,
            email: userData.email,
            is_active: true
          });

        if (studentError) throw studentError;
      }

      return true;
    } catch (error) {
      console.error('Error creating user account:', error);
      throw new Error(error.message || 'Failed to create account');
    }
  };

  // Render current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <EmailPasswordForm
            onSubmit={handleCredentials}
            initialValues={userData}
          />
        );
      case 2:
        return (
          <FaceCaptureModal
            onCapture={handleFaceCapture}
            retryLimit={3}
          />
        );
      case 3:
        return (
          <ConsentCheckbox
            onSubmit={handleConsent}
            termsLink="/privacy"
            required={true}
          />
        );
      case 4:
        return (
          <SuccessScreen
            title="Account Created!"
            subtitle="Face registration complete"
            message="You can now mark attendance via face recognition."
            buttonText="Go to Login"
            buttonLink="/login"
          />
        );
      default:
        return null;
    }
  };

  return (
    <SignupLayout currentStep={currentStep}>
      {error && (
        <div className="mb-4 bg-red-100 text-red-700 p-3 rounded-md">
          {error}
        </div>
      )}
      {renderStep()}
    </SignupLayout>
  );
}
