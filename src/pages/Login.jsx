import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon, ShieldCheckIcon, AcademicCapIcon, BuildingLibraryIcon } from '@heroicons/react/24/outline';
import AuthLogger from '../utils/authLogger';
import zxcvbn from 'zxcvbn';
import PasswordStrengthMeter from '../components/PasswordStrengthMeter';
import SecureLogin from '../components/auth/SecureLogin';
import { getCSRFToken } from '../utils/authService';

export default function Login() {
  // State for form fields and UI
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [rateLimited, setRateLimited] = useState(false);
  const [selectedDemo, setSelectedDemo] = useState(null);
  const [useSecureLogin, setUseSecureLogin] = useState(false);
  const { login, ROLES } = useAuth();
  const navigate = useNavigate();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } }
  };

  // Demo accounts
  const demoAccounts = [
    {
      role: ROLES.ADMIN,
      email: '<EMAIL>',
      password: 'password123',
      icon: ShieldCheckIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      borderColor: 'border-red-200',
      description: 'Full system access'
    },
    {
      role: ROLES.INSTITUTE,
      email: '<EMAIL>',
      password: 'password123',
      icon: BuildingLibraryIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      borderColor: 'border-purple-200',
      description: 'Manage students & exams'
    },
    {
      role: ROLES.STUDENT,
      email: '<EMAIL>',
      password: 'password123',
      icon: AcademicCapIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      borderColor: 'border-blue-200',
      description: 'Take exams & attendance'
    }
  ];

  // Auto-fill demo credentials when a demo account is selected
  useEffect(() => {
    if (selectedDemo) {
      setEmail(selectedDemo.email);
      setPassword(selectedDemo.password);
    }
  }, [selectedDemo]);

  // Check if user is rate limited
  useEffect(() => {
    const checkRateLimit = async () => {
      if (email) {
        const isLimited = await AuthLogger.isRateLimited(email);
        setRateLimited(isLimited);
      }
    };

    checkRateLimit();
  }, [email]);

  // Role-based redirection after login
  const redirectByRole = {
    [ROLES.ADMIN]: '/admin/dashboard',
    [ROLES.INSTITUTE]: '/institute/portal',
    [ROLES.STUDENT]: '/student/home',
    default: '/dashboard'
  };

  // Handle form submission
  async function handleSubmit(e) {
    e.preventDefault();

    if (!email || !password) {
      return setError('Please enter both email and password');
    }

    // Check if user is rate limited
    if (rateLimited) {
      return setError('Too many failed attempts. Please try again later.');
    }

    // Block demo accounts in production
    if (process.env.NODE_ENV === 'production') {
      const demoEmails = demoAccounts.map(account => account.email);
      if (demoEmails.includes(email)) {
        await AuthLogger.logFailedLogin(email, 'Demo account in production');
        return setError('Unauthorized access. Please use your institutional credentials.');
      }
    }

    // Check password complexity in production
    if (process.env.NODE_ENV === 'production') {
      const passwordStrength = zxcvbn(password);
      if (passwordStrength.score < 3) {
        return setError('Password does not meet security requirements. Please use a stronger password.');
      }
    }

    try {
      setError('');
      setLoading(true);

      // Get CSRF token for secure login
      const csrfToken = getCSRFToken();

      // Attempt login
      const data = await login(email, password, csrfToken);

      // Log successful login
      await AuthLogger.logLogin(data.user.id, email, data.user.role);

      // Redirect based on role
      const userRole = data.user.role || 'default';
      navigate(redirectByRole[userRole] || redirectByRole.default);
    } catch (error) {
      console.error('Login error:', error);

      // Log failed login
      await AuthLogger.logFailedLogin(email, error.message);

      setError('Failed to sign in. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  }

  // Handle secure login success
  const handleSecureLoginSuccess = (user, sessionToken) => {
    // Redirect to admin dashboard
    navigate('/admin');
  };

  // Handle secure login error
  const handleSecureLoginError = (error) => {
    setError(error);
  };

  // Check if we should show secure login
  useEffect(() => {
    if (email === '<EMAIL>') {
      setUseSecureLogin(true);
    } else {
      setUseSecureLogin(false);
    }
  }, [email]);

  // Temporarily disable secure login for testing
  // if (useSecureLogin) {
  //   return (
  //     <SecureLogin
  //       onLoginSuccess={handleSecureLoginSuccess}
  //       onLoginError={handleSecureLoginError}
  //     />
  //   );
  // }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <motion.div
        className="max-w-md w-full space-y-8"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <h1 className="text-center text-3xl font-extrabold text-blue-600">Examino</h1>
          <h2 className="mt-6 text-center text-2xl font-bold text-gray-900">
            Sign in to your account
          </h2>
        </motion.div>

        {error && (
          <motion.div
            className="bg-red-100 text-red-700 p-3 rounded-md"
            variants={itemVariants}
          >
            {error}
          </motion.div>
        )}

        {rateLimited && (
          <motion.div
            className="bg-yellow-100 text-yellow-700 p-3 rounded-md"
            variants={itemVariants}
          >
            <p className="font-medium">Account temporarily locked</p>
            <p className="text-sm">Too many failed login attempts. Please try again in 15 minutes or reset your password.</p>
          </motion.div>
        )}

        <motion.form
          className="mt-8 space-y-6"
          onSubmit={handleSubmit}
          variants={itemVariants}
        >
          <div className="space-y-4">
            <div>
              <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 mb-1">
                Email address
              </label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Your institutional email"
                disabled={loading || rateLimited}
              />
            </div>
            <div className="relative">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm pr-10"
                  placeholder="Password"
                  disabled={loading || rateLimited}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>

              {/* Password strength indicator (only shown when typing) */}
              {password && (
                <PasswordStrengthMeter
                  password={password}
                  className="mt-1"
                  showText={true}
                />
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                Remember me
              </label>
            </div>

            <div className="text-sm">
              <Link to="/forgot-password" className="font-medium text-blue-600 hover:text-blue-500">
                Forgot your password?
              </Link>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading || rateLimited}
              className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 ${(loading || rateLimited) ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>

          {/* Demo accounts - only shown in development */}
          {process.env.NODE_ENV === 'development' && (
            <motion.div
              className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-md"
              variants={itemVariants}
            >
              <h3 className="font-medium text-sm text-yellow-800 mb-2">Development Access:</h3>
              <div className="space-y-2">
                {demoAccounts.map((account, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => setSelectedDemo(account)}
                    className={`w-full text-left p-2 rounded-md border transition-colors duration-200 flex items-center ${selectedDemo?.email === account.email ? account.bgColor + ' ' + account.borderColor : 'bg-white border-gray-200 hover:bg-gray-50'}`}
                  >
                    <account.icon className={`h-5 w-5 mr-2 ${account.color}`} />
                    <div>
                      <div className="text-sm font-medium">{account.role}</div>
                      <div className="text-xs text-gray-500">{account.email}</div>
                    </div>
                  </button>
                ))}
              </div>
              <p className="mt-2 text-xs text-gray-500 text-center">
                All use password: <span className="font-mono">password123</span>
              </p>
            </motion.div>
          )}
        </motion.form>

        <motion.div
          className="text-center mt-4"
          variants={itemVariants}
        >
          <p className="text-sm text-gray-600">
            Don't have an account?{' '}
            <Link to="/signup" className="font-medium text-blue-600 hover:text-blue-500 transition-colors">
              Sign up
            </Link>
          </p>
        </motion.div>

        <motion.div
          className="text-center mt-2"
          variants={itemVariants}
        >
          <Link to="/" className="text-sm text-blue-600 hover:text-blue-700 transition-colors">
            &larr; Back to Home
          </Link>
        </motion.div>
      </motion.div>
    </div>
  );
}
