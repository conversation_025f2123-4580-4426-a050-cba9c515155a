import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { supabase } from '../utils/supabaseClient';
import { useAuth } from '../contexts/AuthContext';
import Header from '../components/Header';
import WebcamModal from '../components/WebcamModal';
import { Link } from 'react-router-dom';

// Dashboard Components
import HeroWelcome from '../components/dashboard/HeroWelcome';
import AttendancePanel from '../components/dashboard/AttendancePanel';
import ExamsOverview from '../components/dashboard/ExamsOverview';
import AnalyticsWidget from '../components/dashboard/AnalyticsWidget';
import QuickActions from '../components/dashboard/QuickActions';

export default function Dashboard() {
  const { currentUser, userProfile, userRole, isAdmin, isInstitute, isStudent } = useAuth();
  const [showWebcamModal, setShowWebcamModal] = useState(false);
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeExams, setActiveExams] = useState([]);
  const [loadingExams, setLoadingExams] = useState(true);

  // Fetch recent results
  useEffect(() => {
    async function fetchResults() {
      if (!currentUser) return;

      try {
        const { data, error } = await supabase
          .from('submissions')
          .select('*')
          .eq('user_id', currentUser.id)
          .order('submitted_at', { ascending: false })
          .limit(3);

        if (error) throw error;

        const resultsData = data.map(item => ({
          id: item.id,
          ...item,
          status: item.score >= 60 ? 'Pass' : 'Fail'
        }));

        setResults(resultsData);
      } catch (error) {
        console.error('Error fetching results:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchResults();
  }, [currentUser]);

  // Check for active exams
  useEffect(() => {
    async function checkActiveExams() {
      try {
        const { data, error } = await supabase
          .from('exams')
          .select('*')
          .eq('is_active', true);

        if (error) throw error;

        setActiveExams(data || []);
      } catch (error) {
        console.error('Error checking active exams:', error);
      } finally {
        setLoadingExams(false);
      }
    }

    checkActiveExams();
  }, []);

  const handleAttendanceClick = () => {
    // Check if user has face data registered
    if (!userProfile?.reference_image_url && !userProfile?.face_descriptor && !userProfile?.referenceImage) {
      alert('Contact admin to register your face for attendance.');
      return;
    }

    // Check webcam permissions
    navigator.mediaDevices.getUserMedia({ video: true })
      .then(() => {
        setShowWebcamModal(true);
      })
      .catch(() => {
        alert('Enable camera access to mark attendance.');
      });
  };

  // Page animation variants
  const pageVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: { staggerChildren: 0.2 }
    },
    exit: { opacity: 0 }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <motion.main
        className="max-w-7xl mx-auto px-4 py-6 space-y-6"
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageVariants}
      >
        {/* Hero Welcome Section */}
        <HeroWelcome
          attendanceRate={82}
          examsCount={activeExams?.length || 3}
          pendingActions={2}
        />

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AttendancePanel />
          <ExamsOverview activeExams={activeExams} />
          <AnalyticsWidget />
        </div>

        {/* Quick Actions */}
        <QuickActions />

        {/* Role-Specific Dashboard Section */}
        <div className="bg-white rounded-xl shadow-md p-6 space-y-4">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Role-Based Access</h2>

          {/* Role-specific content */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Admin Panel - Only visible to admins */}
            <div className={`p-4 rounded-lg border ${isAdmin ? 'bg-red-50 border-red-200' : 'bg-gray-50 border-gray-200 opacity-50'}`}>
              <h3 className="font-medium text-gray-800 mb-2">Admin Dashboard</h3>
              <p className="text-sm text-gray-600 mb-3">
                Manage users, create institutes, and configure system settings.
              </p>
              {isAdmin ? (
                <Link to="/admin" className="text-sm text-red-600 hover:text-red-800 font-medium">
                  Access Admin Panel →
                </Link>
              ) : (
                <span className="text-sm text-gray-500">Requires admin role</span>
              )}
            </div>

            {/* Institute Panel - Only visible to institutes */}
            <div className={`p-4 rounded-lg border ${isInstitute ? 'bg-purple-50 border-purple-200' : 'bg-gray-50 border-gray-200 opacity-50'}`}>
              <h3 className="font-medium text-gray-800 mb-2">Institute Dashboard</h3>
              <p className="text-sm text-gray-600 mb-3">
                Manage students, create exams, and view analytics for your institute.
              </p>
              {isInstitute ? (
                <Link to="/institute" className="text-sm text-purple-600 hover:text-purple-800 font-medium">
                  Access Institute Panel →
                </Link>
              ) : (
                <span className="text-sm text-gray-500">Requires institute role</span>
              )}
            </div>

            {/* Student Panel - Only visible to students */}
            <div className={`p-4 rounded-lg border ${isStudent ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200 opacity-50'}`}>
              <h3 className="font-medium text-gray-800 mb-2">Student Dashboard</h3>
              <p className="text-sm text-gray-600 mb-3">
                Take exams, mark attendance, and view your academic progress.
              </p>
              {isStudent ? (
                <Link to="/student" className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                  Access Student Panel →
                </Link>
              ) : (
                <span className="text-sm text-gray-500">Requires student role</span>
              )}
            </div>
          </div>

          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-lg text-sm">
            <p className="font-medium">Current Role: {userRole || 'Unknown'}</p>
            <p className="mt-1">You only have access to features appropriate for your role.</p>
          </div>
        </div>
      </motion.main>

      {/* Webcam Modal */}
      {showWebcamModal && (
        <WebcamModal onClose={() => setShowWebcamModal(false)} />
      )}
    </div>
  );
}
