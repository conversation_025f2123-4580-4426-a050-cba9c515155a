export default function Simple() {
  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      backgroundColor: '#f0f0f0',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        textAlign: 'center',
        maxWidth: '500px'
      }}>
        <h1 style={{ color: '#333', marginBottom: '1rem' }}>
          🎉 Examino is Working!
        </h1>
        <p style={{ color: '#666', marginBottom: '1.5rem' }}>
          Your React application is running successfully.
        </p>
        <div style={{ marginBottom: '1rem' }}>
          <div style={{ 
            backgroundColor: '#d4edda', 
            color: '#155724', 
            padding: '0.5rem', 
            borderRadius: '4px',
            marginBottom: '0.5rem'
          }}>
            ✅ React is working
          </div>
          <div style={{ 
            backgroundColor: '#d4edda', 
            color: '#155724', 
            padding: '0.5rem', 
            borderRadius: '4px',
            marginBottom: '0.5rem'
          }}>
            ✅ Vite dev server is running
          </div>
          <div style={{ 
            backgroundColor: '#d1ecf1', 
            color: '#0c5460', 
            padding: '0.5rem', 
            borderRadius: '4px'
          }}>
            🌐 Server: http://localhost:5174
          </div>
        </div>
        <div>
          <a 
            href="/" 
            style={{
              display: 'inline-block',
              backgroundColor: '#007bff',
              color: 'white',
              padding: '0.5rem 1rem',
              textDecoration: 'none',
              borderRadius: '4px',
              marginRight: '0.5rem'
            }}
          >
            🏠 Home
          </a>
          <a 
            href="/login" 
            style={{
              display: 'inline-block',
              backgroundColor: '#28a745',
              color: 'white',
              padding: '0.5rem 1rem',
              textDecoration: 'none',
              borderRadius: '4px'
            }}
          >
            🔐 Login
          </a>
        </div>
      </div>
    </div>
  );
}
