/**
 * Mock Supabase Client
 * Provides a mock implementation of the Supabase client for development and testing
 */

// Helper function to generate UUID
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Mock data
const mockData = {
  users: [
    { id: '1', email: '<EMAIL>', role_id: '1', name: 'Admin User' },
    { id: '2', email: '<EMAIL>', role_id: '2', name: 'Institute User' },
    { id: '3', email: '<EMAIL>', role_id: '3', name: 'Student User' }
  ],
  user_roles: [
    { role_id: '1', role_name: 'admin' },
    { role_id: '2', role_name: 'institute' },
    { role_id: '3', role_name: 'student' }
  ],
  students: [
    { id: '1', first_name: '<PERSON>', last_name: '<PERSON><PERSON>', student_id: 'S1001', class_id: '1', reference_image_url: 'https://randomuser.me/api/portraits/men/1.jpg' },
    { id: '2', first_name: 'Jane', last_name: 'Smith', student_id: 'S1002', class_id: '1', reference_image_url: 'https://randomuser.me/api/portraits/women/1.jpg' },
    { id: '3', first_name: 'Bob', last_name: 'Johnson', student_id: 'S1003', class_id: '2', reference_image_url: 'https://randomuser.me/api/portraits/men/2.jpg' }
  ],
  classes: [
    { id: '1', name: 'Computer Science 101', instructor_id: '2' },
    { id: '2', name: 'Mathematics 202', instructor_id: '2' }
  ],
  exams: [
    { id: '1', name: 'Midterm Exam', date: '2023-12-15', status: 'published', class_id: '1' },
    { id: '2', name: 'Final Exam', date: '2023-12-30', status: 'draft', class_id: '1' }
  ],
  attendance: [
    { id: '1', student_id: '1', class_id: '1', date: '2023-12-01', status: true, verification_method: 'face', created_at: '2023-12-01T09:00:00Z' },
    { id: '2', student_id: '2', class_id: '1', date: '2023-12-01', status: true, verification_method: 'face', created_at: '2023-12-01T09:05:00Z' },
    { id: '3', student_id: '3', class_id: '2', date: '2023-12-01', status: false, verification_method: 'manual', created_at: '2023-12-01T09:10:00Z' }
  ],
  attendance_logs: [
    { id: '1', institute_id: '2', student_id: '1', class_id: '1', date: '2023-12-01', status: true, verification_method: 'face', created_at: '2023-12-01T09:00:00Z' },
    { id: '2', institute_id: '2', student_id: '2', class_id: '1', date: '2023-12-01', status: true, verification_method: 'face', created_at: '2023-12-01T09:05:00Z' },
    { id: '3', institute_id: '2', student_id: '3', class_id: '2', date: '2023-12-01', status: false, verification_method: 'manual', created_at: '2023-12-01T09:10:00Z' }
  ]
};

// Mock event listeners
const eventListeners = {};

// Helper function to filter data based on query
const filterData = (table, filters = {}) => {
  let filteredData = [...(mockData[table] || [])];

  // Apply filters
  Object.entries(filters).forEach(([key, value]) => {
    if (key === 'gte') {
      const [field, fieldValue] = Object.entries(value)[0];
      filteredData = filteredData.filter(item => item[field] >= fieldValue);
    } else if (key === 'lte') {
      const [field, fieldValue] = Object.entries(value)[0];
      filteredData = filteredData.filter(item => item[field] <= fieldValue);
    } else if (key === 'eq') {
      const [field, fieldValue] = Object.entries(value)[0];
      filteredData = filteredData.filter(item => item[field] === fieldValue);
    } else if (key === 'neq') {
      const [field, fieldValue] = Object.entries(value)[0];
      filteredData = filteredData.filter(item => item[field] !== fieldValue);
    } else if (key === 'order') {
      const { column, ascending } = value;
      filteredData.sort((a, b) => {
        if (ascending) {
          return a[column] > b[column] ? 1 : -1;
        } else {
          return a[column] < b[column] ? 1 : -1;
        }
      });
    } else if (key === 'limit') {
      filteredData = filteredData.slice(0, value);
    }
  });

  return filteredData;
};

/**
 * Create a mock Supabase client
 * @returns {Object} - Mock Supabase client
 */
export const createMockSupabaseClient = () => {
  console.log('Using mock Supabase client');

  // Mock authentication
  const mockAuth = {
    signInWithPassword: async ({ email, password }) => {
      const user = mockData.users.find(u => u.email === email);
      if (user && password === 'password123') {
        return { data: { user }, error: null };
      }
      return { data: null, error: { message: 'Invalid login credentials' } };
    },
    signOut: async () => {
      return { error: null };
    },
    getUser: () => {
      // Return a mock user (admin by default)
      return { data: { user: mockData.users[0] } };
    },
    user: () => mockData.users[0]
  };

  // Mock database operations
  return {
    auth: mockAuth,
    from: (table) => {
      let query = {};
      let selectedColumns = '*';

      const builder = {
        select: (columns) => {
          selectedColumns = columns;
          return builder;
        },
        eq: (column, value) => {
          query.eq = { [column]: value };
          return builder;
        },
        neq: (column, value) => {
          query.neq = { [column]: value };
          return builder;
        },
        gte: (column, value) => {
          query.gte = { [column]: value };
          return builder;
        },
        lte: (column, value) => {
          query.lte = { [column]: value };
          return builder;
        },
        order: (column, { ascending = true } = {}) => {
          query.order = { column, ascending };
          return builder;
        },
        limit: (count) => {
          query.limit = count;
          return builder;
        },
        single: () => {
          const filteredData = filterData(table, query);
          const item = filteredData.length > 0 ? filteredData[0] : null;
          return { data: item, error: null };
        },
        then: (callback) => {
          const filteredData = filterData(table, query);
          return callback({ data: filteredData, error: null });
        },
        insert: (items) => {
          return {
            select: (returnColumns) => {
              return {
                single: () => {
                  const newItem = Array.isArray(items) ? items[0] : items;
                  newItem.id = newItem.id || generateUUID();
                  newItem.created_at = newItem.created_at || new Date().toISOString();
                  mockData[table] = [...(mockData[table] || []), newItem];

                  // Trigger real-time events
                  if (eventListeners[table]) {
                    eventListeners[table].forEach(listener => {
                      if (listener.event === 'INSERT' || listener.event === '*') {
                        listener.callback({
                          eventType: 'INSERT',
                          new: newItem
                        });
                      }
                    });
                  }

                  return { data: newItem, error: null };
                }
              };
            },
            then: (callback) => {
              const newItems = Array.isArray(items) ? items : [items];
              newItems.forEach(item => {
                item.id = item.id || generateUUID();
                item.created_at = item.created_at || new Date().toISOString();
                mockData[table] = [...(mockData[table] || []), item];

                // Trigger real-time events
                if (eventListeners[table]) {
                  eventListeners[table].forEach(listener => {
                    if (listener.event === 'INSERT' || listener.event === '*') {
                      listener.callback({
                        eventType: 'INSERT',
                        new: item
                      });
                    }
                  });
                }
              });
              return callback({ data: newItems, error: null });
            }
          };
        },
        update: (updates) => {
          return {
            eq: (column, value) => {
              return {
                then: (callback) => {
                  const index = mockData[table]?.findIndex(item => item[column] === value);
                  if (index !== -1) {
                    const oldItem = { ...mockData[table][index] };
                    mockData[table][index] = { ...oldItem, ...updates, updated_at: new Date().toISOString() };

                    // Trigger real-time events
                    if (eventListeners[table]) {
                      eventListeners[table].forEach(listener => {
                        if (listener.event === 'UPDATE' || listener.event === '*') {
                          listener.callback({
                            eventType: 'UPDATE',
                            old: oldItem,
                            new: mockData[table][index]
                          });
                        }
                      });
                    }

                    return callback({ data: mockData[table][index], error: null });
                  }
                  return callback({ data: null, error: null });
                }
              };
            }
          };
        },
        delete: () => {
          return {
            eq: (column, value) => {
              return {
                then: (callback) => {
                  const deletedItems = mockData[table]?.filter(item => item[column] === value) || [];
                  mockData[table] = mockData[table]?.filter(item => item[column] !== value) || [];

                  // Trigger real-time events
                  if (eventListeners[table] && deletedItems.length > 0) {
                    deletedItems.forEach(deletedItem => {
                      eventListeners[table].forEach(listener => {
                        if (listener.event === 'DELETE' || listener.event === '*') {
                          listener.callback({
                            eventType: 'DELETE',
                            old: deletedItem
                          });
                        }
                      });
                    });
                  }

                  return callback({ data: null, error: null });
                }
              };
            }
          };
        }
      };

      return builder;
    },
    storage: {
      from: (bucket) => ({
        upload: (path, file) => {
          return { data: { path }, error: null };
        },
        getPublicUrl: (path) => {
          // Use randomuser.me for placeholder images instead of via.placeholder.com
          return { data: { publicUrl: `https://randomuser.me/api/portraits/men/${Math.floor(Math.random() * 100)}.jpg` } };
        }
      })
    },
    channel: (channelName) => {
      const channel = {
        on: (event, filter, callback) => {
          const { table } = filter;
          if (!eventListeners[table]) {
            eventListeners[table] = [];
          }
          eventListeners[table].push({
            channelName,
            event: filter.event,
            callback
          });
          return channel;
        },
        subscribe: () => {
          return channelName;
        }
      };
      return channel;
    },
    removeChannel: (channelName) => {
      // Remove all listeners for this channel
      Object.keys(eventListeners).forEach(table => {
        eventListeners[table] = eventListeners[table].filter(
          listener => listener.channelName !== channelName
        );
      });
    },
    rpc: (functionName, params) => {
      // Mock RPC functions
      if (functionName === 'get_institute_attendance_statistics') {
        return {
          data: [
            {
              total_records: 10,
              present_count: 8,
              absent_count: 2,
              attendance_rate: 80,
              class_id: '1',
              class_name: 'Computer Science 101',
              date: '2023-12-01'
            },
            {
              total_records: 5,
              present_count: 3,
              absent_count: 2,
              attendance_rate: 60,
              class_id: '2',
              class_name: 'Mathematics 202',
              date: '2023-12-01'
            }
          ],
          error: null
        };
      } else if (functionName === 'get_institute_daily_attendance_summary') {
        return {
          data: [
            {
              date: '2023-12-01',
              total_students: 15,
              present_count: 11,
              absent_count: 4,
              attendance_rate: 73.33
            },
            {
              date: '2023-11-30',
              total_students: 15,
              present_count: 13,
              absent_count: 2,
              attendance_rate: 86.67
            }
          ],
          error: null
        };
      } else if (functionName === 'get_institute_student_attendance_summary') {
        return {
          data: mockData.students.map(student => ({
            student_id: student.id,
            student_name: `${student.first_name} ${student.last_name}`,
            student_id_text: student.student_id,
            total_days: 10,
            present_days: Math.floor(Math.random() * 11),
            absent_days: 0,
            attendance_rate: 0
          })).map(item => ({
            ...item,
            absent_days: 10 - item.present_days,
            attendance_rate: (item.present_days / 10) * 100
          })),
          error: null
        };
      }

      return { data: null, error: { message: `RPC function ${functionName} not implemented in mock client` } };
    }
  };
};
