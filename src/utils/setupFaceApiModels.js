/**
 * Face-API.js Model Setup Utility
 * Helper to download and setup models for development
 */

import { setupLocalModels } from './faceApiModelManager';

/**
 * Setup face-api.js models for development
 */
export const setupModelsForDevelopment = async () => {
  console.log('🚀 Setting up face-api.js models for development...');
  
  try {
    const result = await setupLocalModels();
    
    if (result.success) {
      console.log('✅ Face-api.js models setup completed successfully!');
      console.log(`📊 Downloaded ${result.downloaded}/${result.total} model files`);
      
      // Show setup instructions
      console.log('\n📋 Setup Instructions:');
      console.log('1. Models are now available from CDN fallback');
      console.log('2. For local development, you can optionally download models to /public/models/');
      console.log('3. The system will automatically use CDN fallback if local models are not available');
      console.log('4. Mock detection is available as a last resort fallback');
      
      return true;
    } else {
      console.warn('⚠️ Some models failed to setup, but CDN fallback is available');
      return false;
    }
  } catch (error) {
    console.error('❌ Model setup failed:', error);
    return false;
  }
};

/**
 * Test face detection with current setup
 */
export const testFaceDetection = async () => {
  console.log('🧪 Testing face detection with current setup...');
  
  try {
    // Create a test image with a face-like pattern
    const canvas = document.createElement('canvas');
    canvas.width = 640;
    canvas.height = 480;
    const ctx = canvas.getContext('2d');
    
    // Background
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 640, 480);
    
    // Face
    ctx.fillStyle = '#ddbea9';
    ctx.beginPath();
    ctx.arc(320, 240, 80, 0, 2 * Math.PI);
    ctx.fill();
    
    // Eyes
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.arc(300, 220, 8, 0, 2 * Math.PI);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(340, 220, 8, 0, 2 * Math.PI);
    ctx.fill();
    
    // Mouth
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(320, 260, 20, 0, Math.PI);
    ctx.stroke();
    
    // Convert to image element
    const imageElement = await new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.src = canvas.toDataURL();
    });
    
    // Test face detection
    const { detectFacesWithFallback } = await import('./faceApiModelManager');
    const result = await detectFacesWithFallback(imageElement);
    
    if (result.success) {
      console.log('✅ Face detection test successful!');
      console.log(`Method used: ${result.method}`);
      console.log(`Detection score: ${(result.detectionScore * 100).toFixed(1)}%`);
      console.log(`Face box:`, result.faceBox);
      return true;
    } else {
      console.log('❌ Face detection test failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Face detection test error:', error);
    return false;
  }
};

/**
 * Show model status and recommendations
 */
export const showModelStatus = async () => {
  console.log('📊 Face-API.js Model Status:');
  console.log('============================');
  
  try {
    const { getModelStatus } = await import('./faceApiModelManager');
    const status = getModelStatus();
    
    console.log(`Models Loaded: ${status.loaded ? '✅' : '❌'}`);
    console.log(`Models Loading: ${status.loading ? '🔄' : '❌'}`);
    console.log(`Models Available: ${status.available ? '✅' : '❌'}`);
    
    if (!status.loaded && !status.loading) {
      console.log('\n💡 Recommendations:');
      console.log('1. Run: window.setupFaceApiModels.setupModelsForDevelopment()');
      console.log('2. Or wait for automatic CDN fallback');
      console.log('3. Mock detection is available as last resort');
    }
    
    if (status.loaded) {
      console.log('\n🎉 Models are ready for face detection!');
    }
    
    return status;
  } catch (error) {
    console.error('❌ Failed to get model status:', error);
    return null;
  }
};

/**
 * Quick fix for face detection issues
 */
export const quickFixFaceDetection = async () => {
  console.log('🔧 Quick Fix: Setting up face detection...');
  
  try {
    // 1. Setup models
    console.log('Step 1: Setting up models...');
    await setupModelsForDevelopment();
    
    // 2. Test detection
    console.log('Step 2: Testing face detection...');
    const testResult = await testFaceDetection();
    
    // 3. Show status
    console.log('Step 3: Checking final status...');
    await showModelStatus();
    
    if (testResult) {
      console.log('\n🎉 Quick fix completed successfully!');
      console.log('Face detection should now work properly.');
      return true;
    } else {
      console.log('\n⚠️ Quick fix completed with warnings.');
      console.log('Face detection will use fallback methods.');
      return false;
    }
  } catch (error) {
    console.error('❌ Quick fix failed:', error);
    return false;
  }
};

// Make functions available globally for browser console
if (typeof window !== 'undefined') {
  window.setupFaceApiModels = {
    setupModelsForDevelopment,
    testFaceDetection,
    showModelStatus,
    quickFixFaceDetection
  };
  
  console.log('🔧 Face-API.js model setup utilities loaded!');
  console.log('Available commands:');
  console.log('- window.setupFaceApiModels.quickFixFaceDetection()');
  console.log('- window.setupFaceApiModels.setupModelsForDevelopment()');
  console.log('- window.setupFaceApiModels.testFaceDetection()');
  console.log('- window.setupFaceApiModels.showModelStatus()');
}
