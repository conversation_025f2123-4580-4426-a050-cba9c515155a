/**
 * Optimized Face Registration Performance Testing
 * Comprehensive testing for <1s face registration pipeline
 */

import { 
  registerFaceOptimized,
  compressImageFast,
  detectFaceOptimized,
  uploadToOptimizedStorage,
  getPerformanceMetrics,
  cleanup
} from './optimizedFaceRegistration';

/**
 * Performance benchmarking configuration
 */
const BENCHMARK_CONFIG = {
  TARGET_TIME: 1000, // 1 second target
  ITERATIONS: 10,
  IMAGE_SIZES: [
    { width: 640, height: 480, name: 'VGA' },
    { width: 1280, height: 720, name: 'HD' },
    { width: 1920, height: 1080, name: 'FHD' }
  ],
  QUALITY_LEVELS: [0.6, 0.8, 0.9]
};

/**
 * Create test images of different sizes
 */
function createTestImage(width, height, withFace = true) {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    // Background
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, width, height);
    
    if (withFace) {
      // Draw a face-like shape
      const centerX = width / 2;
      const centerY = height / 2;
      const faceRadius = Math.min(width, height) / 6;
      
      // Face
      ctx.fillStyle = '#ddbea9';
      ctx.beginPath();
      ctx.arc(centerX, centerY, faceRadius, 0, 2 * Math.PI);
      ctx.fill();
      
      // Eyes
      ctx.fillStyle = '#000';
      ctx.beginPath();
      ctx.arc(centerX - faceRadius/3, centerY - faceRadius/3, faceRadius/8, 0, 2 * Math.PI);
      ctx.fill();
      ctx.beginPath();
      ctx.arc(centerX + faceRadius/3, centerY - faceRadius/3, faceRadius/8, 0, 2 * Math.PI);
      ctx.fill();
      
      // Mouth
      ctx.beginPath();
      ctx.arc(centerX, centerY + faceRadius/3, faceRadius/4, 0, Math.PI);
      ctx.stroke();
    }
    
    canvas.toBlob(resolve, 'image/jpeg', 0.9);
  });
}

/**
 * Test image compression performance
 */
export const testCompressionPerformance = async () => {
  console.log('🧪 Testing Image Compression Performance...');
  
  const results = [];
  
  for (const size of BENCHMARK_CONFIG.IMAGE_SIZES) {
    for (const quality of BENCHMARK_CONFIG.QUALITY_LEVELS) {
      console.log(`Testing ${size.name} (${size.width}x${size.height}) at ${quality * 100}% quality`);
      
      const testImage = await createTestImage(size.width, size.height);
      const testFile = new File([testImage], `test_${size.name}.jpg`, { type: 'image/jpeg' });
      
      const times = [];
      
      for (let i = 0; i < BENCHMARK_CONFIG.ITERATIONS; i++) {
        const result = await compressImageFast(testFile, 640, 480, quality);
        if (result.success) {
          times.push(result.processingTime);
        }
      }
      
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);
      
      results.push({
        size: size.name,
        dimensions: `${size.width}x${size.height}`,
        quality: quality * 100,
        originalSize: testFile.size,
        avgTime: avgTime.toFixed(0),
        minTime: minTime.toFixed(0),
        maxTime: maxTime.toFixed(0),
        targetMet: avgTime < 200 // 200ms target for compression
      });
      
      console.log(`${size.name} @ ${quality * 100}%: ${avgTime.toFixed(0)}ms avg (${minTime.toFixed(0)}-${maxTime.toFixed(0)}ms)`);
    }
  }
  
  console.log('✅ Compression performance test completed');
  return results;
};

/**
 * Test face detection performance
 */
export const testFaceDetectionPerformance = async () => {
  console.log('🧪 Testing Face Detection Performance...');
  
  const results = [];
  const scenarios = [
    { name: 'Single Face', faces: 1 },
    { name: 'No Face', faces: 0 },
    { name: 'Multiple Faces', faces: 2 }
  ];
  
  for (const scenario of scenarios) {
    console.log(`Testing ${scenario.name} scenario`);
    
    // Create test image based on scenario
    const canvas = document.createElement('canvas');
    canvas.width = 640;
    canvas.height = 480;
    const ctx = canvas.getContext('2d');
    
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 640, 480);
    
    if (scenario.faces > 0) {
      for (let i = 0; i < scenario.faces; i++) {
        const x = 200 + (i * 200);
        const y = 240;
        
        ctx.fillStyle = '#ddbea9';
        ctx.beginPath();
        ctx.arc(x, y, 60, 0, 2 * Math.PI);
        ctx.fill();
      }
    }
    
    const testBlob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/jpeg', 0.8);
    });
    
    const times = [];
    
    for (let i = 0; i < BENCHMARK_CONFIG.ITERATIONS; i++) {
      const result = await detectFaceOptimized(testBlob);
      if (result.success !== undefined) {
        times.push(result.processingTime);
      }
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    
    results.push({
      scenario: scenario.name,
      expectedFaces: scenario.faces,
      avgTime: avgTime.toFixed(0),
      targetMet: avgTime < 600 // 600ms target for detection
    });
    
    console.log(`${scenario.name}: ${avgTime.toFixed(0)}ms avg`);
  }
  
  console.log('✅ Face detection performance test completed');
  return results;
};

/**
 * Test upload performance
 */
export const testUploadPerformance = async () => {
  console.log('🧪 Testing Upload Performance...');
  
  const results = [];
  const fileSizes = [
    { size: 100 * 1024, name: '100KB' },
    { size: 500 * 1024, name: '500KB' },
    { size: 1024 * 1024, name: '1MB' }
  ];
  
  for (const fileSize of fileSizes) {
    console.log(`Testing ${fileSize.name} upload`);
    
    // Create test blob of specific size
    const testData = new Uint8Array(fileSize.size);
    const testBlob = new Blob([testData], { type: 'image/webp' });
    const fileName = `test_${fileSize.name}_${Date.now()}.webp`;
    
    const times = [];
    
    for (let i = 0; i < Math.min(BENCHMARK_CONFIG.ITERATIONS, 5); i++) { // Fewer iterations for uploads
      const result = await uploadToOptimizedStorage(testBlob, fileName, 'TEST001');
      if (result.success) {
        times.push(result.uploadTime);
      }
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    
    results.push({
      fileSize: fileSize.name,
      bytes: fileSize.size,
      avgTime: avgTime.toFixed(0),
      targetMet: avgTime < 300 // 300ms target for upload
    });
    
    console.log(`${fileSize.name}: ${avgTime.toFixed(0)}ms avg`);
  }
  
  console.log('✅ Upload performance test completed');
  return results;
};

/**
 * Test complete registration pipeline
 */
export const testCompleteRegistrationPipeline = async () => {
  console.log('🧪 Testing Complete Registration Pipeline...');
  
  const results = [];
  
  for (let i = 0; i < BENCHMARK_CONFIG.ITERATIONS; i++) {
    console.log(`Pipeline test ${i + 1}/${BENCHMARK_CONFIG.ITERATIONS}`);
    
    // Create test data
    const studentData = {
      studentId: `TEST_${Date.now()}_${i}`,
      name: `Test Student ${i}`,
      email: `test${i}@example.com`
    };
    
    const testImage = await createTestImage(1280, 720, true);
    const testFile = new File([testImage], `test_${i}.jpg`, { type: 'image/jpeg' });
    
    // Run complete pipeline
    const result = await registerFaceOptimized(studentData, testFile);
    
    if (result.success) {
      results.push({
        iteration: i + 1,
        totalTime: result.performance.totalTime,
        compressionTime: result.performance.compressionTime,
        detectionTime: result.performance.detectionTime,
        uploadTime: result.performance.uploadTime,
        databaseTime: result.performance.databaseTime,
        targetAchieved: result.performance.targetAchieved,
        compressionRatio: result.optimization.compressionRatio,
        method: result.optimization.method
      });
      
      console.log(`Test ${i + 1}: ${result.performance.totalTime.toFixed(0)}ms ${result.performance.targetAchieved ? '✅' : '❌'}`);
    } else {
      console.log(`Test ${i + 1}: Failed - ${result.error}`);
    }
  }
  
  // Calculate statistics
  const successfulTests = results.filter(r => r.targetAchieved);
  const avgTotalTime = results.reduce((a, b) => a + b.totalTime, 0) / results.length;
  const successRate = (successfulTests.length / results.length) * 100;
  
  console.log(`✅ Pipeline test completed: ${successRate.toFixed(1)}% success rate, ${avgTotalTime.toFixed(0)}ms avg`);
  
  return {
    results,
    statistics: {
      totalTests: results.length,
      successfulTests: successfulTests.length,
      successRate: successRate.toFixed(1),
      avgTotalTime: avgTotalTime.toFixed(0),
      targetAchievementRate: successRate
    }
  };
};

/**
 * Test worker vs main thread performance
 */
export const testWorkerVsMainThread = async () => {
  console.log('🧪 Testing Worker vs Main Thread Performance...');
  
  const testImage = await createTestImage(640, 480, true);
  const testBlob = new Blob([testImage], { type: 'image/jpeg' });
  
  const workerTimes = [];
  const mainThreadTimes = [];
  
  // Test worker performance
  for (let i = 0; i < 5; i++) {
    const result = await detectFaceOptimized(testBlob);
    if (result.method === 'worker') {
      workerTimes.push(result.processingTime);
    }
  }
  
  // Test main thread performance (fallback)
  // Note: This would require implementing a main thread version
  
  const avgWorkerTime = workerTimes.length > 0 
    ? workerTimes.reduce((a, b) => a + b, 0) / workerTimes.length 
    : 0;
  
  console.log(`Worker performance: ${avgWorkerTime.toFixed(0)}ms avg`);
  console.log('✅ Worker vs main thread test completed');
  
  return {
    workerAvgTime: avgWorkerTime.toFixed(0),
    workerTests: workerTimes.length,
    workerSupported: workerTimes.length > 0
  };
};

/**
 * Load testing simulation
 */
export const testLoadPerformance = async (concurrentUsers = 5) => {
  console.log(`🧪 Testing Load Performance (${concurrentUsers} concurrent users)...`);
  
  const promises = [];
  
  for (let i = 0; i < concurrentUsers; i++) {
    const promise = (async () => {
      const studentData = {
        studentId: `LOAD_TEST_${Date.now()}_${i}`,
        name: `Load Test User ${i}`,
        email: `loadtest${i}@example.com`
      };
      
      const testImage = await createTestImage(640, 480, true);
      const testFile = new File([testImage], `load_test_${i}.jpg`, { type: 'image/jpeg' });
      
      const startTime = performance.now();
      const result = await registerFaceOptimized(studentData, testFile);
      const endTime = performance.now();
      
      return {
        userId: i,
        success: result.success,
        totalTime: endTime - startTime,
        targetAchieved: result.success && result.performance.targetAchieved
      };
    })();
    
    promises.push(promise);
  }
  
  const results = await Promise.all(promises);
  
  const successfulResults = results.filter(r => r.success);
  const avgTime = successfulResults.reduce((a, b) => a + b.totalTime, 0) / successfulResults.length;
  const successRate = (successfulResults.length / results.length) * 100;
  
  console.log(`Load test completed: ${successRate.toFixed(1)}% success rate, ${avgTime.toFixed(0)}ms avg`);
  
  return {
    concurrentUsers,
    results,
    successRate: successRate.toFixed(1),
    avgTime: avgTime.toFixed(0),
    successfulUsers: successfulResults.length
  };
};

/**
 * Run comprehensive performance test suite
 */
export const runOptimizedRegistrationTests = async () => {
  console.log('🚀 Running Comprehensive Optimized Registration Tests...');
  console.log('=======================================================');
  
  const testResults = {};
  
  try {
    // System capabilities
    console.log('\n📊 System Capabilities:');
    const metrics = getPerformanceMetrics();
    console.log('Worker Support:', metrics.workerSupported);
    console.log('WebP Support:', metrics.webpSupported);
    console.log('OffscreenCanvas Support:', metrics.offscreenCanvasSupported);
    testResults.systemCapabilities = metrics;
    
    // Compression performance
    console.log('\n📦 Compression Performance:');
    testResults.compression = await testCompressionPerformance();
    
    // Face detection performance
    console.log('\n🔍 Face Detection Performance:');
    testResults.faceDetection = await testFaceDetectionPerformance();
    
    // Upload performance
    console.log('\n☁️ Upload Performance:');
    testResults.upload = await testUploadPerformance();
    
    // Complete pipeline
    console.log('\n🔄 Complete Pipeline Performance:');
    testResults.pipeline = await testCompleteRegistrationPipeline();
    
    // Worker vs main thread
    console.log('\n⚡ Worker vs Main Thread:');
    testResults.workerComparison = await testWorkerVsMainThread();
    
    // Load testing
    console.log('\n🏋️ Load Testing:');
    testResults.loadTest = await testLoadPerformance(3);
    
    console.log('\n📊 Performance Test Summary:');
    console.log('============================');
    console.log(`Pipeline Success Rate: ${testResults.pipeline.statistics.successRate}%`);
    console.log(`Average Total Time: ${testResults.pipeline.statistics.avgTotalTime}ms`);
    console.log(`Target Achievement Rate: ${testResults.pipeline.statistics.targetAchievementRate}%`);
    console.log(`Load Test Success Rate: ${testResults.loadTest.successRate}%`);
    
    if (parseFloat(testResults.pipeline.statistics.avgTotalTime) < BENCHMARK_CONFIG.TARGET_TIME) {
      console.log('🎉 Performance target achieved!');
    } else {
      console.log('⚠️ Performance target not met consistently');
    }
    
    return testResults;
    
  } catch (error) {
    console.error('❌ Performance testing failed:', error);
    return { error: error.message };
  } finally {
    cleanup();
  }
};

// Make test functions available globally for browser console
if (typeof window !== 'undefined') {
  window.testOptimizedRegistration = {
    testCompressionPerformance,
    testFaceDetectionPerformance,
    testUploadPerformance,
    testCompleteRegistrationPipeline,
    testWorkerVsMainThread,
    testLoadPerformance,
    runOptimizedRegistrationTests
  };
  
  console.log('🔧 Optimized registration test utilities loaded!');
  console.log('Available commands:');
  console.log('- window.testOptimizedRegistration.runOptimizedRegistrationTests()');
  console.log('- window.testOptimizedRegistration.testCompleteRegistrationPipeline()');
  console.log('- window.testOptimizedRegistration.testLoadPerformance(5)');
}
