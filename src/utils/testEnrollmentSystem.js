/**
 * Student Enrollment System Test Utilities
 * Comprehensive testing for the student face enrollment with manual identity system
 */

import { 
  enrollStudentWithFace,
  enableManualOverride,
  getEnrollmentStatistics,
  getStudentEnrollmentHistory,
  validateEnrollmentData,
  validateImageFile,
  createImageElement,
  detectFacesInImage,
  uploadStudentFaceImage,
  ensureModelsLoaded
} from './studentEnrollmentService';

/**
 * Test enrollment data validation
 */
export const testEnrollmentDataValidation = () => {
  console.log('🧪 Testing Enrollment Data Validation...');
  
  try {
    // Test valid data
    const validData = {
      studentId: 'E22273735500014',
      name: 'Anupam',
      email: '<EMAIL>'
    };
    
    const validResult = validateEnrollmentData(validData);
    console.log('Valid data test:', validResult);
    
    // Test invalid data
    const invalidData = {
      studentId: '',
      name: '',
      email: 'invalid-email'
    };
    
    const invalidResult = validateEnrollmentData(invalidData);
    console.log('Invalid data test:', invalidResult);
    
    console.log('✅ Enrollment data validation tests completed');
    return true;
  } catch (error) {
    console.error('❌ Enrollment data validation test failed:', error);
    return false;
  }
};

/**
 * Test image file validation for enrollment
 */
export const testEnrollmentImageValidation = () => {
  console.log('🧪 Testing Enrollment Image Validation...');
  
  try {
    // Test valid image file
    const validFile = new File(['test'], 'student.jpg', { type: 'image/jpeg' });
    const validResult = validateImageFile(validFile);
    console.log('Valid image file test:', validResult);
    
    // Test invalid file type
    const invalidFile = new File(['test'], 'document.pdf', { type: 'application/pdf' });
    const invalidResult = validateImageFile(invalidFile);
    console.log('Invalid file type test:', invalidResult);
    
    // Test large file
    const largeFile = new File([new ArrayBuffer(6 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });
    const largeResult = validateImageFile(largeFile);
    console.log('Large file test:', largeResult);
    
    // Test null file
    const nullResult = validateImageFile(null);
    console.log('Null file test:', nullResult);
    
    console.log('✅ Enrollment image validation tests completed');
    return true;
  } catch (error) {
    console.error('❌ Enrollment image validation test failed:', error);
    return false;
  }
};

/**
 * Test student face image upload to student_faces bucket
 */
export const testStudentFaceImageUpload = async () => {
  console.log('🧪 Testing Student Face Image Upload...');
  
  try {
    // Create a test image
    const canvas = document.createElement('canvas');
    canvas.width = 300;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');
    
    // Draw a face-like shape
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 300, 300);
    ctx.fillStyle = '#333';
    ctx.beginPath();
    ctx.arc(150, 150, 80, 0, 2 * Math.PI);
    ctx.fill();
    
    // Convert to blob
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/jpeg', 0.8);
    });
    
    const testFile = new File([blob], 'test_student.jpg', { type: 'image/jpeg' });
    
    // Test upload to student_faces bucket
    const uploadResult = await uploadStudentFaceImage(testFile, 'E22273735500014');
    
    if (uploadResult.success) {
      console.log('✅ Student face image upload successful');
      console.log('Upload URL:', uploadResult.url);
      console.log('Storage path:', uploadResult.path);
    } else {
      console.log('⚠️ Student face image upload failed:', uploadResult.error);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Student face image upload test failed:', error);
    return false;
  }
};

/**
 * Test complete student enrollment workflow
 */
export const testCompleteStudentEnrollment = async () => {
  console.log('🧪 Testing Complete Student Enrollment Workflow...');
  
  try {
    // Test data
    const studentData = {
      studentId: 'E' + Date.now(),
      name: 'Test Student',
      email: '<EMAIL>'
    };
    
    // Create test image with a face
    const canvas = document.createElement('canvas');
    canvas.width = 400;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');
    
    // Draw a clear face
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 400, 300);
    ctx.fillStyle = '#333';
    ctx.beginPath();
    ctx.arc(200, 150, 80, 0, 2 * Math.PI);
    ctx.fill();
    
    // Add eyes
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.arc(180, 130, 10, 0, 2 * Math.PI);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(220, 130, 10, 0, 2 * Math.PI);
    ctx.fill();
    
    const dataUrl = canvas.toDataURL('image/jpeg');
    
    console.log('Testing enrollment for:', studentData.studentId);
    
    // Test enrollment
    const result = await enrollStudentWithFace(studentData, dataUrl);
    
    console.log('Enrollment result:', {
      success: result.success,
      message: result.message,
      status: result.enrollmentStatus,
      studentId: result.studentId,
      facesDetected: result.facesDetected,
      processingTime: result.processingTimeMs + 'ms'
    });
    
    if (result.success) {
      console.log('✅ Complete student enrollment workflow successful');
      console.log('Student enrolled with ID:', result.studentId);
      console.log('Image URL:', result.imageUrl);
    } else {
      console.log('⚠️ Enrollment failed (may be expected):', result.message);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Complete student enrollment test failed:', error);
    return false;
  }
};

/**
 * Test enrollment scenarios (success, no face, multiple faces, poor quality)
 */
export const testEnrollmentScenarios = async () => {
  console.log('🧪 Testing Different Enrollment Scenarios...');
  
  try {
    const scenarios = [
      {
        name: 'Valid Single Face',
        studentData: {
          studentId: 'E_VALID_' + Date.now(),
          name: 'Valid Student',
          email: '<EMAIL>'
        },
        setup: (ctx, canvas) => {
          ctx.fillStyle = '#f0f0f0';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.fillStyle = '#333';
          ctx.beginPath();
          ctx.arc(200, 150, 80, 0, 2 * Math.PI);
          ctx.fill();
        },
        expectedStatus: 'success'
      },
      {
        name: 'Multiple Faces',
        studentData: {
          studentId: 'E_MULTI_' + Date.now(),
          name: 'Multiple Student',
          email: '<EMAIL>'
        },
        setup: (ctx, canvas) => {
          ctx.fillStyle = '#f0f0f0';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.fillStyle = '#333';
          // First face
          ctx.beginPath();
          ctx.arc(150, 150, 60, 0, 2 * Math.PI);
          ctx.fill();
          // Second face
          ctx.beginPath();
          ctx.arc(300, 150, 60, 0, 2 * Math.PI);
          ctx.fill();
        },
        expectedStatus: 'multiple_faces'
      },
      {
        name: 'No Face',
        studentData: {
          studentId: 'E_NOFACE_' + Date.now(),
          name: 'No Face Student',
          email: '<EMAIL>'
        },
        setup: (ctx, canvas) => {
          ctx.fillStyle = '#f0f0f0';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.fillStyle = '#333';
          ctx.fillRect(100, 100, 200, 100); // Rectangle instead of face
        },
        expectedStatus: 'no_face'
      }
    ];
    
    for (const scenario of scenarios) {
      console.log(`Testing scenario: ${scenario.name}`);
      
      const canvas = document.createElement('canvas');
      canvas.width = 400;
      canvas.height = 300;
      const ctx = canvas.getContext('2d');
      
      scenario.setup(ctx, canvas);
      
      const dataUrl = canvas.toDataURL('image/jpeg');
      
      const result = await enrollStudentWithFace(scenario.studentData, dataUrl);
      
      console.log(`${scenario.name} result:`, {
        success: result.success,
        status: result.enrollmentStatus,
        message: result.message,
        expected: scenario.expectedStatus
      });
      
      // Check if result matches expectation (in mock mode)
      const statusMatches = result.enrollmentStatus === scenario.expectedStatus || 
                           (scenario.expectedStatus === 'success' && result.success);
      
      console.log(`Status match: ${statusMatches ? '✅' : '⚠️'}`);
    }
    
    console.log('✅ Enrollment scenarios testing completed');
    return true;
  } catch (error) {
    console.error('❌ Enrollment scenarios test failed:', error);
    return false;
  }
};

/**
 * Test manual override functionality
 */
export const testManualOverride = async () => {
  console.log('🧪 Testing Manual Override Functionality...');
  
  try {
    const studentId = 'E_OVERRIDE_' + Date.now();
    const imageUrl = 'mock://override_image.jpg';
    const overrideReason = 'Image quality is acceptable despite low detection score';
    
    console.log('Testing manual override for student:', studentId);
    
    const result = await enableManualOverride(studentId, imageUrl, overrideReason);
    
    if (result.success) {
      console.log('✅ Manual override successful');
      console.log('Override reason:', result.overrideReason);
    } else {
      console.log('⚠️ Manual override failed:', result.error);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Manual override test failed:', error);
    return false;
  }
};

/**
 * Test enrollment statistics
 */
export const testEnrollmentStatistics = async () => {
  console.log('🧪 Testing Enrollment Statistics...');
  
  try {
    const statsResult = await getEnrollmentStatistics(30);
    
    if (statsResult.success) {
      console.log('✅ Enrollment statistics retrieved successfully');
      console.log('Statistics:', {
        totalAttempts: statsResult.stats.total_attempts,
        successfulEnrollments: statsResult.stats.successful_enrollments,
        successRate: statsResult.stats.success_rate + '%',
        avgDetectionScore: (statsResult.stats.avg_detection_score * 100).toFixed(1) + '%',
        avgProcessingTime: statsResult.stats.avg_processing_time_ms + 'ms'
      });
    } else {
      console.log('⚠️ Failed to get enrollment statistics:', statsResult.error);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Enrollment statistics test failed:', error);
    return false;
  }
};

/**
 * Test student enrollment history
 */
export const testEnrollmentHistory = async () => {
  console.log('🧪 Testing Student Enrollment History...');
  
  try {
    const historyResult = await getStudentEnrollmentHistory('E22273735500014');
    
    if (historyResult.success) {
      console.log('✅ Enrollment history retrieved successfully');
      console.log('History entries:', historyResult.history.length);
      
      if (historyResult.history.length > 0) {
        console.log('Sample entry:', {
          name: historyResult.history[0].name,
          email: historyResult.history[0].email,
          status: historyResult.history[0].enrollment_status,
          facesDetected: historyResult.history[0].faces_detected
        });
      }
    } else {
      console.log('⚠️ Failed to get enrollment history:', historyResult.error);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Enrollment history test failed:', error);
    return false;
  }
};

/**
 * Test expected response messages
 */
export const testResponseMessages = async () => {
  console.log('🧪 Testing Expected Response Messages...');
  
  try {
    const testCases = [
      {
        name: 'Success Case',
        facesDetected: 1,
        expectedMessage: 'Student enrolled successfully'
      },
      {
        name: 'No Face Case',
        facesDetected: 0,
        expectedMessage: 'No face found, please try again.'
      },
      {
        name: 'Multiple Faces Case',
        facesDetected: 2,
        expectedMessage: 'Multiple faces detected. Please upload a photo with only one face.'
      }
    ];
    
    for (const testCase of testCases) {
      console.log(`Testing ${testCase.name}:`);
      
      // Create mock image based on test case
      const canvas = document.createElement('canvas');
      canvas.width = 400;
      canvas.height = 300;
      const ctx = canvas.getContext('2d');
      
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, 400, 300);
      
      if (testCase.facesDetected > 0) {
        ctx.fillStyle = '#333';
        for (let i = 0; i < testCase.facesDetected; i++) {
          ctx.beginPath();
          ctx.arc(150 + (i * 100), 150, 60, 0, 2 * Math.PI);
          ctx.fill();
        }
      }
      
      const dataUrl = canvas.toDataURL('image/jpeg');
      const studentData = {
        studentId: `E_MSG_${testCase.name.replace(/\s+/g, '_')}_${Date.now()}`,
        name: `Test ${testCase.name}`,
        email: '<EMAIL>'
      };
      
      const result = await enrollStudentWithFace(studentData, dataUrl);
      
      const messageMatches = result.message.includes(testCase.expectedMessage.split('.')[0]);
      
      console.log(`Expected: "${testCase.expectedMessage}"`);
      console.log(`Received: "${result.message}"`);
      console.log(`Message match: ${messageMatches ? '✅' : '⚠️'}`);
    }
    
    console.log('✅ Response messages testing completed');
    return true;
  } catch (error) {
    console.error('❌ Response messages test failed:', error);
    return false;
  }
};

/**
 * Run comprehensive enrollment system tests
 */
export const runEnrollmentSystemTests = async () => {
  console.log('🚀 Running Comprehensive Student Enrollment Tests...');
  console.log('=====================================================');
  
  const tests = [
    { name: 'Enrollment Data Validation', test: testEnrollmentDataValidation },
    { name: 'Enrollment Image Validation', test: testEnrollmentImageValidation },
    { name: 'Student Face Image Upload', test: testStudentFaceImageUpload },
    { name: 'Complete Student Enrollment', test: testCompleteStudentEnrollment },
    { name: 'Enrollment Scenarios', test: testEnrollmentScenarios },
    { name: 'Manual Override', test: testManualOverride },
    { name: 'Enrollment Statistics', test: testEnrollmentStatistics },
    { name: 'Enrollment History', test: testEnrollmentHistory },
    { name: 'Response Messages', test: testResponseMessages }
  ];
  
  const results = [];
  
  for (const { name, test } of tests) {
    console.log(`\n📋 Running ${name} test...`);
    const result = await test();
    results.push({ name, passed: result });
    console.log(`${result ? '✅' : '❌'} ${name}: ${result ? 'PASSED' : 'FAILED'}`);
  }
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(({ name, passed }) => {
    console.log(`${passed ? '✅' : '❌'} ${name}`);
  });
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All enrollment system tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Check the implementation.');
  }
  
  return passed === total;
};

// Make test functions available globally for browser console
if (typeof window !== 'undefined') {
  window.testEnrollmentSystem = {
    testEnrollmentDataValidation,
    testEnrollmentImageValidation,
    testStudentFaceImageUpload,
    testCompleteStudentEnrollment,
    testEnrollmentScenarios,
    testManualOverride,
    testEnrollmentStatistics,
    testEnrollmentHistory,
    testResponseMessages,
    runEnrollmentSystemTests
  };
  
  console.log('🔧 Student enrollment test utilities loaded!');
  console.log('Available commands:');
  console.log('- window.testEnrollmentSystem.runEnrollmentSystemTests()');
  console.log('- window.testEnrollmentSystem.testCompleteStudentEnrollment()');
  console.log('- window.testEnrollmentSystem.testEnrollmentScenarios()');
  console.log('- window.testEnrollmentSystem.testResponseMessages()');
}
