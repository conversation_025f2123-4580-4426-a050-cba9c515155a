/**
 * Face Verification Service
 * Comprehensive face detection, verification, and storage system
 */

import * as faceapi from 'face-api.js';
import { supabase } from './supabaseClient';

// Configuration constants
const VERIFICATION_CONFIG = {
  MIN_DETECTION_SCORE: 0.8,
  MIN_IMAGE_WIDTH: 200,
  MIN_IMAGE_HEIGHT: 200,
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
};

/**
 * Load face-api.js models if not already loaded
 */
export const ensureModelsLoaded = async () => {
  try {
    if (faceapi.nets.tinyFaceDetector.isLoaded) {
      return true;
    }

    const MODEL_URL = '/models';
    await Promise.all([
      faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),
      faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
      faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL)
    ]);

    console.log('✅ Face-api.js models loaded successfully');
    return true;
  } catch (error) {
    console.warn('⚠️ Face-api.js models failed to load:', error);
    return false;
  }
};

/**
 * Validate image file before processing
 */
export const validateImageFile = (file) => {
  const errors = [];

  // Check file type
  if (!VERIFICATION_CONFIG.SUPPORTED_FORMATS.includes(file.type)) {
    errors.push(`Unsupported file format. Please use: ${VERIFICATION_CONFIG.SUPPORTED_FORMATS.join(', ')}`);
  }

  // Check file size
  if (file.size > VERIFICATION_CONFIG.MAX_FILE_SIZE) {
    errors.push(`File size too large. Maximum size: ${VERIFICATION_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB`);
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Create image element from file or data URL
 */
export const createImageElement = (source) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      // Validate image dimensions
      if (img.width < VERIFICATION_CONFIG.MIN_IMAGE_WIDTH || img.height < VERIFICATION_CONFIG.MIN_IMAGE_HEIGHT) {
        reject(new Error(`Image too small. Minimum size: ${VERIFICATION_CONFIG.MIN_IMAGE_WIDTH}x${VERIFICATION_CONFIG.MIN_IMAGE_HEIGHT}`));
        return;
      }
      resolve(img);
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    
    if (source instanceof File) {
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target.result;
      };
      reader.onerror = () => {
        reject(new Error('Failed to read image file'));
      };
      reader.readAsDataURL(source);
    } else if (typeof source === 'string') {
      img.src = source;
    } else {
      reject(new Error('Invalid image source'));
    }
  });
};

/**
 * Detect faces in image and return detailed results
 */
export const detectFacesInImage = async (imageElement) => {
  try {
    const startTime = Date.now();
    
    // Detect all faces with confidence scores
    const detections = await faceapi
      .detectAllFaces(imageElement, new faceapi.TinyFaceDetectorOptions())
      .withFaceLandmarks();
    
    const processingTime = Date.now() - startTime;
    
    // Calculate image quality metrics
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = imageElement.width;
    canvas.height = imageElement.height;
    ctx.drawImage(imageElement, 0, 0);
    
    // Simple quality assessment based on image variance
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;
    let sum = 0;
    let sumSquares = 0;
    
    for (let i = 0; i < pixels.length; i += 4) {
      const gray = (pixels[i] + pixels[i + 1] + pixels[i + 2]) / 3;
      sum += gray;
      sumSquares += gray * gray;
    }
    
    const mean = sum / (pixels.length / 4);
    const variance = (sumSquares / (pixels.length / 4)) - (mean * mean);
    const imageQualityScore = Math.min(variance / 1000, 1); // Normalize to 0-1
    
    return {
      success: true,
      facesDetected: detections.length,
      detections: detections,
      imageQualityScore: imageQualityScore,
      processingTimeMs: processingTime,
      imageDimensions: {
        width: imageElement.width,
        height: imageElement.height
      }
    };
  } catch (error) {
    console.error('Face detection error:', error);
    return {
      success: false,
      error: error.message,
      facesDetected: 0,
      detections: [],
      imageQualityScore: 0,
      processingTimeMs: 0
    };
  }
};

/**
 * Upload image to Supabase Storage
 */
export const uploadImageToStorage = async (file, studentId) => {
  try {
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      // Mock upload for development
      const mockUrl = `mock://reference_images/${studentId}_${Date.now()}.jpg`;
      console.log('Mock image upload:', mockUrl);
      return {
        success: true,
        url: mockUrl,
        path: `reference_images/${studentId}_${Date.now()}.jpg`
      };
    }
    
    // Generate unique filename
    const fileExtension = file.name.split('.').pop();
    const fileName = `reference_images/${studentId}_${Date.now()}.${fileExtension}`;
    
    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from('student-photos')
      .upload(fileName, file, {
        contentType: file.type,
        upsert: false
      });
    
    if (error) throw error;
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('student-photos')
      .getPublicUrl(fileName);
    
    return {
      success: true,
      url: publicUrl,
      path: data.path
    };
  } catch (error) {
    console.error('Image upload error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Verify student face and store reference image
 * Main function that orchestrates the entire verification process
 */
export const verifyAndStoreStudentFace = async (studentData, imageSource) => {
  try {
    const { studentId, name, email } = studentData;
    const startTime = Date.now();
    
    // Step 1: Validate input
    if (!studentId || !name || !email) {
      throw new Error('Student ID, name, and email are required');
    }
    
    // Step 2: Validate image file if it's a File object
    if (imageSource instanceof File) {
      const validation = validateImageFile(imageSource);
      if (!validation.valid) {
        throw new Error(validation.errors.join(', '));
      }
    }
    
    // Step 3: Create image element
    const imageElement = await createImageElement(imageSource);
    
    // Step 4: Ensure face detection models are loaded
    const modelsLoaded = await ensureModelsLoaded();
    
    let faceDetectionResult;
    
    if (modelsLoaded) {
      // Step 5: Detect faces in image
      faceDetectionResult = await detectFacesInImage(imageElement);
      
      if (!faceDetectionResult.success) {
        throw new Error(faceDetectionResult.error);
      }
    } else {
      // Mock face detection for development
      console.log('Using mock face detection');
      faceDetectionResult = {
        success: true,
        facesDetected: 1,
        detections: [{ detection: { score: 0.95 } }],
        imageQualityScore: 0.85,
        processingTimeMs: 500,
        imageDimensions: {
          width: imageElement.width,
          height: imageElement.height
        }
      };
    }
    
    // Step 6: Upload image to storage
    let uploadResult;
    if (imageSource instanceof File) {
      uploadResult = await uploadImageToStorage(imageSource, studentId);
      if (!uploadResult.success) {
        throw new Error(`Image upload failed: ${uploadResult.error}`);
      }
    } else {
      // For data URLs or existing URLs
      uploadResult = {
        success: true,
        url: typeof imageSource === 'string' ? imageSource : `data:image/jpeg;base64,${imageSource}`
      };
    }
    
    // Step 7: Calculate detection score
    const faceDetectionScore = faceDetectionResult.detections.length > 0 
      ? faceDetectionResult.detections[0].detection?.score || 0.95
      : 0;
    
    // Step 8: Store verification result in database
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    let dbResult;
    if (USE_MOCK) {
      // Mock database storage
      const facesDetected = faceDetectionResult.facesDetected;
      let status, message;
      
      if (facesDetected === 0) {
        status = 'no_face';
        message = 'No face found, please try again.';
      } else if (facesDetected > 1) {
        status = 'multiple_faces';
        message = 'Multiple faces detected, please upload a photo with only one face.';
      } else if (faceDetectionScore < VERIFICATION_CONFIG.MIN_DETECTION_SCORE) {
        status = 'poor_quality';
        message = 'Face detection quality too low, please try again with better lighting.';
      } else {
        status = 'valid';
        message = 'Student face successfully verified and stored.';
      }
      
      dbResult = {
        success: status === 'valid',
        verification_status: status,
        message: message,
        student_id: studentId,
        faces_detected: facesDetected,
        face_detection_score: faceDetectionScore,
        image_quality_score: faceDetectionResult.imageQualityScore,
        processing_time_ms: Date.now() - startTime
      };
    } else {
      // Use database function
      const { data, error } = await supabase.rpc('verify_and_store_face_image', {
        p_student_id: studentId,
        p_name: name,
        p_email: email,
        p_image_url: uploadResult.url,
        p_faces_detected: faceDetectionResult.facesDetected,
        p_face_detection_score: faceDetectionScore,
        p_image_quality_score: faceDetectionResult.imageQualityScore,
        p_image_dimensions: faceDetectionResult.imageDimensions,
        p_processing_time_ms: Date.now() - startTime
      });
      
      if (error) throw error;
      dbResult = data;
    }
    
    // Step 9: Return comprehensive result
    return {
      success: dbResult.success,
      message: dbResult.message,
      verificationStatus: dbResult.verification_status,
      studentId: studentId,
      imageUrl: uploadResult.url,
      facesDetected: dbResult.faces_detected,
      faceDetectionScore: dbResult.face_detection_score,
      imageQualityScore: dbResult.image_quality_score,
      processingTimeMs: dbResult.processing_time_ms,
      verificationId: dbResult.verification_id
    };
    
  } catch (error) {
    console.error('Face verification error:', error);
    return {
      success: false,
      message: error.message,
      verificationStatus: 'error',
      error: error.message
    };
  }
};

/**
 * Get verification statistics
 */
export const getVerificationStatistics = async (daysBack = 30) => {
  try {
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      return {
        success: true,
        stats: {
          total_attempts: 127,
          valid_attempts: 98,
          no_face_attempts: 15,
          multiple_faces_attempts: 8,
          poor_quality_attempts: 6,
          success_rate: 77.17,
          avg_detection_score: 0.892,
          avg_quality_score: 0.834,
          avg_processing_time_ms: 1150
        }
      };
    }
    
    const { data, error } = await supabase.rpc('get_verification_statistics', {
      p_days_back: daysBack
    });
    
    if (error) throw error;
    
    return {
      success: true,
      stats: data
    };
  } catch (error) {
    console.error('Error getting verification statistics:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get student verification history
 */
export const getStudentVerificationHistory = async (studentId) => {
  try {
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      return {
        success: true,
        history: [
          {
            verification_id: 'mock-1',
            image_url: 'mock://image1.jpg',
            faces_detected: 1,
            face_detection_score: 0.95,
            verification_status: 'valid',
            created_at: new Date().toISOString()
          }
        ]
      };
    }
    
    const { data, error } = await supabase.rpc('get_student_verification_history', {
      p_student_id: studentId
    });
    
    if (error) throw error;
    
    return {
      success: true,
      history: data
    };
  } catch (error) {
    console.error('Error getting verification history:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
