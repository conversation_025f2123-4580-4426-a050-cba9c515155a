import { supabase } from './supabaseClient';

/**
 * Utility for logging authentication events
 */
export const AuthLogger = {
  /**
   * Log an authentication event
   *
   * @param {string} userId - User ID (null for failed attempts)
   * @param {string} event - Event type (login, logout, signup, etc.)
   * @param {string} status - Status (success, failed)
   * @param {Object} metadata - Additional metadata
   */
  async logEvent(userId, event, status, metadata = {}) {
    try {
      // Don't log events in development mode
      if (import.meta.env.DEV) {
        console.log(`[Auth Log] ${event} - ${status}`, { userId, ...metadata });
        return;
      }

      // Get client IP address (in a real app, this would be done server-side)
      const ipAddress = await this.getClientIP();

      // Log to Supabase
      const { error } = await supabase
        .from('auth_logs')
        .insert({
          user_id: userId,
          event,
          status,
          ip_address: ipAddress,
          metadata: JSON.stringify(metadata),
          timestamp: new Date().toISOString()
        });

      if (error) {
        console.error('Error logging auth event:', error);
      }
    } catch (error) {
      console.error('Error in auth logger:', error);
    }
  },

  /**
   * Log a successful login
   *
   * @param {string} userId - User ID
   * @param {string} email - User email
   * @param {string} role - User role
   */
  async logLogin(userId, email, role) {
    await this.logEvent(userId, 'login', 'success', { email, role });
  },

  /**
   * Log a failed login attempt
   *
   * @param {string} email - Email used in attempt
   * @param {string} reason - Reason for failure
   */
  async logFailedLogin(email, reason) {
    await this.logEvent(null, 'login', 'failed', { email, reason });
  },

  /**
   * Log a logout event
   *
   * @param {string} userId - User ID
   */
  async logLogout(userId) {
    await this.logEvent(userId, 'logout', 'success');
  },

  /**
   * Log a signup event
   *
   * @param {string} userId - User ID
   * @param {string} email - User email
   * @param {string} role - User role
   */
  async logSignup(userId, email, role) {
    await this.logEvent(userId, 'signup', 'success', { email, role });
  },

  /**
   * Get client IP address
   * In a real app, this would be done server-side
   *
   * @returns {Promise<string>} - Client IP address
   */
  async getClientIP() {
    try {
      // This is a public API that returns the client's IP
      // In a real app, this would be handled server-side
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      console.error('Error getting client IP:', error);
      return '0.0.0.0';
    }
  },

  /**
   * Check if user is rate limited
   * In a real app, this would query a rate limiting service
   *
   * @param {string} email - User email
   * @returns {Promise<boolean>} - True if rate limited
   */
  async isRateLimited(email) {
    try {
      // In development, never rate limit unless explicitly testing
      if (import.meta.env.DEV && !import.meta.env.VITE_TEST_RATE_LIMIT) {
        return false;
      }

      // In a real app, this would query a rate limiting service
      // For now, we'll just check if there have been 5+ failed attempts in the last 15 minutes
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000).toISOString();

      const { data, error } = await supabase
        .from('auth_logs')
        .select('*')
        .eq('status', 'failed')
        .eq('event', 'login')
        .gte('timestamp', fifteenMinutesAgo)
        .filter('metadata', 'cs', `{"email":"${email}"}`);

      if (error) {
        console.error('Error checking rate limit:', error);
        return false;
      }

      // Rate limit after 5 failed attempts
      const failedAttempts = data.length;

      // Log rate limit status for monitoring
      if (failedAttempts >= 3) {
        console.warn(`Rate limit warning for ${email}: ${failedAttempts} failed attempts`);
      }

      return failedAttempts >= 5;
    } catch (error) {
      console.error('Error in rate limit check:', error);
      return false;
    }
  }
};

export default AuthLogger;
