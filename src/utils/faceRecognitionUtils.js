/**
 * Face Recognition Utilities
 * Provides functions for face detection, recognition, and verification
 */
import * as faceapi from 'face-api.js';
import pako from 'pako';
import { supabase } from './supabaseClient';

// Configuration
const FACE_MATCH_THRESHOLD = 0.6; // Lower values are more strict (0-1)
const MODELS_PATH = '/models';

/**
 * Load face-api.js models
 * @returns {Promise<boolean>} - Whether models were loaded successfully
 */
export const loadFaceModels = async () => {
  try {
    // Check if we're in development mode
    const isDevelopment = import.meta.env.DEV || !import.meta.env.PROD;
    
    if (isDevelopment && !import.meta.env.VITE_FORCE_FACE_MODELS) {
      console.log('Development mode detected. Using mock face detection.');
      return true;
    }
    
    // Check if models are already loaded
    if (faceapi.nets.tinyFaceDetector.isLoaded && 
        faceapi.nets.faceRecognitionNet.isLoaded && 
        faceapi.nets.faceLandmark68Net.isLoaded) {
      return true;
    }
    
    // Load models
    await Promise.all([
      faceapi.nets.tinyFaceDetector.loadFromUri(MODELS_PATH),
      faceapi.nets.faceRecognitionNet.loadFromUri(MODELS_PATH),
      faceapi.nets.faceLandmark68Net.loadFromUri(MODELS_PATH)
    ]);
    
    console.log('Face-api models loaded successfully');
    return true;
  } catch (error) {
    console.error('Error loading face-api models:', error);
    return false;
  }
};

/**
 * Detect and encode a face from an image or video element
 * @param {HTMLImageElement|HTMLVideoElement|HTMLCanvasElement} imageElement - Image element containing a face
 * @returns {Promise<{descriptor: Float32Array, detection: Object, quality: number}|null>} - Face data or null if no face detected
 */
export const detectAndEncodeFace = async (imageElement) => {
  try {
    // Ensure models are loaded
    await loadFaceModels();
    
    // Check if we're in development mode
    const isDevelopment = import.meta.env.DEV || !import.meta.env.PROD;
    
    if (isDevelopment && !import.meta.env.VITE_FORCE_FACE_MODELS) {
      console.log('Development mode detected. Using mock face detection.');
      // In development, return a mock descriptor
      return {
        descriptor: new Float32Array(128).fill(0.5), // Mock descriptor with 128 elements
        detection: { detection: { box: { x: 0, y: 0, width: 100, height: 100 } } },
        quality: 0.95
      };
    }
    
    // Detect face with landmarks and descriptor
    const detection = await faceapi.detectSingleFace(
      imageElement, 
      new faceapi.TinyFaceDetectorOptions()
    )
    .withFaceLandmarks()
    .withFaceDescriptor();
    
    if (!detection) {
      console.warn('No face detected in the image');
      return null;
    }
    
    // Calculate image quality
    const quality = calculateFaceQuality(imageElement, detection);
    
    return {
      descriptor: detection.descriptor,
      detection,
      quality
    };
  } catch (error) {
    console.error('Error detecting face:', error);
    return null;
  }
};

/**
 * Calculate the quality of a face image
 * @param {HTMLImageElement|HTMLVideoElement|HTMLCanvasElement} imageElement - Image element
 * @param {Object} detection - Face detection result from face-api.js
 * @returns {number} - Quality score (0-1)
 */
export const calculateFaceQuality = (imageElement, detection) => {
  try {
    const { width, height } = imageElement;
    const { box } = detection.detection;
    
    // Calculate face size relative to image
    const faceArea = box.width * box.height;
    const imageArea = width * height;
    const faceRatio = faceArea / imageArea;
    
    // Calculate face position (center is best)
    const faceCenterX = box.x + box.width / 2;
    const faceCenterY = box.y + box.height / 2;
    const imageCenterX = width / 2;
    const imageCenterY = height / 2;
    
    // Calculate distance from center (normalized)
    const maxDistance = Math.sqrt(Math.pow(width / 2, 2) + Math.pow(height / 2, 2));
    const distance = Math.sqrt(Math.pow(faceCenterX - imageCenterX, 2) + Math.pow(faceCenterY - imageCenterY, 2));
    const normalizedDistance = 1 - (distance / maxDistance);
    
    // Calculate aspect ratio score (closer to 1:1 is better)
    const aspectRatio = box.width / box.height;
    const aspectRatioScore = 1 - Math.min(Math.abs(aspectRatio - 1), 0.5) * 2;
    
    // Calculate final quality score (weighted average)
    const sizeWeight = 0.5;
    const positionWeight = 0.3;
    const aspectRatioWeight = 0.2;
    
    // Size score: optimal is 15-25% of image area
    let sizeScore;
    if (faceRatio < 0.05) {
      // Face too small
      sizeScore = faceRatio * 20;
    } else if (faceRatio > 0.6) {
      // Face too large
      sizeScore = Math.max(0, 1 - ((faceRatio - 0.6) * 2.5));
    } else {
      // Face size is good
      sizeScore = 1;
    }
    
    return (
      sizeScore * sizeWeight +
      normalizedDistance * positionWeight +
      aspectRatioScore * aspectRatioWeight
    );
  } catch (error) {
    console.error('Error calculating face quality:', error);
    return 0;
  }
};

/**
 * Compare two face descriptors and return the similarity
 * @param {Float32Array} descriptor1 - First face descriptor
 * @param {Float32Array} descriptor2 - Second face descriptor
 * @returns {number} - Similarity score (0-1, higher is more similar)
 */
export const compareFaceDescriptors = (descriptor1, descriptor2) => {
  try {
    // Calculate Euclidean distance between descriptors
    const distance = faceapi.euclideanDistance(descriptor1, descriptor2);
    
    // Convert distance to similarity score (0-1)
    // Lower distance means higher similarity
    const similarity = Math.max(0, 1 - (distance / FACE_MATCH_THRESHOLD));
    
    return similarity;
  } catch (error) {
    console.error('Error comparing face descriptors:', error);
    return 0;
  }
};

/**
 * Store face descriptor in the database
 * @param {string} studentId - Student ID
 * @param {Float32Array} descriptor - Face descriptor
 * @param {number} quality - Face quality score
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export const storeFaceDescriptor = async (studentId, descriptor, quality) => {
  try {
    // Convert Float32Array to regular array for storage
    const descriptorArray = Array.from(descriptor);
    
    // Store the descriptor in the database
    const { error } = await supabase
      .from('students')
      .update({
        face_embedding: descriptorArray,
        face_quality: quality,
        last_verification: new Date().toISOString()
      })
      .eq('id', studentId);
    
    if (error) {
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('Error storing face descriptor:', error);
    
    // Try compressed storage as fallback if pgvector is not available
    try {
      const compressed = pako.deflate(JSON.stringify(Array.from(descriptor)));
      const base64Compressed = btoa(String.fromCharCode.apply(null, compressed));
      
      const { error } = await supabase
        .from('students')
        .update({
          face_descriptor: base64Compressed, // Fallback to regular column
          face_quality: quality,
          last_verification: new Date().toISOString()
        })
        .eq('id', studentId);
      
      if (error) {
        throw error;
      }
      
      return true;
    } catch (fallbackError) {
      console.error('Error storing compressed face descriptor:', fallbackError);
      return false;
    }
  }
};

/**
 * Verify a face against a stored reference
 * @param {string} studentId - Student ID
 * @param {Float32Array} currentDescriptor - Current face descriptor
 * @returns {Promise<{match: boolean, confidence: number}>} - Match result and confidence
 */
export const verifyFace = async (studentId, currentDescriptor) => {
  try {
    // Get the stored face descriptor
    const { data, error } = await supabase
      .from('students')
      .select('face_embedding, face_descriptor')
      .eq('id', studentId)
      .single();
    
    if (error || (!data?.face_embedding && !data?.face_descriptor)) {
      throw new Error('No reference face found for this student');
    }
    
    let storedDescriptor;
    
    // Handle pgvector embedding
    if (data.face_embedding) {
      storedDescriptor = new Float32Array(data.face_embedding);
    } 
    // Handle compressed descriptor
    else if (data.face_descriptor) {
      const compressed = atob(data.face_descriptor);
      const charData = compressed.split('').map(x => x.charCodeAt(0));
      const uncompressed = pako.inflate(new Uint8Array(charData));
      const descriptorString = String.fromCharCode.apply(null, uncompressed);
      storedDescriptor = new Float32Array(JSON.parse(descriptorString));
    } else {
      throw new Error('No face data available');
    }
    
    // Compare the descriptors
    const confidence = compareFaceDescriptors(currentDescriptor, storedDescriptor);
    const match = confidence > 0.7; // 70% confidence threshold for a match
    
    // Log the verification
    await logVerification(studentId, match, confidence);
    
    return { match, confidence };
  } catch (error) {
    console.error('Error verifying face:', error);
    throw new Error('Failed to verify face');
  }
};

/**
 * Log a face verification attempt
 * @param {string} studentId - Student ID
 * @param {boolean} verified - Whether the verification was successful
 * @param {number} confidence - Confidence score
 * @returns {Promise<void>}
 */
export const logVerification = async (studentId, verified, confidence) => {
  try {
    // Get device info
    const deviceInfo = {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      timestamp: new Date().toISOString()
    };
    
    // Log the verification
    await supabase
      .from('face_verification_logs')
      .insert({
        student_id: studentId,
        verified,
        confidence,
        device_info: deviceInfo,
        ip_address: 'client-side' // Actual IP will be logged by Supabase
      });
    
    // Update verification count
    await supabase
      .from('students')
      .update({
        verification_count: supabase.rpc('increment', { x: 1 }),
        last_verification: new Date().toISOString()
      })
      .eq('id', studentId);
  } catch (error) {
    console.error('Error logging verification:', error);
  }
};

/**
 * Create a mock face descriptor for development
 * @returns {Float32Array} - Mock face descriptor
 */
export const createMockDescriptor = () => {
  return new Float32Array(128).fill(0.5);
};
