/**
 * Face Recognition Service
 * Provides both client-side and server-side face recognition capabilities
 */
import { loadFaceModels, calculateFaceDistance, createMockDescriptor, getFaceApi } from './faceDetectionUtils';
import { supabase } from './supabaseClient';
import { encryptData, decryptData } from './encryption';
import { calculateImageQuality, getDeviceInfo } from './imageUtils';

// Configuration
const FACE_MATCH_THRESHOLD = 0.6; // Lower values are more strict
const MODELS_PATH = '/models';
const SERVER_API_URL = import.meta.env.VITE_FACE_API_URL || 'http://localhost:5000';
const USE_SERVER_SIDE = import.meta.env.VITE_USE_SERVER_SIDE_FACE_RECOGNITION === 'true';

// Re-export loadFaceModels from faceDetectionUtils
export { loadFaceModels };

/**
 * Detect and encode a face from an image
 * @param {HTMLImageElement|HTMLVideoElement|HTMLCanvasElement} imageElement - Image element containing a face
 * @returns {Promise<{descriptor: Float32Array, detection: Object, quality: number}|null>} - Face data or null if no face detected
 */
export const detectAndEncodeFace = async (imageElement) => {
  try {
    // Check if we're in development mode
    const isDevelopment = import.meta.env.DEV || !import.meta.env.PROD;

    if (isDevelopment && !import.meta.env.VITE_FORCE_FACE_MODELS) {
      console.log('Development mode detected. Using mock face detection.');
      // In development, return a mock descriptor
      return {
        descriptor: createMockDescriptor(), // Use the utility function
        detection: { detection: { box: { x: 0, y: 0, width: 100, height: 100 } } },
        quality: 0.95
      };
    }

    // Ensure models are loaded
    await loadFaceModels();

    // Get the faceapi instance
    const faceapi = getFaceApi();

    // Detect face with landmarks and descriptor
    const detection = await faceapi.detectSingleFace(imageElement)
      .withFaceLandmarks()
      .withFaceDescriptor();

    if (!detection) {
      console.warn('No face detected in the image');
      return null;
    }

    // Calculate image quality
    const quality = calculateImageQuality(imageElement, detection);

    return {
      descriptor: detection.descriptor,
      detection,
      quality
    };
  } catch (error) {
    console.error('Error detecting face:', error);
    // Instead of throwing, return null
    return null;
  }
};

/**
 * Upload a face image and store descriptor
 * @param {string} studentId - Student ID
 * @param {Blob|File} imageBlob - Image blob or file
 * @param {Float32Array} descriptor - Face descriptor
 * @param {number} quality - Image quality score
 * @returns {Promise<{success: boolean, imageUrl: string}>} - Result of the operation
 */
export const uploadFaceData = async (studentId, imageBlob, descriptor, quality) => {
  try {
    // Generate a unique filename
    const fileName = `${Date.now()}.webp`;
    const filePath = `${studentId}/${fileName}`;

    // Upload the image to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from('student_photos')
      .upload(filePath, imageBlob, {
        contentType: 'image/webp',
        upsert: true
      });

    if (uploadError) {
      throw uploadError;
    }

    // Get the public URL of the uploaded image
    const { data } = supabase.storage
      .from('student_photos')
      .getPublicUrl(filePath);

    const imageUrl = data.publicUrl;

    // Convert Float32Array to regular array for storage
    const descriptorArray = Array.from(descriptor);

    // Encrypt the descriptor for security
    const encryptedDescriptor = encryptData(JSON.stringify(descriptorArray));

    // Store the embedding directly in the database
    const { error: updateError } = await supabase.rpc('update_student_photo', {
      p_student_id: studentId,
      p_photo_path: filePath,
      p_embedding: descriptorArray,
      p_quality: quality
    });

    if (updateError) {
      // Fallback to regular update if RPC fails
      const { error: fallbackError } = await supabase
        .from('students')
        .update({
          photo_path: filePath,
          face_descriptor: encryptedDescriptor,
          reference_image_url: imageUrl,
          last_photo_update: new Date().toISOString(),
          photo_quality: quality
        })
        .eq('id', studentId);

      if (fallbackError) {
        throw fallbackError;
      }
    }

    // Record the update
    await supabase
      .from('photo_updates')
      .insert({
        student_id: studentId,
        update_type: 'update',
        photo_path: filePath
      });

    return {
      success: true,
      imageUrl,
      quality
    };
  } catch (error) {
    console.error('Error uploading face data:', error);
    throw new Error('Failed to upload face data');
  }
};

/**
 * Compare two face descriptors and return the similarity
 * @param {Float32Array} descriptor1 - First face descriptor
 * @param {Float32Array} descriptor2 - Second face descriptor
 * @returns {number} - Similarity score (0-1, higher is more similar)
 */
export const compareFaceDescriptors = (descriptor1, descriptor2) => {
  // Calculate Euclidean distance between descriptors
  const distance = calculateFaceDistance(descriptor1, descriptor2);

  // Convert distance to similarity score (0-1)
  // Lower distance means higher similarity
  const similarity = Math.max(0, 1 - (distance / FACE_MATCH_THRESHOLD));

  return similarity;
};

/**
 * Verify a face against a stored reference using client-side processing
 * @param {string} studentId - Student ID
 * @param {Float32Array} currentDescriptor - Current face descriptor
 * @returns {Promise<{match: boolean, confidence: number}>} - Match result and confidence
 */
export const verifyFaceClientSide = async (studentId, currentDescriptor) => {
  try {
    // Get the stored face descriptor
    const { data, error } = await supabase
      .from('students')
      .select('face_descriptor')
      .eq('id', studentId)
      .single();

    if (error || !data?.face_descriptor) {
      throw new Error('No reference face found for this user');
    }

    // Handle mock data for development
    if (typeof data.face_descriptor === 'string' && data.face_descriptor === 'mock-face-descriptor-data') {
      // For mock data, always return a successful match
      return { match: true, confidence: 0.95 };
    }

    // For real data, decrypt and parse the stored descriptor
    const decryptedDescriptor = JSON.parse(decryptData(data.face_descriptor));
    const storedDescriptor = new Float32Array(decryptedDescriptor);

    // Compare the descriptors
    const confidence = compareFaceDescriptors(currentDescriptor, storedDescriptor);
    const match = confidence > 0.7; // 70% confidence threshold for a match

    return { match, confidence };
  } catch (error) {
    console.error('Error verifying face:', error);
    throw new Error('Failed to verify face');
  }
};

/**
 * Verify a face against a stored reference using server-side processing
 * @param {string} studentId - Student ID
 * @param {string} capturedImageUrl - URL of the captured image
 * @returns {Promise<{match: boolean, confidence: number}>} - Match result and confidence
 */
export const verifyFaceServerSide = async (studentId, capturedImageUrl) => {
  try {
    // Get the reference image URL
    const { data, error } = await supabase
      .from('students')
      .select('reference_image_url')
      .eq('id', studentId)
      .single();

    if (error || !data?.reference_image_url) {
      throw new Error('No reference face found for this user');
    }

    // Handle mock data for development
    if (import.meta.env.DEV && !import.meta.env.VITE_FORCE_FACE_API) {
      // For development, always return a successful match
      return { match: true, confidence: 0.95 };
    }

    // Call the server-side API
    const response = await fetch(`${SERVER_API_URL}/verify_face`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        student_id: studentId,
        captured_image_url: capturedImageUrl,
        reference_image_url: data.reference_image_url,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Server error');
    }

    const result = await response.json();

    return {
      match: result.is_match,
      confidence: result.confidence,
    };
  } catch (error) {
    console.error('Error verifying face with server:', error);
    throw new Error('Failed to verify face with server');
  }
};

/**
 * Verify a face against a stored reference
 * Uses server-side or client-side processing based on configuration
 * @param {string} studentId - Student ID
 * @param {Float32Array} currentDescriptor - Current face descriptor (for client-side)
 * @param {string} capturedImageUrl - URL of the captured image (for server-side)
 * @returns {Promise<{match: boolean, confidence: number}>} - Match result and confidence
 */
export const verifyFace = async (studentId, currentDescriptor, capturedImageUrl) => {
  // Use server-side processing if configured
  if (USE_SERVER_SIDE && capturedImageUrl) {
    return verifyFaceServerSide(studentId, capturedImageUrl);
  }

  // Otherwise use client-side processing
  return verifyFaceClientSide(studentId, currentDescriptor);
};

/**
 * Record attendance for a student
 * @param {string} studentId - Student ID
 * @param {boolean} present - Whether the student is present
 * @param {number} confidence - Match confidence score (0-1)
 * @param {string} classId - Class ID (optional)
 * @returns {Promise<{success: boolean, id: string}>} - Result of the operation
 */
export const recordAttendance = async (studentId, present, confidence, classId = null) => {
  try {
    const deviceInfo = getDeviceInfo();

    const attendanceData = {
      student_id: studentId,
      status: present,
      confidence: confidence,
      device_info: deviceInfo,
      timestamp: new Date().toISOString()
    };

    // Add class_id if provided
    if (classId) {
      attendanceData.class_id = classId;
    }

    const { data, error } = await supabase
      .from('attendance')
      .insert(attendanceData)
      .select('id')
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      id: data.id
    };
  } catch (error) {
    console.error('Error recording attendance:', error);
    throw new Error('Failed to record attendance');
  }
};

/**
 * Find similar faces in the database
 * @param {Float32Array} descriptor - Face descriptor to search for
 * @param {number} threshold - Similarity threshold (0-1)
 * @param {number} maxResults - Maximum number of results to return
 * @returns {Promise<Array>} - Array of similar faces
 */
export const findSimilarFaces = async (descriptor, threshold = 0.8, maxResults = 5) => {
  try {
    // Convert Float32Array to regular array
    const descriptorArray = Array.from(descriptor);

    // Call the database function
    const { data, error } = await supabase.rpc('find_similar_faces', {
      search_embedding: descriptorArray,
      similarity_threshold: threshold,
      max_results: maxResults
    });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error finding similar faces:', error);
    return [];
  }
};
