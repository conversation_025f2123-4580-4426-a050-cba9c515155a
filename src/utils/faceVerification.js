/**
 * Face Verification Utilities
 * Advanced face recognition and verification system for multi-student identification
 */

import * as faceapi from 'face-api.js';
import { supabase } from './supabaseClient';

// Configuration
const FACE_VERIFICATION_CONFIG = {
  SIMILARITY_THRESHOLD: 0.6, // Minimum similarity for match
  MIN_DETECTION_SCORE: 0.9, // Anti-spoofing threshold
  MAX_EMBEDDING_DISTANCE: 0.4, // Maximum Euclidean distance for match
  VERIFICATION_TIMEOUT: 10000, // 10 seconds timeout
  IMAGE_QUALITY_THRESHOLD: 0.8 // Minimum image quality
};

// Known students data
export const KNOWN_STUDENTS = [
  { id: 1, name: 'Anupam', student_id: 'S1001', email: '<EMAIL>' },
  { id: 2, name: '<PERSON><PERSON><PERSON>', student_id: 'S1002', email: '<EMAIL>' },
  { id: 3, name: '<PERSON><PERSON><PERSON>', student_id: 'S1003', email: 'a<PERSON><PERSON>@example.com' }
];

/**
 * Load face-api.js models
 * @returns {Promise<boolean>} - Success status
 */
export const loadFaceApiModels = async () => {
  try {
    const MODEL_URL = '/models'; // Models should be in public/models folder
    
    await Promise.all([
      faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),
      faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
      faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL),
      faceapi.nets.faceExpressionNet.loadFromUri(MODEL_URL),
      faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL)
    ]);
    
    console.log('✅ Face-api.js models loaded successfully');
    return true;
  } catch (error) {
    console.error('❌ Error loading face-api.js models:', error);
    return false;
  }
};

/**
 * Calculate face embedding from image
 * @param {HTMLImageElement|HTMLVideoElement|HTMLCanvasElement} imageElement - Image element
 * @returns {Promise<Object>} - Face detection result with embedding
 */
export const calculateFaceEmbedding = async (imageElement) => {
  try {
    // Detect face with landmarks and descriptor
    const detection = await faceapi
      .detectSingleFace(imageElement, new faceapi.TinyFaceDetectorOptions())
      .withFaceLandmarks()
      .withFaceDescriptor();
    
    if (!detection) {
      throw new Error('No face detected in the image');
    }
    
    // Check detection quality
    if (detection.detection.score < FACE_VERIFICATION_CONFIG.MIN_DETECTION_SCORE) {
      throw new Error(`Low detection quality: ${(detection.detection.score * 100).toFixed(1)}%`);
    }
    
    return {
      success: true,
      embedding: Array.from(detection.descriptor),
      detectionScore: detection.detection.score,
      landmarks: detection.landmarks,
      box: detection.detection.box
    };
  } catch (error) {
    console.error('Error calculating face embedding:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Register face embedding for a student
 * @param {string} studentId - Student ID
 * @param {HTMLImageElement|HTMLVideoElement|HTMLCanvasElement} imageElement - Image element
 * @param {string} photoUrl - URL of the stored photo
 * @returns {Promise<Object>} - Registration result
 */
export const registerFaceEmbedding = async (studentId, imageElement, photoUrl) => {
  try {
    // Calculate face embedding
    const embeddingResult = await calculateFaceEmbedding(imageElement);
    
    if (!embeddingResult.success) {
      throw new Error(embeddingResult.error);
    }
    
    // Check if we're using mock Supabase
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      // Mock registration for development
      console.log('Mock face registration:', {
        studentId,
        embeddingLength: embeddingResult.embedding.length,
        detectionScore: embeddingResult.detectionScore,
        photoUrl
      });
      
      return {
        success: true,
        studentId,
        embeddingLength: embeddingResult.embedding.length,
        detectionScore: embeddingResult.detectionScore,
        message: 'Face registered successfully (mock mode)'
      };
    }
    
    // Register with database function
    const { data, error } = await supabase.rpc('register_face_embedding', {
      p_student_id: studentId,
      p_face_embedding: embeddingResult.embedding,
      p_photo_url: photoUrl,
      p_quality_score: embeddingResult.detectionScore,
      p_anti_spoofing_score: embeddingResult.detectionScore // Simplified for demo
    });
    
    if (error) throw error;
    
    return {
      success: true,
      studentId,
      embeddingLength: embeddingResult.embedding.length,
      detectionScore: embeddingResult.detectionScore,
      message: 'Face registered successfully'
    };
  } catch (error) {
    console.error('Error registering face embedding:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Verify student face against stored embedding
 * @param {HTMLImageElement|HTMLVideoElement|HTMLCanvasElement} imageElement - Live image element
 * @param {string} studentId - Student ID to verify against
 * @param {string} verificationImageUrl - URL of verification image (optional)
 * @returns {Promise<Object>} - Verification result
 */
export const verifyStudentFace = async (imageElement, studentId, verificationImageUrl = null) => {
  try {
    const startTime = Date.now();
    
    // Calculate live face embedding
    const embeddingResult = await calculateFaceEmbedding(imageElement);
    
    if (!embeddingResult.success) {
      throw new Error(embeddingResult.error);
    }
    
    // Check if we're using mock Supabase
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      // Mock verification for development
      const student = KNOWN_STUDENTS.find(s => s.student_id === studentId);
      if (!student) {
        throw new Error('Student not found');
      }
      
      // Simulate verification with random confidence
      const confidence = Math.random() * 40 + 60; // 60-100%
      const isMatch = confidence > 75;
      const verificationTime = Date.now() - startTime;
      
      return {
        success: true,
        isMatch,
        confidence: parseFloat(confidence.toFixed(2)),
        studentName: student.name,
        studentId: student.student_id,
        detectionScore: embeddingResult.detectionScore,
        verificationTimeMs: verificationTime,
        threshold: FACE_VERIFICATION_CONFIG.SIMILARITY_THRESHOLD * 100,
        message: isMatch ? 'Face verification successful' : 'Face does not match'
      };
    }
    
    // Verify with database function
    const { data, error } = await supabase.rpc('verify_face_embedding', {
      p_student_id: studentId,
      p_live_embedding: embeddingResult.embedding,
      p_verification_image_url: verificationImageUrl,
      p_device_info: {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        timestamp: new Date().toISOString()
      },
      p_threshold: FACE_VERIFICATION_CONFIG.SIMILARITY_THRESHOLD
    });
    
    if (error) throw error;
    
    if (!data.success) {
      throw new Error(data.error);
    }
    
    const verificationTime = Date.now() - startTime;
    
    return {
      success: true,
      isMatch: data.is_match,
      confidence: data.confidence,
      studentName: data.student_name,
      studentId: studentId,
      detectionScore: embeddingResult.detectionScore,
      verificationTimeMs: verificationTime,
      threshold: data.threshold_used * 100,
      verificationId: data.verification_id,
      message: data.is_match ? 'Face verification successful' : 'Face does not match'
    };
  } catch (error) {
    console.error('Error verifying student face:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Identify student from live image (1:N matching)
 * @param {HTMLImageElement|HTMLVideoElement|HTMLCanvasElement} imageElement - Live image element
 * @returns {Promise<Object>} - Identification result
 */
export const identifyStudent = async (imageElement) => {
  try {
    const startTime = Date.now();
    
    // Calculate live face embedding
    const embeddingResult = await calculateFaceEmbedding(imageElement);
    
    if (!embeddingResult.success) {
      throw new Error(embeddingResult.error);
    }
    
    // Check if we're using mock Supabase
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      // Mock identification for development
      const randomStudent = KNOWN_STUDENTS[Math.floor(Math.random() * KNOWN_STUDENTS.length)];
      const confidence = Math.random() * 40 + 60; // 60-100%
      const isMatch = confidence > 75;
      const verificationTime = Date.now() - startTime;
      
      if (isMatch) {
        return {
          success: true,
          identified: true,
          student: randomStudent,
          confidence: parseFloat(confidence.toFixed(2)),
          detectionScore: embeddingResult.detectionScore,
          verificationTimeMs: verificationTime,
          message: `Identified as ${randomStudent.name}`
        };
      } else {
        return {
          success: true,
          identified: false,
          confidence: parseFloat(confidence.toFixed(2)),
          detectionScore: embeddingResult.detectionScore,
          verificationTimeMs: verificationTime,
          message: 'No matching student found'
        };
      }
    }
    
    // Try to identify against all known students
    const verificationPromises = KNOWN_STUDENTS.map(student => 
      verifyStudentFace(imageElement, student.student_id)
    );
    
    const results = await Promise.all(verificationPromises);
    
    // Find the best match
    const validResults = results.filter(r => r.success);
    const matches = validResults.filter(r => r.isMatch);
    
    if (matches.length === 0) {
      return {
        success: true,
        identified: false,
        confidence: 0,
        detectionScore: embeddingResult.detectionScore,
        verificationTimeMs: Date.now() - startTime,
        message: 'No matching student found'
      };
    }
    
    // Get the best match (highest confidence)
    const bestMatch = matches.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    );
    
    const student = KNOWN_STUDENTS.find(s => s.student_id === bestMatch.studentId);
    
    return {
      success: true,
      identified: true,
      student: student,
      confidence: bestMatch.confidence,
      detectionScore: embeddingResult.detectionScore,
      verificationTimeMs: Date.now() - startTime,
      verificationId: bestMatch.verificationId,
      message: `Identified as ${bestMatch.studentName}`
    };
  } catch (error) {
    console.error('Error identifying student:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get face verification statistics
 * @param {string} studentId - Student ID (optional)
 * @param {number} daysBack - Number of days to look back
 * @returns {Promise<Object>} - Verification statistics
 */
export const getFaceVerificationStats = async (studentId = null, daysBack = 30) => {
  try {
    // Check if we're using mock Supabase
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      // Mock statistics for development
      return {
        success: true,
        stats: {
          total_attempts: 45,
          successful_matches: 42,
          failed_matches: 3,
          success_rate: 93.33,
          avg_confidence: 87.5,
          max_confidence: 98.2,
          min_confidence: 62.1,
          avg_verification_time_ms: 1250
        }
      };
    }
    
    const { data, error } = await supabase.rpc('get_face_verification_stats', {
      p_student_id: studentId,
      p_days_back: daysBack
    });
    
    if (error) throw error;
    
    return {
      success: true,
      stats: data
    };
  } catch (error) {
    console.error('Error getting face verification stats:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Check if face-api.js models are loaded
 * @returns {boolean} - Models loaded status
 */
export const areModelsLoaded = () => {
  return (
    faceapi.nets.tinyFaceDetector.isLoaded &&
    faceapi.nets.faceLandmark68Net.isLoaded &&
    faceapi.nets.faceRecognitionNet.isLoaded
  );
};

/**
 * Get device information for verification logging
 * @returns {Object} - Device information
 */
export const getDeviceInfo = () => {
  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    devicePixelRatio: window.devicePixelRatio,
    timestamp: new Date().toISOString()
  };
};

/**
 * Validate image quality for face verification
 * @param {HTMLImageElement|HTMLVideoElement|HTMLCanvasElement} imageElement - Image element
 * @returns {Promise<Object>} - Quality validation result
 */
export const validateImageQuality = async (imageElement) => {
  try {
    // Basic quality checks
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = imageElement.width || imageElement.videoWidth;
    canvas.height = imageElement.height || imageElement.videoHeight;
    
    ctx.drawImage(imageElement, 0, 0);
    
    // Check image dimensions
    if (canvas.width < 200 || canvas.height < 200) {
      return {
        valid: false,
        reason: 'Image resolution too low (minimum 200x200)',
        score: 0.2
      };
    }
    
    // Check for face detection
    const detection = await faceapi
      .detectSingleFace(imageElement, new faceapi.TinyFaceDetectorOptions());
    
    if (!detection) {
      return {
        valid: false,
        reason: 'No face detected in image',
        score: 0.0
      };
    }
    
    // Calculate quality score based on detection confidence and face size
    const faceArea = detection.box.width * detection.box.height;
    const imageArea = canvas.width * canvas.height;
    const faceRatio = faceArea / imageArea;
    
    let qualityScore = detection.score;
    
    // Adjust score based on face size (prefer larger faces)
    if (faceRatio < 0.05) {
      qualityScore *= 0.7; // Face too small
    } else if (faceRatio > 0.5) {
      qualityScore *= 0.8; // Face too large
    }
    
    const isValid = qualityScore >= FACE_VERIFICATION_CONFIG.IMAGE_QUALITY_THRESHOLD;
    
    return {
      valid: isValid,
      score: qualityScore,
      detectionScore: detection.score,
      faceRatio: faceRatio,
      reason: isValid ? 'Image quality acceptable' : 'Image quality too low'
    };
  } catch (error) {
    return {
      valid: false,
      reason: `Quality validation failed: ${error.message}`,
      score: 0.0
    };
  }
};
