/**
 * Enhanced Student Registration System Testing
 * Comprehensive testing for reliable face registration flow
 */

import { 
  validateRegistrationForm,
  validateFaceImageQuality,
  processFaceFromImage,
  registerStudentEnhanced,
  getCoursesAndClasses,
  logRegistrationStep,
  uploadFaceImage
} from './enhancedRegistrationService';

/**
 * Test form validation with various scenarios
 */
export const testFormValidation = () => {
  console.log('🧪 Testing Enhanced Form Validation...');
  
  const testCases = [
    {
      name: 'Valid Complete Form',
      data: {
        first_name: 'Anupam',
        last_name: '<PERSON>',
        email: '<EMAIL>',
        student_id: 'E22273735500014',
        course: 'Computer Science',
        semester: 6,
        class: 'Class A'
      },
      expectedValid: true
    },
    {
      name: 'Invalid Student ID Format',
      data: {
        first_name: 'Test',
        last_name: 'Student',
        email: '<EMAIL>',
        student_id: '123456789', // Invalid format
        course: 'Computer Science',
        semester: 1,
        class: 'Class A'
      },
      expectedValid: false
    },
    {
      name: 'Invalid Email Domain',
      data: {
        first_name: 'Test',
        last_name: 'Student',
        email: '<EMAIL>', // Not in allowed domains
        student_id: 'E12345678901234',
        course: 'Computer Science',
        semester: 1,
        class: 'Class A'
      },
      expectedValid: false
    },
    {
      name: 'Missing Required Fields',
      data: {
        first_name: '',
        last_name: '',
        email: '',
        student_id: '',
        course: '',
        semester: 0,
        class: ''
      },
      expectedValid: false
    },
    {
      name: 'Invalid Semester Range',
      data: {
        first_name: 'Test',
        last_name: 'Student',
        email: '<EMAIL>',
        student_id: 'E12345678901234',
        course: 'Computer Science',
        semester: 10, // Invalid semester
        class: 'Class A'
      },
      expectedValid: false
    }
  ];
  
  let passed = 0;
  
  testCases.forEach(testCase => {
    console.log(`Testing: ${testCase.name}`);
    
    const result = validateRegistrationForm(testCase.data);
    const success = result.valid === testCase.expectedValid;
    
    console.log(`Expected: ${testCase.expectedValid}, Got: ${result.valid}`);
    if (!result.valid && result.errors.length > 0) {
      console.log('Validation errors:', result.errors);
    }
    console.log(`Result: ${success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (success) passed++;
    console.log('---');
  });
  
  console.log(`✅ Form validation tests completed: ${passed}/${testCases.length} passed`);
  return passed === testCases.length;
};

/**
 * Test image quality validation
 */
export const testImageQualityValidation = async () => {
  console.log('🧪 Testing Image Quality Validation...');
  
  const testImages = [
    {
      name: 'High Quality Image',
      width: 800,
      height: 600,
      sharpness: 0.8,
      expectedValid: true
    },
    {
      name: 'Low Resolution Image',
      width: 200,
      height: 150,
      sharpness: 0.8,
      expectedValid: false
    },
    {
      name: 'Blurry Image',
      width: 640,
      height: 480,
      sharpness: 0.3,
      expectedValid: false
    },
    {
      name: 'Minimum Valid Size',
      width: 500,
      height: 500,
      sharpness: 0.7,
      expectedValid: true
    }
  ];
  
  let passed = 0;
  
  for (const testImage of testImages) {
    console.log(`Testing: ${testImage.name}`);
    
    // Create test image
    const canvas = document.createElement('canvas');
    canvas.width = testImage.width;
    canvas.height = testImage.height;
    const ctx = canvas.getContext('2d');
    
    // Fill with pattern to simulate sharpness
    if (testImage.sharpness > 0.6) {
      // Sharp pattern
      for (let i = 0; i < canvas.width; i += 2) {
        ctx.fillStyle = i % 4 === 0 ? '#000' : '#fff';
        ctx.fillRect(i, 0, 2, canvas.height);
      }
    } else {
      // Blurry pattern
      ctx.fillStyle = '#888';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
    }
    
    const imageElement = await new Promise(resolve => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.src = canvas.toDataURL();
    });
    
    const result = await validateFaceImageQuality(imageElement);
    const success = result.valid === testImage.expectedValid;
    
    console.log(`Expected: ${testImage.expectedValid}, Got: ${result.valid}`);
    if (!result.valid) {
      console.log('Quality error:', result.error);
    }
    console.log(`Result: ${success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (success) passed++;
    console.log('---');
  }
  
  console.log(`✅ Image quality validation tests completed: ${passed}/${testImages.length} passed`);
  return passed === testImages.length;
};

/**
 * Test face processing
 */
export const testFaceProcessing = async () => {
  console.log('🧪 Testing Face Processing...');
  
  const scenarios = [
    {
      name: 'Single Clear Face',
      faces: 1,
      quality: 'high',
      expectedSuccess: true
    },
    {
      name: 'No Face',
      faces: 0,
      quality: 'high',
      expectedSuccess: false
    },
    {
      name: 'Multiple Faces',
      faces: 2,
      quality: 'high',
      expectedSuccess: false
    }
  ];
  
  let passed = 0;
  
  for (const scenario of scenarios) {
    console.log(`Testing: ${scenario.name}`);
    
    // Create test image
    const canvas = document.createElement('canvas');
    canvas.width = 640;
    canvas.height = 480;
    const ctx = canvas.getContext('2d');
    
    // Background
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 640, 480);
    
    // Draw faces based on scenario
    if (scenario.faces > 0) {
      for (let i = 0; i < scenario.faces; i++) {
        const x = 200 + (i * 200);
        const y = 240;
        
        // Face
        ctx.fillStyle = '#ddbea9';
        ctx.beginPath();
        ctx.arc(x, y, 60, 0, 2 * Math.PI);
        ctx.fill();
        
        // Eyes
        ctx.fillStyle = '#000';
        ctx.beginPath();
        ctx.arc(x - 20, y - 20, 5, 0, 2 * Math.PI);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(x + 20, y - 20, 5, 0, 2 * Math.PI);
        ctx.fill();
      }
    }
    
    const imageElement = await new Promise(resolve => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.src = canvas.toDataURL();
    });
    
    const result = await processFaceFromImage(imageElement);
    const success = result.success === scenario.expectedSuccess;
    
    console.log(`Expected success: ${scenario.expectedSuccess}, Got: ${result.success}`);
    if (!result.success) {
      console.log('Processing error:', result.error);
    } else {
      console.log('Detection score:', (result.detectionScore * 100).toFixed(1) + '%');
    }
    console.log(`Result: ${success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (success) passed++;
    console.log('---');
  }
  
  console.log(`✅ Face processing tests completed: ${passed}/${scenarios.length} passed`);
  return passed === scenarios.length;
};

/**
 * Test complete registration workflow
 */
export const testCompleteRegistration = async () => {
  console.log('🧪 Testing Complete Registration Workflow...');
  
  const testStudents = [
    {
      name: 'Valid Student with Face',
      data: {
        first_name: 'Test',
        last_name: 'Student',
        email: '<EMAIL>',
        student_id: 'E' + Date.now().toString().slice(-12),
        course: 'Computer Science',
        semester: 1,
        class: 'Class A'
      },
      includeFace: true,
      expectedSuccess: true
    },
    {
      name: 'Valid Student without Face',
      data: {
        first_name: 'Test',
        last_name: 'NoFace',
        email: '<EMAIL>',
        student_id: 'E' + (Date.now() + 1).toString().slice(-12),
        course: 'Computer Science',
        semester: 1,
        class: 'Class A'
      },
      includeFace: false,
      expectedSuccess: true
    },
    {
      name: 'Invalid Student Data',
      data: {
        first_name: '',
        last_name: '',
        email: 'invalid-email',
        student_id: 'invalid-id',
        course: '',
        semester: 0,
        class: ''
      },
      includeFace: false,
      expectedSuccess: false
    }
  ];
  
  let passed = 0;
  
  for (const testStudent of testStudents) {
    console.log(`Testing: ${testStudent.name}`);
    
    let faceBlob = null;
    
    if (testStudent.includeFace) {
      // Create test face image
      const canvas = document.createElement('canvas');
      canvas.width = 640;
      canvas.height = 480;
      const ctx = canvas.getContext('2d');
      
      // Background
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, 640, 480);
      
      // Face
      ctx.fillStyle = '#ddbea9';
      ctx.beginPath();
      ctx.arc(320, 240, 80, 0, 2 * Math.PI);
      ctx.fill();
      
      // Eyes
      ctx.fillStyle = '#000';
      ctx.beginPath();
      ctx.arc(300, 220, 8, 0, 2 * Math.PI);
      ctx.fill();
      ctx.beginPath();
      ctx.arc(340, 220, 8, 0, 2 * Math.PI);
      ctx.fill();
      
      faceBlob = await new Promise(resolve => {
        canvas.toBlob(resolve, 'image/jpeg', 0.9);
      });
    }
    
    const result = await registerStudentEnhanced(testStudent.data, faceBlob);
    const success = result.success === testStudent.expectedSuccess;
    
    console.log(`Expected success: ${testStudent.expectedSuccess}, Got: ${result.success}`);
    console.log('Student ID:', result.studentId || 'N/A');
    console.log('Status:', result.registrationStatus || 'N/A');
    if (!result.success) {
      console.log('Error:', result.error);
    }
    if (result.faceData) {
      console.log('Face detection score:', (result.faceData.detectionScore * 100).toFixed(1) + '%');
    }
    console.log(`Result: ${success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (success) passed++;
    console.log('---');
  }
  
  console.log(`✅ Complete registration tests completed: ${passed}/${testStudents.length} passed`);
  return passed === testStudents.length;
};

/**
 * Test courses and classes data loading
 */
export const testCoursesAndClasses = async () => {
  console.log('🧪 Testing Courses and Classes Data...');
  
  try {
    const result = await getCoursesAndClasses();
    
    if (result.success) {
      console.log('✅ Courses data loaded successfully');
      console.log('Number of courses:', result.courses.length);
      
      result.courses.forEach(course => {
        console.log(`Course: ${course.name} (${course.code})`);
        console.log(`  Classes: ${course.classes.length}`);
      });
      
      return true;
    } else {
      console.log('❌ Failed to load courses:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Courses test failed:', error);
    return false;
  }
};

/**
 * Test registration step logging
 */
export const testRegistrationLogging = async () => {
  console.log('🧪 Testing Registration Step Logging...');
  
  const testSteps = [
    'form_submitted',
    'camera_initialized',
    'face_captured',
    'face_processed',
    'data_stored',
    'completed'
  ];
  
  const testStudentId = 'TEST_LOG_' + Date.now();
  let passed = 0;
  
  for (const step of testSteps) {
    try {
      const result = await logRegistrationStep(testStudentId, step, {
        test: true,
        timestamp: Date.now()
      });
      
      if (result.success) {
        console.log(`✅ Logged step: ${step}`);
        passed++;
      } else {
        console.log(`❌ Failed to log step: ${step}`);
      }
    } catch (error) {
      console.log(`❌ Error logging step ${step}:`, error.message);
    }
  }
  
  console.log(`✅ Registration logging tests completed: ${passed}/${testSteps.length} passed`);
  return passed === testSteps.length;
};

/**
 * Test image upload functionality
 */
export const testImageUpload = async () => {
  console.log('🧪 Testing Image Upload...');
  
  try {
    // Create test image
    const canvas = document.createElement('canvas');
    canvas.width = 300;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');
    
    ctx.fillStyle = '#ff0000';
    ctx.fillRect(0, 0, 300, 300);
    
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/webp', 0.8);
    });
    
    const testStudentId = 'TEST_UPLOAD_' + Date.now();
    const result = await uploadFaceImage(blob, testStudentId);
    
    if (result.success) {
      console.log('✅ Image upload successful');
      console.log('Upload path:', result.path);
      console.log('Upload URL:', result.url);
      return true;
    } else {
      console.log('❌ Image upload failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Image upload test failed:', error);
    return false;
  }
};

/**
 * Run comprehensive enhanced registration tests
 */
export const runEnhancedRegistrationTests = async () => {
  console.log('🚀 Running Comprehensive Enhanced Registration Tests...');
  console.log('=========================================================');
  
  const tests = [
    { name: 'Form Validation', test: testFormValidation },
    { name: 'Image Quality Validation', test: testImageQualityValidation },
    { name: 'Face Processing', test: testFaceProcessing },
    { name: 'Complete Registration', test: testCompleteRegistration },
    { name: 'Courses and Classes', test: testCoursesAndClasses },
    { name: 'Registration Logging', test: testRegistrationLogging },
    { name: 'Image Upload', test: testImageUpload }
  ];
  
  const results = [];
  
  for (const { name, test } of tests) {
    console.log(`\n📋 Running ${name} test...`);
    const result = await test();
    results.push({ name, passed: result });
    console.log(`${result ? '✅' : '❌'} ${name}: ${result ? 'PASSED' : 'FAILED'}`);
  }
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(({ name, passed }) => {
    console.log(`${passed ? '✅' : '❌'} ${name}`);
  });
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All enhanced registration tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Check the implementation.');
  }
  
  return passed === total;
};

// Make test functions available globally for browser console
if (typeof window !== 'undefined') {
  window.testEnhancedRegistration = {
    testFormValidation,
    testImageQualityValidation,
    testFaceProcessing,
    testCompleteRegistration,
    testCoursesAndClasses,
    testRegistrationLogging,
    testImageUpload,
    runEnhancedRegistrationTests
  };
  
  console.log('🔧 Enhanced registration test utilities loaded!');
  console.log('Available commands:');
  console.log('- window.testEnhancedRegistration.runEnhancedRegistrationTests()');
  console.log('- window.testEnhancedRegistration.testCompleteRegistration()');
  console.log('- window.testEnhancedRegistration.testFormValidation()');
  console.log('- window.testEnhancedRegistration.testFaceProcessing()');
}
