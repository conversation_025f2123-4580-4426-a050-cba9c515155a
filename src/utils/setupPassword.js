/**
 * Password Setup Utility
 * One-time setup script to hash and store the <NAME_EMAIL>
 */

import { supabase } from './supabaseClient';
import { hashPassword } from './passwordUtils';

/**
 * Setup password for the authorized user
 * This should be run once to initialize the secure authentication system
 * @param {string} password - The password to set for the authorized user
 * @returns {Promise<boolean>} - Success status
 */
export const setupAuthorizedUserPassword = async (password) => {
  try {
    console.log('Setting up password for authorized user...');

    // Validate password
    if (!password || password.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }

    // Hash the password
    const { hash, salt } = await hashPassword(password);

    // Update the user record in the database
    const { data, error } = await supabase
      .from('auth_users')
      .update({
        encrypted_password: hash,
        password_salt: salt,
        updated_at: new Date().toISOString()
      })
      .eq('email', '<EMAIL>');

    if (error) {
      console.error('Error updating password:', error);
      throw error;
    }

    console.log('Password setup completed successfully');
    return true;

  } catch (error) {
    console.error('Error setting up password:', error);
    throw error;
  }
};

/**
 * Initialize the authorized user if not exists
 * @param {string} password - The password to set for the authorized user
 * @returns {Promise<boolean>} - Success status
 */
export const initializeAuthorizedUser = async (password) => {
  try {
    console.log('Initializing authorized user...');

    // Check if user already exists
    const { data: existingUser, error: checkError } = await supabase
      .from('auth_users')
      .select('id, email')
      .eq('email', '<EMAIL>')
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is expected if user doesn't exist
      throw checkError;
    }

    if (existingUser) {
      console.log('User already exists, updating password...');
      return await setupAuthorizedUserPassword(password);
    }

    // Hash the password
    const { hash, salt } = await hashPassword(password);

    // Create the user record
    const { data, error } = await supabase
      .from('auth_users')
      .insert({
        email: '<EMAIL>',
        encrypted_password: hash,
        password_salt: salt
      });

    if (error) {
      console.error('Error creating user:', error);
      throw error;
    }

    console.log('Authorized user initialized successfully');
    return true;

  } catch (error) {
    console.error('Error initializing authorized user:', error);
    throw error;
  }
};

/**
 * Test the authentication system
 * @param {string} email - Email to test
 * @param {string} password - Password to test
 * @returns {Promise<boolean>} - Test result
 */
export const testAuthentication = async (email, password) => {
  try {
    console.log('Testing authentication...');

    // Import authentication function
    const { authenticateUser, getCSRFToken } = await import('./authService');

    // Get CSRF token
    const csrfToken = getCSRFToken();

    // Test authentication
    const result = await authenticateUser(email, password, csrfToken);

    if (result.success) {
      console.log('Authentication test passed');
      return true;
    } else {
      console.error('Authentication test failed:', result.error);
      return false;
    }

  } catch (error) {
    console.error('Error testing authentication:', error);
    return false;
  }
};

/**
 * Setup script to run in browser console
 * Usage: setupExaminoAuth('your-secure-password')
 */
window.setupExaminoAuth = async (password) => {
  try {
    console.log('🔐 Starting Examino Authentication Setup...');

    if (!password) {
      console.error('❌ Password is required');
      return false;
    }

    // Check if we're using mock Supabase
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;

    if (USE_MOCK) {
      console.log('🔧 Mock mode detected - authentication will work with any 8+ character password');
      console.log('✅ Authentication setup completed successfully');
      console.log('🔑 You can now login with:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: [any password 8+ characters]');

      // Test authentication
      console.log('🧪 Testing authentication...');
      const testResult = await testAuthentication('<EMAIL>', password);

      if (testResult) {
        console.log('✅ Authentication test passed');
      } else {
        console.log('❌ Authentication test failed');
      }

      return true;
    }

    // Initialize user for production
    const success = await initializeAuthorizedUser(password);

    if (success) {
      console.log('✅ Authentication setup completed successfully');
      console.log('🔑 You can now login with:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: [your password]');

      // Test authentication
      console.log('🧪 Testing authentication...');
      const testResult = await testAuthentication('<EMAIL>', password);

      if (testResult) {
        console.log('✅ Authentication test passed');
      } else {
        console.log('❌ Authentication test failed');
      }

      return true;
    } else {
      console.log('❌ Authentication setup failed');
      return false;
    }

  } catch (error) {
    console.error('❌ Setup error:', error);
    return false;
  }
};

// Export for use in other modules
export { setupExaminoAuth } from './setupPassword';
