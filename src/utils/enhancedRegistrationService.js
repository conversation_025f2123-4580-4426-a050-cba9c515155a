/**
 * Enhanced Student Registration Service
 * Reliable face registration flow with optimized camera handling
 */

import { supabase } from './supabaseClient';
import * as faceapi from 'face-api.js';

// Configuration for enhanced registration
const REGISTRATION_CONFIG = {
  // Camera settings
  CAMERA_WIDTH: 500,
  CAMERA_HEIGHT: 500,
  CAMERA_FACING_MODE: 'user',
  CAMERA_RETRY_ATTEMPTS: 3,
  CAMERA_RETRY_DELAY: 1000,
  
  // Image quality requirements
  MIN_IMAGE_WIDTH: 500,
  MIN_IMAGE_HEIGHT: 500,
  MIN_SHARPNESS: 0.7,
  COMPRESSION_QUALITY: 0.9,
  
  // Storage settings
  STORAGE_BUCKET: 'face-images',
  IMAGE_FORMAT: 'webp',
  
  // Validation patterns
  STUDENT_ID_PATTERN: /^[A-Z]\d{12,14}$/,
  EMAIL_PATTERN: /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/,
  
  // Institute domain whitelist (can be configured)
  ALLOWED_EMAIL_DOMAINS: ['gmail.com', 'example.com', 'university.edu']
};

/**
 * Robust camera initialization with retry logic
 */
export const initializeCamera = async (videoRef, onError = null, retryCount = 0) => {
  try {
    console.log(`🎥 Initializing camera (attempt ${retryCount + 1}/${REGISTRATION_CONFIG.CAMERA_RETRY_ATTEMPTS})`);
    
    // Stop any existing stream
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
    }
    
    // Request camera access with optimized settings
    const stream = await navigator.mediaDevices.getUserMedia({
      video: {
        width: { ideal: REGISTRATION_CONFIG.CAMERA_WIDTH },
        height: { ideal: REGISTRATION_CONFIG.CAMERA_HEIGHT },
        facingMode: REGISTRATION_CONFIG.CAMERA_FACING_MODE,
        frameRate: { ideal: 30, max: 60 }
      },
      audio: false
    });
    
    if (videoRef.current) {
      videoRef.current.srcObject = stream;
      
      // Wait for video to be ready
      return new Promise((resolve, reject) => {
        videoRef.current.onloadedmetadata = () => {
          console.log('✅ Camera initialized successfully');
          resolve({
            success: true,
            stream,
            width: videoRef.current.videoWidth,
            height: videoRef.current.videoHeight
          });
        };
        
        videoRef.current.onerror = (error) => {
          console.error('❌ Video element error:', error);
          reject(new Error('Failed to load video stream'));
        };
        
        // Timeout after 10 seconds
        setTimeout(() => {
          reject(new Error('Camera initialization timeout'));
        }, 10000);
      });
    }
    
    return { success: true, stream };
    
  } catch (error) {
    console.error(`❌ Camera initialization failed (attempt ${retryCount + 1}):`, error);
    
    // Retry logic
    if (retryCount < REGISTRATION_CONFIG.CAMERA_RETRY_ATTEMPTS - 1) {
      console.log(`🔄 Retrying camera initialization in ${REGISTRATION_CONFIG.CAMERA_RETRY_DELAY}ms...`);
      
      await new Promise(resolve => setTimeout(resolve, REGISTRATION_CONFIG.CAMERA_RETRY_DELAY));
      return initializeCamera(videoRef, onError, retryCount + 1);
    }
    
    // All retries failed
    const errorMessage = getCameraErrorMessage(error);
    
    if (onError) {
      onError({
        type: 'camera_error',
        message: errorMessage,
        originalError: error,
        canRetry: true
      });
    }
    
    return {
      success: false,
      error: errorMessage,
      originalError: error
    };
  }
};

/**
 * Get user-friendly camera error message
 */
const getCameraErrorMessage = (error) => {
  if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
    return 'Camera access denied. Please enable camera permissions in your browser settings and refresh the page.';
  }
  
  if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
    return 'No camera found. Please connect a camera and try again.';
  }
  
  if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
    return 'Camera is being used by another application. Please close other applications and try again.';
  }
  
  if (error.name === 'OverconstrainedError' || error.name === 'ConstraintNotSatisfiedError') {
    return 'Camera does not support the required settings. Please try with a different camera.';
  }
  
  return 'Camera initialization failed. Please check your camera and try again.';
};

/**
 * Validate student registration form data
 */
export const validateRegistrationForm = (formData) => {
  const errors = [];
  
  // First name validation
  if (!formData.first_name || formData.first_name.trim().length < 2) {
    errors.push('First name must be at least 2 characters long');
  }
  
  // Last name validation
  if (!formData.last_name || formData.last_name.trim().length < 2) {
    errors.push('Last name must be at least 2 characters long');
  }
  
  // Email validation
  if (!formData.email || !REGISTRATION_CONFIG.EMAIL_PATTERN.test(formData.email)) {
    errors.push('Please provide a valid email address');
  } else {
    const emailDomain = formData.email.split('@')[1];
    if (!REGISTRATION_CONFIG.ALLOWED_EMAIL_DOMAINS.includes(emailDomain)) {
      errors.push(`Email domain ${emailDomain} is not allowed. Please use an approved domain.`);
    }
  }
  
  // Student ID validation
  if (!formData.student_id || !REGISTRATION_CONFIG.STUDENT_ID_PATTERN.test(formData.student_id)) {
    errors.push('Student ID must follow format: Letter followed by 12-14 digits (e.g., E22273735500014)');
  }
  
  // Course validation
  if (!formData.course || formData.course.trim().length === 0) {
    errors.push('Please select a course');
  }
  
  // Semester validation
  if (!formData.semester || formData.semester < 1 || formData.semester > 8) {
    errors.push('Semester must be between 1 and 8');
  }
  
  // Class validation
  if (!formData.class || formData.class.trim().length === 0) {
    errors.push('Please select a class');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Validate captured face image quality
 */
export const validateFaceImageQuality = async (imageElement) => {
  try {
    // Check image dimensions
    if (imageElement.width < REGISTRATION_CONFIG.MIN_IMAGE_WIDTH || 
        imageElement.height < REGISTRATION_CONFIG.MIN_IMAGE_HEIGHT) {
      return {
        valid: false,
        error: `Image too small. Minimum size: ${REGISTRATION_CONFIG.MIN_IMAGE_WIDTH}x${REGISTRATION_CONFIG.MIN_IMAGE_HEIGHT}px`
      };
    }
    
    // Calculate image sharpness (simple variance-based method)
    const canvas = document.createElement('canvas');
    canvas.width = imageElement.width;
    canvas.height = imageElement.height;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(imageElement, 0, 0);
    
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;
    
    // Calculate variance for sharpness estimation
    let sum = 0;
    let sumSquares = 0;
    const pixelCount = pixels.length / 4;
    
    for (let i = 0; i < pixels.length; i += 4) {
      const gray = (pixels[i] + pixels[i + 1] + pixels[i + 2]) / 3;
      sum += gray;
      sumSquares += gray * gray;
    }
    
    const mean = sum / pixelCount;
    const variance = (sumSquares / pixelCount) - (mean * mean);
    const sharpness = Math.min(variance / 1000, 1); // Normalize to 0-1
    
    if (sharpness < REGISTRATION_CONFIG.MIN_SHARPNESS) {
      return {
        valid: false,
        error: `Image not sharp enough (${(sharpness * 100).toFixed(1)}%). Please ensure good lighting and focus.`,
        sharpness
      };
    }
    
    return {
      valid: true,
      sharpness,
      dimensions: {
        width: imageElement.width,
        height: imageElement.height
      }
    };
    
  } catch (error) {
    return {
      valid: false,
      error: 'Failed to validate image quality: ' + error.message
    };
  }
};

/**
 * Process face from captured image
 */
export const processFaceFromImage = async (imageElement) => {
  try {
    // Load face-api models if not already loaded
    if (!faceapi.nets.tinyFaceDetector.isLoaded) {
      console.log('Loading face detection models...');
      const MODEL_URL = '/models';
      await Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),
        faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
        faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL)
      ]);
    }
    
    // Detect faces
    const detections = await faceapi
      .detectAllFaces(imageElement, new faceapi.TinyFaceDetectorOptions())
      .withFaceLandmarks()
      .withFaceDescriptors();
    
    if (detections.length === 0) {
      return {
        success: false,
        error: 'No face detected. Please ensure your face is clearly visible and try again.'
      };
    }
    
    if (detections.length > 1) {
      return {
        success: false,
        error: 'Multiple faces detected. Please ensure only one person is in the frame.'
      };
    }
    
    const detection = detections[0];
    const descriptor = Array.from(detection.descriptor);
    
    return {
      success: true,
      descriptor,
      detectionScore: detection.detection.score,
      faceBox: detection.detection.box,
      landmarks: detection.landmarks.positions
    };
    
  } catch (error) {
    console.error('Face processing error:', error);
    return {
      success: false,
      error: 'Face processing failed: ' + error.message
    };
  }
};

/**
 * Upload face image to storage
 */
export const uploadFaceImage = async (imageBlob, studentId) => {
  try {
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      console.log('Mock face image upload for student:', studentId);
      return {
        success: true,
        path: `faces/${studentId}.${REGISTRATION_CONFIG.IMAGE_FORMAT}`,
        url: `mock://face-images/faces/${studentId}.${REGISTRATION_CONFIG.IMAGE_FORMAT}`
      };
    }
    
    const fileName = `faces/${studentId}.${REGISTRATION_CONFIG.IMAGE_FORMAT}`;
    
    const { data, error } = await supabase.storage
      .from(REGISTRATION_CONFIG.STORAGE_BUCKET)
      .upload(fileName, imageBlob, {
        contentType: `image/${REGISTRATION_CONFIG.IMAGE_FORMAT}`,
        upsert: true // Allow overwrite for re-registration
      });
    
    if (error) throw error;
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from(REGISTRATION_CONFIG.STORAGE_BUCKET)
      .getPublicUrl(fileName);
    
    return {
      success: true,
      path: data.path,
      url: publicUrl
    };
    
  } catch (error) {
    console.error('Face image upload error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Register student with enhanced validation and face processing
 */
export const registerStudentEnhanced = async (formData, faceImageBlob = null) => {
  const startTime = Date.now();
  
  try {
    console.log('🎓 Starting enhanced student registration for:', formData.student_id);
    
    // Step 1: Validate form data
    const validation = validateRegistrationForm(formData);
    if (!validation.valid) {
      throw new Error(validation.errors.join(', '));
    }
    
    // Step 2: Process face image if provided
    let faceData = null;
    let imagePath = null;
    
    if (faceImageBlob) {
      console.log('📸 Processing face image...');
      
      // Create image element for processing
      const imageElement = await new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = URL.createObjectURL(faceImageBlob);
      });
      
      // Validate image quality
      const qualityValidation = await validateFaceImageQuality(imageElement);
      if (!qualityValidation.valid) {
        throw new Error(qualityValidation.error);
      }
      
      // Process face
      const faceResult = await processFaceFromImage(imageElement);
      if (!faceResult.success) {
        throw new Error(faceResult.error);
      }
      
      // Upload image
      const uploadResult = await uploadFaceImage(faceImageBlob, formData.student_id);
      if (!uploadResult.success) {
        throw new Error('Failed to upload face image: ' + uploadResult.error);
      }
      
      faceData = {
        descriptor: faceResult.descriptor,
        detectionScore: faceResult.detectionScore,
        qualityScore: qualityValidation.sharpness
      };
      imagePath = uploadResult.path;
      
      console.log('✅ Face processing completed');
    }
    
    // Step 3: Store student data
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    let dbResult;
    if (USE_MOCK) {
      console.log('Mock student registration');
      dbResult = {
        success: true,
        message: 'Student registered successfully',
        student_id: formData.student_id,
        database_id: Math.floor(Math.random() * 1000) + 1000,
        registration_status: faceData ? 'completed' : 'pending',
        requires_face_capture: !faceData
      };
    } else {
      const { data, error } = await supabase.rpc('register_student_enhanced', {
        p_first_name: formData.first_name,
        p_last_name: formData.last_name,
        p_email: formData.email,
        p_student_id: formData.student_id,
        p_course: formData.course,
        p_semester: formData.semester,
        p_class: formData.class,
        p_face_descriptor: faceData ? new Uint8Array(faceData.descriptor) : null,
        p_face_image_path: imagePath,
        p_face_quality_score: faceData ? faceData.qualityScore : null
      });
      
      if (error) throw error;
      dbResult = data;
    }
    
    const processingTime = Date.now() - startTime;
    
    console.log(`✅ Student registration completed in ${processingTime}ms`);
    
    return {
      success: true,
      message: dbResult.message,
      studentId: formData.student_id,
      databaseId: dbResult.database_id,
      registrationStatus: dbResult.registration_status,
      requiresFaceCapture: dbResult.requires_face_capture,
      processingTime,
      faceData: faceData ? {
        detectionScore: faceData.detectionScore,
        qualityScore: faceData.qualityScore
      } : null
    };
    
  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error(`❌ Student registration failed in ${processingTime}ms:`, error);
    
    return {
      success: false,
      error: error.message,
      processingTime
    };
  }
};

/**
 * Get courses and classes data
 */
export const getCoursesAndClasses = async () => {
  try {
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      return {
        success: true,
        courses: [
          {
            id: 1,
            name: 'Computer Science',
            code: 'CS',
            duration_semesters: 8,
            classes: [
              { id: 1, name: 'Class A', semester: 1, academic_year: '2024-25' },
              { id: 2, name: 'Class B', semester: 1, academic_year: '2024-25' },
              { id: 3, name: 'Class A', semester: 6, academic_year: '2024-25' }
            ]
          },
          {
            id: 2,
            name: 'Information Technology',
            code: 'IT',
            duration_semesters: 8,
            classes: [
              { id: 4, name: 'Class A', semester: 1, academic_year: '2024-25' },
              { id: 5, name: 'Class B', semester: 1, academic_year: '2024-25' }
            ]
          }
        ]
      };
    }
    
    const { data, error } = await supabase.rpc('get_courses_and_classes');
    
    if (error) throw error;
    
    return data;
    
  } catch (error) {
    console.error('Error fetching courses and classes:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Log registration step for tracking
 */
export const logRegistrationStep = async (studentId, step, stepData = null, errorMessage = null, processingTime = null) => {
  try {
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      console.log(`📝 Mock log: ${studentId} - ${step}`, stepData);
      return { success: true };
    }
    
    const { data, error } = await supabase.rpc('log_registration_step', {
      p_student_id: studentId,
      p_step: step,
      p_step_data: stepData,
      p_error_message: errorMessage,
      p_processing_time_ms: processingTime
    });
    
    if (error) throw error;
    
    return { success: true, logId: data };
    
  } catch (error) {
    console.error('Error logging registration step:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Cleanup camera stream
 */
export const cleanupCamera = (videoRef) => {
  if (videoRef.current && videoRef.current.srcObject) {
    const tracks = videoRef.current.srcObject.getTracks();
    tracks.forEach(track => {
      track.stop();
      console.log('🎥 Camera track stopped');
    });
    videoRef.current.srcObject = null;
  }
};
