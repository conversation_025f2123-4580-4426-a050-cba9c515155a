/**
 * Browser Test Utilities
 * Quick tests to verify the application is working in the browser
 */

import { supabase } from './supabaseClient';

/**
 * Test Supabase connection
 */
export const testSupabaseConnection = async () => {
  console.log('🧪 Testing Supabase Connection...');
  
  try {
    // Test if client exists
    if (!supabase) {
      throw new Error('Supabase client not found');
    }
    
    console.log('✅ Supabase client loaded');
    
    // Check if we're using mock or real Supabase
    const useMock = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true';
    console.log(`🔧 Mode: ${useMock ? 'Mock Supabase' : 'Real Supabase'}`);
    
    // Test auth functionality
    try {
      const { data, error } = await supabase.auth.getSession();
      console.log('✅ Auth system accessible');
      console.log('Session data:', data);
    } catch (authError) {
      console.log('⚠️ Auth test warning:', authError.message);
    }
    
    // Test a simple query (will work with mock)
    try {
      const { data, error } = await supabase
        .from('students')
        .select('*')
        .limit(1);
      
      console.log('✅ Database query successful');
      console.log('Sample data:', data);
    } catch (dbError) {
      console.log('⚠️ Database test warning:', dbError.message);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Supabase connection test failed:', error);
    return false;
  }
};

/**
 * Test authentication with dummy credentials
 */
export const testAuthentication = async () => {
  console.log('🧪 Testing Authentication...');
  
  try {
    // Test with invalid credentials (should fail gracefully)
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'dummy_password'
    });
    
    if (error) {
      console.log('✅ Auth error handling working:', error.message);
      return true;
    } else {
      console.log('⚠️ Unexpected auth success with dummy credentials');
      return false;
    }
  } catch (error) {
    console.log('✅ Auth system properly rejecting invalid credentials');
    return true;
  }
};

/**
 * Test secure authentication for authorized user
 */
export const testSecureAuth = async (password = 'TestPassword123!') => {
  console.log('🧪 Testing Secure Authentication...');
  
  try {
    // Import auth service
    const { authenticateUser, getCSRFToken } = await import('./authService');
    
    // Get CSRF token
    const csrfToken = getCSRFToken();
    console.log('✅ CSRF token generated');
    
    // Test with authorized email
    const result = await authenticateUser('<EMAIL>', password, csrfToken);
    
    if (result.success) {
      console.log('✅ Secure authentication successful');
      console.log('User:', result.user);
      return true;
    } else {
      console.log('⚠️ Secure authentication failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Secure auth test failed:', error);
    return false;
  }
};

/**
 * Run comprehensive system test
 */
export const runSystemTest = async () => {
  console.log('🚀 Running Comprehensive System Test...');
  console.log('=====================================');
  
  const results = {
    supabase: false,
    auth: false,
    secureAuth: false
  };
  
  // Test Supabase connection
  results.supabase = await testSupabaseConnection();
  
  // Test basic authentication
  results.auth = await testAuthentication();
  
  // Test secure authentication
  results.secureAuth = await testSecureAuth();
  
  // Summary
  console.log('\n📊 Test Results:');
  console.log('================');
  console.log(`Supabase Connection: ${results.supabase ? '✅' : '❌'}`);
  console.log(`Basic Authentication: ${results.auth ? '✅' : '❌'}`);
  console.log(`Secure Authentication: ${results.secureAuth ? '✅' : '❌'}`);
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All systems operational!');
  } else {
    console.log('⚠️ Some systems need attention');
  }
  
  return results;
};

/**
 * Quick environment check
 */
export const checkEnvironment = () => {
  console.log('🔍 Environment Check:');
  console.log('====================');
  console.log('Development Mode:', import.meta.env.DEV);
  console.log('Mock Supabase:', import.meta.env.VITE_USE_MOCK_SUPABASE);
  console.log('Dev Mode Flag:', import.meta.env.VITE_DEV_MODE);
  console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL);
  console.log('Has Anon Key:', !!import.meta.env.VITE_SUPABASE_ANON_KEY);
};

// Make functions available globally for browser console
if (typeof window !== 'undefined') {
  window.examinoTest = {
    testSupabaseConnection,
    testAuthentication,
    testSecureAuth,
    runSystemTest,
    checkEnvironment
  };
  
  console.log('🔧 Browser test utilities loaded!');
  console.log('Available commands:');
  console.log('- window.examinoTest.runSystemTest()');
  console.log('- window.examinoTest.checkEnvironment()');
  console.log('- window.examinoTest.testSupabaseConnection()');
  console.log('- window.examinoTest.testSecureAuth("your-password")');
}
