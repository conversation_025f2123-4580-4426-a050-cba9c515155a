/**
 * Student Enrollment Service
 * Comprehensive student face enrollment with manual identity input
 */

import * as faceapi from 'face-api.js';
import { supabase } from './supabaseClient';

// Configuration constants
const ENROLLMENT_CONFIG = {
  MIN_DETECTION_SCORE: 0.7,
  MIN_IMAGE_WIDTH: 200,
  MIN_IMAGE_HEIGHT: 200,
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  STORAGE_BUCKET: 'student_faces'
};

/**
 * Load face-api.js models if not already loaded
 */
export const ensureModelsLoaded = async () => {
  try {
    if (faceapi.nets.tinyFaceDetector.isLoaded) {
      return true;
    }

    const MODEL_URL = '/models';
    await Promise.all([
      faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),
      faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
      faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL)
    ]);

    console.log('✅ Face-api.js models loaded successfully');
    return true;
  } catch (error) {
    console.warn('⚠️ Face-api.js models failed to load:', error);
    return false;
  }
};

/**
 * Validate student enrollment data
 */
export const validateEnrollmentData = (studentData) => {
  const errors = [];
  
  if (!studentData.studentId || studentData.studentId.trim() === '') {
    errors.push('Student ID is required');
  }
  
  if (!studentData.name || studentData.name.trim() === '') {
    errors.push('Student name is required');
  }
  
  if (!studentData.email || studentData.email.trim() === '') {
    errors.push('Email is required');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(studentData.email)) {
    errors.push('Please enter a valid email address');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Validate image file for enrollment
 */
export const validateImageFile = (file) => {
  const errors = [];

  if (!file) {
    errors.push('Image file is required');
    return { valid: false, errors };
  }

  // Check file type
  if (!ENROLLMENT_CONFIG.SUPPORTED_FORMATS.includes(file.type)) {
    errors.push(`Unsupported file format. Please use: ${ENROLLMENT_CONFIG.SUPPORTED_FORMATS.join(', ')}`);
  }

  // Check file size
  if (file.size > ENROLLMENT_CONFIG.MAX_FILE_SIZE) {
    errors.push(`File size too large. Maximum size: ${ENROLLMENT_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB`);
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Create image element from file or data URL
 */
export const createImageElement = (source) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      // Validate image dimensions
      if (img.width < ENROLLMENT_CONFIG.MIN_IMAGE_WIDTH || img.height < ENROLLMENT_CONFIG.MIN_IMAGE_HEIGHT) {
        reject(new Error(`Image too small. Minimum size: ${ENROLLMENT_CONFIG.MIN_IMAGE_WIDTH}x${ENROLLMENT_CONFIG.MIN_IMAGE_HEIGHT}`));
        return;
      }
      resolve(img);
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    
    if (source instanceof File) {
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target.result;
      };
      reader.onerror = () => {
        reject(new Error('Failed to read image file'));
      };
      reader.readAsDataURL(source);
    } else if (typeof source === 'string') {
      img.src = source;
    } else {
      reject(new Error('Invalid image source'));
    }
  });
};

/**
 * Detect faces in image and return detailed results
 */
export const detectFacesInImage = async (imageElement) => {
  try {
    const startTime = Date.now();
    
    // Detect all faces with confidence scores
    const detections = await faceapi
      .detectAllFaces(imageElement, new faceapi.TinyFaceDetectorOptions())
      .withFaceLandmarks();
    
    const processingTime = Date.now() - startTime;
    
    // Calculate image quality metrics
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = imageElement.width;
    canvas.height = imageElement.height;
    ctx.drawImage(imageElement, 0, 0);
    
    // Simple quality assessment based on image variance
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;
    let sum = 0;
    let sumSquares = 0;
    
    for (let i = 0; i < pixels.length; i += 4) {
      const gray = (pixels[i] + pixels[i + 1] + pixels[i + 2]) / 3;
      sum += gray;
      sumSquares += gray * gray;
    }
    
    const mean = sum / (pixels.length / 4);
    const variance = (sumSquares / (pixels.length / 4)) - (mean * mean);
    const imageQualityScore = Math.min(variance / 1000, 1); // Normalize to 0-1
    
    return {
      success: true,
      facesDetected: detections.length,
      detections: detections,
      imageQualityScore: imageQualityScore,
      processingTimeMs: processingTime,
      imageDimensions: {
        width: imageElement.width,
        height: imageElement.height
      }
    };
  } catch (error) {
    console.error('Face detection error:', error);
    return {
      success: false,
      error: error.message,
      facesDetected: 0,
      detections: [],
      imageQualityScore: 0,
      processingTimeMs: 0
    };
  }
};

/**
 * Upload image to Supabase Storage (student_faces bucket)
 */
export const uploadStudentFaceImage = async (file, studentId) => {
  try {
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      // Mock upload for development
      const mockUrl = `mock://student_faces/${studentId}_${Date.now()}.jpg`;
      console.log('Mock image upload to student_faces bucket:', mockUrl);
      return {
        success: true,
        url: mockUrl,
        path: `${studentId}_${Date.now()}.jpg`
      };
    }
    
    // Generate unique filename
    const fileExtension = file.name.split('.').pop();
    const fileName = `${studentId}_${Date.now()}.${fileExtension}`;
    
    // Upload to Supabase Storage (student_faces bucket)
    const { data, error } = await supabase.storage
      .from(ENROLLMENT_CONFIG.STORAGE_BUCKET)
      .upload(fileName, file, {
        contentType: file.type,
        upsert: false
      });
    
    if (error) throw error;
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from(ENROLLMENT_CONFIG.STORAGE_BUCKET)
      .getPublicUrl(fileName);
    
    return {
      success: true,
      url: publicUrl,
      path: data.path
    };
  } catch (error) {
    console.error('Image upload error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Enroll student with face verification
 * Main function that orchestrates the entire enrollment process
 */
export const enrollStudentWithFace = async (studentData, imageSource) => {
  try {
    const { studentId, name, email } = studentData;
    const startTime = Date.now();
    
    // Step 1: Validate student data
    const dataValidation = validateEnrollmentData(studentData);
    if (!dataValidation.valid) {
      throw new Error(dataValidation.errors.join(', '));
    }
    
    // Step 2: Validate image file if it's a File object
    if (imageSource instanceof File) {
      const imageValidation = validateImageFile(imageSource);
      if (!imageValidation.valid) {
        throw new Error(imageValidation.errors.join(', '));
      }
    }
    
    // Step 3: Create image element
    const imageElement = await createImageElement(imageSource);
    
    // Step 4: Ensure face detection models are loaded
    const modelsLoaded = await ensureModelsLoaded();
    
    let faceDetectionResult;
    
    if (modelsLoaded) {
      // Step 5: Detect faces in image
      faceDetectionResult = await detectFacesInImage(imageElement);
      
      if (!faceDetectionResult.success) {
        throw new Error(faceDetectionResult.error);
      }
    } else {
      // Mock face detection for development
      console.log('Using mock face detection for enrollment');
      faceDetectionResult = {
        success: true,
        facesDetected: 1,
        detections: [{ detection: { score: 0.95 } }],
        imageQualityScore: 0.85,
        processingTimeMs: 500,
        imageDimensions: {
          width: imageElement.width,
          height: imageElement.height
        }
      };
    }
    
    // Step 6: Upload image to student_faces storage bucket
    let uploadResult;
    if (imageSource instanceof File) {
      uploadResult = await uploadStudentFaceImage(imageSource, studentId);
      if (!uploadResult.success) {
        throw new Error(`Image upload failed: ${uploadResult.error}`);
      }
    } else {
      // For data URLs or existing URLs
      uploadResult = {
        success: true,
        url: typeof imageSource === 'string' ? imageSource : `data:image/jpeg;base64,${imageSource}`
      };
    }
    
    // Step 7: Calculate detection score
    const faceDetectionScore = faceDetectionResult.detections.length > 0 
      ? faceDetectionResult.detections[0].detection?.score || 0.95
      : 0;
    
    // Step 8: Store enrollment result in database
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    let dbResult;
    if (USE_MOCK) {
      // Mock database enrollment
      const facesDetected = faceDetectionResult.facesDetected;
      let status, message;
      
      if (facesDetected === 0) {
        status = 'no_face';
        message = 'No face found, please try again.';
      } else if (facesDetected > 1) {
        status = 'multiple_faces';
        message = 'Multiple faces detected. Please upload a photo with only one face.';
      } else if (faceDetectionScore < ENROLLMENT_CONFIG.MIN_DETECTION_SCORE) {
        status = 'poor_quality';
        message = 'Face detection quality too low, please try again with better lighting.';
      } else {
        status = 'success';
        message = 'Student enrolled successfully. Image uploaded. Data saved to Supabase.';
      }
      
      dbResult = {
        success: status === 'success',
        enrollment_status: status,
        message: message,
        student_id: studentId,
        name: name,
        email: email,
        image_url: uploadResult.url,
        faces_detected: facesDetected,
        face_detection_score: faceDetectionScore,
        image_quality_score: faceDetectionResult.imageQualityScore,
        processing_time_ms: Date.now() - startTime,
        manual_override_allowed: true
      };
    } else {
      // Use database function
      const { data, error } = await supabase.rpc('enroll_student_with_face', {
        p_student_id: studentId,
        p_name: name,
        p_email: email,
        p_image_url: uploadResult.url,
        p_faces_detected: faceDetectionResult.facesDetected,
        p_face_detection_score: faceDetectionScore,
        p_image_quality_score: faceDetectionResult.imageQualityScore,
        p_image_dimensions: faceDetectionResult.imageDimensions,
        p_processing_time_ms: Date.now() - startTime,
        p_user_agent: navigator.userAgent
      });
      
      if (error) throw error;
      dbResult = data;
    }
    
    // Step 9: Return comprehensive result
    return {
      success: dbResult.success,
      message: dbResult.message,
      enrollmentStatus: dbResult.enrollment_status,
      studentId: studentId,
      name: name,
      email: email,
      imageUrl: dbResult.image_url,
      facesDetected: dbResult.faces_detected,
      faceDetectionScore: dbResult.face_detection_score,
      imageQualityScore: dbResult.image_quality_score,
      processingTimeMs: dbResult.processing_time_ms,
      attemptId: dbResult.attempt_id,
      manualOverrideAllowed: dbResult.manual_override_allowed
    };
    
  } catch (error) {
    console.error('Student enrollment error:', error);
    return {
      success: false,
      message: error.message,
      enrollmentStatus: 'validation_error',
      error: error.message
    };
  }
};

/**
 * Enable manual override for failed enrollments
 */
export const enableManualOverride = async (studentId, imageUrl, overrideReason) => {
  try {
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      return {
        success: true,
        message: 'Manual override applied successfully (mock mode)',
        studentId: studentId,
        overrideReason: overrideReason
      };
    }
    
    const { data, error } = await supabase.rpc('enable_manual_override', {
      p_student_id: studentId,
      p_image_url: imageUrl,
      p_override_reason: overrideReason
    });
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Manual override error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get enrollment statistics
 */
export const getEnrollmentStatistics = async (daysBack = 30) => {
  try {
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      return {
        success: true,
        stats: {
          total_attempts: 89,
          successful_enrollments: 67,
          no_face_attempts: 12,
          multiple_faces_attempts: 6,
          poor_quality_attempts: 3,
          upload_failed_attempts: 1,
          success_rate: 75.28,
          avg_detection_score: 0.887,
          avg_quality_score: 0.821,
          avg_processing_time_ms: 1180
        }
      };
    }
    
    const { data, error } = await supabase.rpc('get_enrollment_statistics', {
      p_days_back: daysBack
    });
    
    if (error) throw error;
    
    return {
      success: true,
      stats: data
    };
  } catch (error) {
    console.error('Error getting enrollment statistics:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get student enrollment history
 */
export const getStudentEnrollmentHistory = async (studentId) => {
  try {
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
    
    if (USE_MOCK) {
      return {
        success: true,
        history: [
          {
            attempt_id: 'mock-1',
            name: 'Anupam',
            email: '<EMAIL>',
            image_url: 'mock://student_faces/anupam.jpg',
            faces_detected: 1,
            face_detection_score: 0.95,
            enrollment_status: 'success',
            created_at: new Date().toISOString()
          }
        ]
      };
    }
    
    const { data, error } = await supabase.rpc('get_student_enrollment_history', {
      p_student_id: studentId
    });
    
    if (error) throw error;
    
    return {
      success: true,
      history: data
    };
  } catch (error) {
    console.error('Error getting enrollment history:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
