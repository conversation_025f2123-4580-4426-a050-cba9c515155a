import { supabase as realSupabase } from '../lib/supabase';
import { createMockSupabaseClient } from './mockSupabaseClient';

// Determine whether to use the real Supabase client or the mock one
// This allows for easy switching between development and production
const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;

// Create the mock Supabase client
const mockSupabase = createMockSupabaseClient();

// Export the appropriate Supabase client
export const supabase = USE_MOCK ? mockSupabase : realSupabase;

// Log which client is being used
console.log(`Using ${USE_MOCK ? 'mock' : 'real'} Supabase client`);
