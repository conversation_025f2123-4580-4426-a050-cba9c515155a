/**
 * Authentication System Test Utility
 * Simple test functions to verify the authentication system works correctly
 */

import { authenticateUser, getCSRFToken } from './authService';
import { hashPassword, verifyPassword, checkPasswordStrength } from './passwordUtils';

/**
 * Test password hashing and verification
 */
export const testPasswordUtils = async () => {
  console.log('🧪 Testing Password Utilities...');
  
  try {
    const testPassword = 'TestPassword123!';
    
    // Test password hashing
    console.log('Testing password hashing...');
    const { hash, salt } = await hashPassword(testPassword);
    console.log('✅ Password hashed successfully');
    console.log('Hash length:', hash.length);
    console.log('Salt length:', salt.length);
    
    // Test password verification
    console.log('Testing password verification...');
    const isValid = await verifyPassword(testPassword, hash, salt);
    console.log('✅ Password verification:', isValid ? 'PASSED' : 'FAILED');
    
    // Test wrong password
    const isInvalid = await verifyPassword('WrongPassword', hash, salt);
    console.log('✅ Wrong password verification:', !isInvalid ? 'PASSED' : 'FAILED');
    
    // Test password strength
    console.log('Testing password strength...');
    const strength = checkPasswordStrength(testPassword);
    console.log('✅ Password strength:', strength.strength);
    console.log('Score:', strength.score);
    console.log('Valid:', strength.isValid);
    
    return true;
  } catch (error) {
    console.error('❌ Password utils test failed:', error);
    return false;
  }
};

/**
 * Test authentication flow
 */
export const testAuthenticationFlow = async () => {
  console.log('🧪 Testing Authentication Flow...');
  
  try {
    const email = '<EMAIL>';
    const password = 'TestPassword123!';
    
    // Get CSRF token
    console.log('Getting CSRF token...');
    const csrfToken = getCSRFToken();
    console.log('✅ CSRF token generated:', csrfToken.length > 0);
    
    // Test authentication
    console.log('Testing authentication...');
    const result = await authenticateUser(email, password, csrfToken);
    
    if (result.success) {
      console.log('✅ Authentication successful');
      console.log('User:', result.user);
      console.log('Session token:', result.sessionToken ? 'Generated' : 'Missing');
      return true;
    } else {
      console.log('❌ Authentication failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Authentication flow test failed:', error);
    return false;
  }
};

/**
 * Test unauthorized email
 */
export const testUnauthorizedEmail = async () => {
  console.log('🧪 Testing Unauthorized Email...');
  
  try {
    const email = '<EMAIL>';
    const password = 'TestPassword123!';
    const csrfToken = getCSRFToken();
    
    const result = await authenticateUser(email, password, csrfToken);
    
    if (!result.success && result.error.includes('Unauthorized')) {
      console.log('✅ Unauthorized email correctly rejected');
      return true;
    } else {
      console.log('❌ Unauthorized email was not rejected');
      return false;
    }
  } catch (error) {
    console.error('❌ Unauthorized email test failed:', error);
    return false;
  }
};

/**
 * Test weak password
 */
export const testWeakPassword = async () => {
  console.log('🧪 Testing Weak Password...');
  
  try {
    const weakPasswords = ['123', 'password', 'abc', '12345678'];
    
    for (const password of weakPasswords) {
      const strength = checkPasswordStrength(password);
      if (strength.strength === 'Weak' || !strength.isValid) {
        console.log(`✅ Weak password "${password}" correctly identified`);
      } else {
        console.log(`❌ Weak password "${password}" not identified as weak`);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Weak password test failed:', error);
    return false;
  }
};

/**
 * Run all tests
 */
export const runAllTests = async () => {
  console.log('🚀 Running All Authentication Tests...');
  console.log('=====================================');
  
  const tests = [
    { name: 'Password Utils', test: testPasswordUtils },
    { name: 'Authentication Flow', test: testAuthenticationFlow },
    { name: 'Unauthorized Email', test: testUnauthorizedEmail },
    { name: 'Weak Password', test: testWeakPassword }
  ];
  
  const results = [];
  
  for (const { name, test } of tests) {
    console.log(`\n📋 Running ${name} test...`);
    const result = await test();
    results.push({ name, passed: result });
    console.log(`${result ? '✅' : '❌'} ${name}: ${result ? 'PASSED' : 'FAILED'}`);
  }
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(({ name, passed }) => {
    console.log(`${passed ? '✅' : '❌'} ${name}`);
  });
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Authentication system is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.');
  }
  
  return passed === total;
};

// Make test functions available globally for browser console
if (typeof window !== 'undefined') {
  window.testExaminoAuth = {
    testPasswordUtils,
    testAuthenticationFlow,
    testUnauthorizedEmail,
    testWeakPassword,
    runAllTests
  };
  
  console.log('🔧 Authentication test utilities loaded. Use window.testExaminoAuth to run tests.');
}
