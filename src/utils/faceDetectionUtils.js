/**
 * Utility functions for face detection
 */
import * as faceapi from 'face-api.js';

// Configuration
const MODELS_PATH = '/models';

/**
 * Load face-api.js models
 * @returns {Promise<boolean>} - Whether models were loaded successfully
 */
export const loadFaceModels = async () => {
  try {
    // Check if we're in development mode
    const isDevelopment = import.meta.env.DEV || !import.meta.env.PROD;
    
    if (isDevelopment && !import.meta.env.VITE_FORCE_FACE_MODELS) {
      console.log('Development mode detected. Using mock face detection.');
      return true;
    }
    
    // Check if models are already loaded
    if (faceapi.nets.ssdMobilenetv1.isLoaded) {
      return true;
    }
    
    // Load models
    await Promise.all([
      faceapi.nets.ssdMobilenetv1.loadFromUri(MODELS_PATH),
      faceapi.nets.faceLandmark68Net.loadFromUri(MODELS_PATH),
      faceapi.nets.faceRecognitionNet.loadFromUri(MODELS_PATH),
    ]);
    
    console.log('Face-api models loaded successfully');
    return true;
  } catch (error) {
    console.error('Error loading face-api models:', error);
    // Instead of throwing, we'll return false and handle it gracefully
    return false;
  }
};

/**
 * Detect faces in an image
 * @param {HTMLImageElement|HTMLVideoElement|HTMLCanvasElement} imageElement - Image element
 * @returns {Promise<Object[]>} - Array of detected faces
 */
export const detectFaces = async (imageElement) => {
  try {
    // Ensure models are loaded
    await loadFaceModels();
    
    // Detect faces
    const detections = await faceapi.detectAllFaces(imageElement)
      .withFaceLandmarks()
      .withFaceDescriptors();
    
    return detections;
  } catch (error) {
    console.error('Error detecting faces:', error);
    return [];
  }
};

/**
 * Calculate Euclidean distance between two face descriptors
 * @param {Float32Array} descriptor1 - First face descriptor
 * @param {Float32Array} descriptor2 - Second face descriptor
 * @returns {number} - Distance between descriptors
 */
export const calculateFaceDistance = (descriptor1, descriptor2) => {
  return faceapi.euclideanDistance(descriptor1, descriptor2);
};

/**
 * Create a mock face descriptor for development
 * @returns {Float32Array} - Mock face descriptor
 */
export const createMockDescriptor = () => {
  return new Float32Array(128).fill(0.5);
};

/**
 * Get face API instance
 * @returns {Object} - face-api.js instance
 */
export const getFaceApi = () => {
  return faceapi;
};
