/**
 * Password Utilities
 * Secure password hashing and validation utilities using bcryptjs
 */

import bcrypt from 'bcryptjs';

// Configuration
const SALT_ROUNDS = 12; // High security salt rounds
const MIN_PASSWORD_LENGTH = 8;
const MAX_PASSWORD_LENGTH = 128;

/**
 * Hash a password with bcrypt
 * @param {string} password - Plain text password
 * @returns {Promise<{hash: string, salt: string}>} - Hashed password and salt
 */
export const hashPassword = async (password) => {
  try {
    // Validate password
    if (!password || typeof password !== 'string') {
      throw new Error('Password must be a non-empty string');
    }

    if (password.length < MIN_PASSWORD_LENGTH) {
      throw new Error(`Password must be at least ${MIN_PASSWORD_LENGTH} characters long`);
    }

    if (password.length > MAX_PASSWORD_LENGTH) {
      throw new Error(`Password must be no more than ${MAX_PASSWORD_LENGTH} characters long`);
    }

    // Generate salt
    const salt = await bcrypt.genSalt(SALT_ROUNDS);

    // Hash password
    const hash = await bcrypt.hash(password, salt);

    return {
      hash,
      salt
    };
  } catch (error) {
    console.error('Error hashing password:', error);
    throw error;
  }
};

/**
 * Verify a password against a hash
 * @param {string} password - Plain text password
 * @param {string} hash - Hashed password
 * @returns {Promise<boolean>} - True if password matches
 */
export const verifyPassword = async (password, hash) => {
  try {
    if (!password || !hash) {
      return false;
    }

    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error('Error verifying password:', error);
    return false;
  }
};

/**
 * Generate a secure random password
 *
 * @param {number} length - Length of the password (default: 12)
 * @returns {string} - Random password
 */
export function generatePassword(length = 12) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
  let password = '';

  // Generate random password
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }

  return password;
}

/**
 * Check password strength
 * @param {string} password - Password to check
 * @returns {Object} - Password strength analysis
 */
export const checkPasswordStrength = (password) => {
  const result = {
    score: 0,
    feedback: [],
    isValid: false
  };

  if (!password) {
    result.feedback.push('Password is required');
    return result;
  }

  // Length check
  if (password.length < MIN_PASSWORD_LENGTH) {
    result.feedback.push(`Password must be at least ${MIN_PASSWORD_LENGTH} characters long`);
  } else if (password.length >= MIN_PASSWORD_LENGTH) {
    result.score += 1;
  }

  // Uppercase letter check
  if (/[A-Z]/.test(password)) {
    result.score += 1;
  } else {
    result.feedback.push('Password should contain at least one uppercase letter');
  }

  // Lowercase letter check
  if (/[a-z]/.test(password)) {
    result.score += 1;
  } else {
    result.feedback.push('Password should contain at least one lowercase letter');
  }

  // Number check
  if (/\d/.test(password)) {
    result.score += 1;
  } else {
    result.feedback.push('Password should contain at least one number');
  }

  // Special character check
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    result.score += 1;
  } else {
    result.feedback.push('Password should contain at least one special character');
  }

  // Length bonus
  if (password.length >= 12) {
    result.score += 1;
  }

  // Common patterns check
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /admin/i,
    /letmein/i
  ];

  const hasCommonPattern = commonPatterns.some(pattern => pattern.test(password));
  if (hasCommonPattern) {
    result.score -= 2;
    result.feedback.push('Password contains common patterns');
  }

  // Determine strength level
  if (result.score >= 5) {
    result.strength = 'Strong';
    result.isValid = true;
  } else if (result.score >= 3) {
    result.strength = 'Medium';
    result.isValid = password.length >= MIN_PASSWORD_LENGTH;
  } else {
    result.strength = 'Weak';
    result.isValid = false;
  }

  return result;
};

/**
 * Generate a secure session token
 * @returns {string} - Base64 encoded session token
 */
export const generateSessionToken = () => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return btoa(String.fromCharCode.apply(null, array))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};

/**
 * Generate CSRF token
 * @returns {string} - CSRF token
 */
export const generateCSRFToken = () => {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return btoa(String.fromCharCode.apply(null, array))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} - True if email is valid
 */
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Get device fingerprint
 * @returns {Object} - Device information
 */
export const getDeviceFingerprint = () => {
  return {
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    screenResolution: `${screen.width}x${screen.height}`,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    cookieEnabled: navigator.cookieEnabled,
    doNotTrack: navigator.doNotTrack,
    timestamp: new Date().toISOString()
  };
};
