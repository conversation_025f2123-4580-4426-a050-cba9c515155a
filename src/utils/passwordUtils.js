/**
 * Password hashing and verification utilities
 * 
 * In a real application, we would use bcrypt or Argon2 for password hashing.
 * For this demo, we're using a simplified approach.
 */

// Secret salt for password hashing (in production, this would be stored securely)
const SALT = 'examino-secure-salt-2023';

/**
 * Hash a password using a simple algorithm
 * Note: This is a basic implementation for development purposes.
 * In production, use a proper hashing library like bcrypt.
 * 
 * @param {string} password - Plain text password
 * @returns {string} - Hashed password
 */
export function hashPassword(password) {
  // In development mode, just return the password with a prefix
  if (import.meta.env.DEV) {
    return `hashed:${password}`;
  }
  
  // Simple hash function using built-in crypto
  // In a real app, we would use bcrypt or Argon2
  try {
    // Use TextEncoder to convert string to Uint8Array
    const encoder = new TextEncoder();
    const data = encoder.encode(password + SALT);
    
    // Use SubtleCrypto to hash the password
    // This is synchronous in this example for simplicity
    // In a real app, we would use async/await with proper crypto
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      hash = ((hash << 5) - hash) + data[i];
      hash |= 0; // Convert to 32bit integer
    }
    
    // Convert to hex string
    return hash.toString(16);
  } catch (error) {
    console.error('Error hashing password:', error);
    // Fallback to simple encoding
    return btoa(password + SALT);
  }
}

/**
 * Verify a password against a hash
 * 
 * @param {string} password - Plain text password
 * @param {string} hash - Hashed password
 * @returns {boolean} - True if password matches hash
 */
export function verifyPassword(password, hash) {
  // In development mode, check if hash has the prefix
  if (import.meta.env.DEV && hash.startsWith('hashed:')) {
    return password === hash.substring(7);
  }
  
  // Compare the hash of the provided password with the stored hash
  return hashPassword(password) === hash;
}

/**
 * Generate a secure random password
 * 
 * @param {number} length - Length of the password (default: 12)
 * @returns {string} - Random password
 */
export function generatePassword(length = 12) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
  let password = '';
  
  // Generate random password
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  
  return password;
}

/**
 * Check password strength
 * 
 * @param {string} password - Password to check
 * @returns {object} - Password strength info
 */
export function checkPasswordStrength(password) {
  // Check password length
  const isLongEnough = password.length >= 8;
  
  // Check for uppercase letters
  const hasUppercase = /[A-Z]/.test(password);
  
  // Check for lowercase letters
  const hasLowercase = /[a-z]/.test(password);
  
  // Check for numbers
  const hasNumbers = /[0-9]/.test(password);
  
  // Check for special characters
  const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
  
  // Calculate score (0-4)
  let score = 0;
  if (isLongEnough) score++;
  if (hasUppercase) score++;
  if (hasLowercase) score++;
  if (hasNumbers) score++;
  if (hasSpecialChars) score++;
  
  // Get feedback based on score
  let feedback = '';
  if (score < 2) {
    feedback = 'Weak password. Try adding uppercase letters, numbers, and special characters.';
  } else if (score < 4) {
    feedback = 'Moderate password. Consider adding more variety.';
  } else {
    feedback = 'Strong password!';
  }
  
  return {
    score,
    feedback,
    isLongEnough,
    hasUppercase,
    hasLowercase,
    hasNumbers,
    hasSpecialChars
  };
}
