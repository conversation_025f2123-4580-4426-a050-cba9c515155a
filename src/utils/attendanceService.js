/**
 * Attendance Service
 * Provides functions for managing attendance logs in Supabase
 */
import { supabase } from './supabaseClient';
import { format, parseISO, startOfDay, endOfDay } from 'date-fns';

/**
 * Get attendance logs for an institute
 * @param {Object} params - Query parameters
 * @param {string} params.startDate - Start date (YYYY-MM-DD)
 * @param {string} params.endDate - End date (YYYY-MM-DD)
 * @param {string} params.classId - Class ID (optional)
 * @param {boolean} params.status - Attendance status (optional)
 * @returns {Promise<Array>} - Array of attendance logs
 */
export const getAttendanceLogs = async ({ startDate, endDate, classId, status }) => {
  try {
    // Convert dates to ISO strings
    const startDateObj = startOfDay(parseISO(startDate));
    const endDateObj = endOfDay(parseISO(endDate));
    
    // Build query
    let query = supabase
      .from('attendance_logs')
      .select(`
        *,
        students (
          id,
          first_name,
          last_name,
          student_id,
          reference_image_url
        ),
        classes (
          id,
          name
        )
      `)
      .gte('date', startDate)
      .lte('date', endDate)
      .order('created_at', { ascending: false });
    
    // Add filters if provided
    if (classId) {
      query = query.eq('class_id', classId);
    }
    
    if (status !== undefined && status !== null) {
      query = query.eq('status', status);
    }
    
    // Execute query
    const { data, error } = await query;
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    console.error('Error fetching attendance logs:', error);
    throw error;
  }
};

/**
 * Get attendance statistics for an institute
 * @param {Object} params - Query parameters
 * @param {string} params.startDate - Start date (YYYY-MM-DD)
 * @param {string} params.endDate - End date (YYYY-MM-DD)
 * @param {string} params.classId - Class ID (optional)
 * @returns {Promise<Object>} - Attendance statistics
 */
export const getAttendanceStatistics = async ({ startDate, endDate, classId }) => {
  try {
    // Call the database function
    const { data, error } = await supabase.rpc('get_institute_attendance_statistics', {
      p_institute_id: supabase.auth.user().id,
      p_start_date: startDate,
      p_end_date: endDate,
      p_class_id: classId || null
    });
    
    if (error) throw error;
    
    // Calculate overall statistics
    const totalRecords = data.reduce((sum, item) => sum + item.total_records, 0);
    const presentCount = data.reduce((sum, item) => sum + item.present_count, 0);
    const absentCount = data.reduce((sum, item) => sum + item.absent_count, 0);
    const attendanceRate = totalRecords > 0 ? (presentCount / totalRecords) * 100 : 0;
    
    return {
      overall: {
        totalRecords,
        presentCount,
        absentCount,
        attendanceRate
      },
      byClass: data.reduce((acc, item) => {
        if (!acc[item.class_id]) {
          acc[item.class_id] = {
            classId: item.class_id,
            className: item.class_name,
            totalRecords: 0,
            presentCount: 0,
            absentCount: 0,
            attendanceRate: 0
          };
        }
        
        acc[item.class_id].totalRecords += item.total_records;
        acc[item.class_id].presentCount += item.present_count;
        acc[item.class_id].absentCount += item.absent_count;
        acc[item.class_id].attendanceRate = acc[item.class_id].totalRecords > 0
          ? (acc[item.class_id].presentCount / acc[item.class_id].totalRecords) * 100
          : 0;
        
        return acc;
      }, {}),
      byDate: data.reduce((acc, item) => {
        const dateStr = item.date;
        
        if (!acc[dateStr]) {
          acc[dateStr] = {
            date: dateStr,
            totalRecords: 0,
            presentCount: 0,
            absentCount: 0,
            attendanceRate: 0
          };
        }
        
        acc[dateStr].totalRecords += item.total_records;
        acc[dateStr].presentCount += item.present_count;
        acc[dateStr].absentCount += item.absent_count;
        acc[dateStr].attendanceRate = acc[dateStr].totalRecords > 0
          ? (acc[dateStr].presentCount / acc[dateStr].totalRecords) * 100
          : 0;
        
        return acc;
      }, {})
    };
  } catch (error) {
    console.error('Error fetching attendance statistics:', error);
    throw error;
  }
};

/**
 * Get daily attendance summary for an institute
 * @param {Object} params - Query parameters
 * @param {string} params.startDate - Start date (YYYY-MM-DD)
 * @param {string} params.endDate - End date (YYYY-MM-DD)
 * @returns {Promise<Array>} - Array of daily attendance summaries
 */
export const getDailyAttendanceSummary = async ({ startDate, endDate }) => {
  try {
    // Call the database function
    const { data, error } = await supabase.rpc('get_institute_daily_attendance_summary', {
      p_institute_id: supabase.auth.user().id,
      p_start_date: startDate,
      p_end_date: endDate
    });
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    console.error('Error fetching daily attendance summary:', error);
    throw error;
  }
};

/**
 * Get student attendance summary for an institute
 * @param {Object} params - Query parameters
 * @param {string} params.startDate - Start date (YYYY-MM-DD)
 * @param {string} params.endDate - End date (YYYY-MM-DD)
 * @param {string} params.classId - Class ID (optional)
 * @returns {Promise<Array>} - Array of student attendance summaries
 */
export const getStudentAttendanceSummary = async ({ startDate, endDate, classId }) => {
  try {
    // Call the database function
    const { data, error } = await supabase.rpc('get_institute_student_attendance_summary', {
      p_institute_id: supabase.auth.user().id,
      p_start_date: startDate,
      p_end_date: endDate,
      p_class_id: classId || null
    });
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    console.error('Error fetching student attendance summary:', error);
    throw error;
  }
};

/**
 * Create an attendance log
 * @param {Object} attendanceData - Attendance data
 * @param {string} attendanceData.studentId - Student ID
 * @param {string} attendanceData.classId - Class ID
 * @param {string} attendanceData.date - Date (YYYY-MM-DD)
 * @param {boolean} attendanceData.status - Attendance status
 * @param {string} attendanceData.verificationMethod - Verification method
 * @param {number} attendanceData.confidence - Confidence score
 * @param {string} attendanceData.notes - Notes
 * @param {Object} attendanceData.location - Location data
 * @param {Object} attendanceData.deviceInfo - Device information
 * @returns {Promise<Object>} - Created attendance log
 */
export const createAttendanceLog = async (attendanceData) => {
  try {
    // Get current user ID
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) throw new Error('User not authenticated');
    
    // Create attendance log
    const { data, error } = await supabase
      .from('attendance_logs')
      .insert([
        {
          institute_id: user.id,
          student_id: attendanceData.studentId,
          class_id: attendanceData.classId,
          date: attendanceData.date || new Date().toISOString().split('T')[0],
          status: attendanceData.status,
          verification_method: attendanceData.verificationMethod,
          confidence: attendanceData.confidence,
          notes: attendanceData.notes,
          location: attendanceData.location,
          device_info: attendanceData.deviceInfo
        }
      ])
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Error creating attendance log:', error);
    throw error;
  }
};

/**
 * Update an attendance log
 * @param {string} id - Attendance log ID
 * @param {Object} attendanceData - Attendance data
 * @param {boolean} attendanceData.status - Attendance status
 * @param {string} attendanceData.notes - Notes
 * @returns {Promise<Object>} - Updated attendance log
 */
export const updateAttendanceLog = async (id, attendanceData) => {
  try {
    // Update attendance log
    const { data, error } = await supabase
      .from('attendance_logs')
      .update({
        status: attendanceData.status,
        notes: attendanceData.notes,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Error updating attendance log:', error);
    throw error;
  }
};

/**
 * Delete an attendance log
 * @param {string} id - Attendance log ID
 * @returns {Promise<void>}
 */
export const deleteAttendanceLog = async (id) => {
  try {
    // Delete attendance log
    const { error } = await supabase
      .from('attendance_logs')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  } catch (error) {
    console.error('Error deleting attendance log:', error);
    throw error;
  }
};

/**
 * Bulk create attendance logs
 * @param {Array} attendanceLogs - Array of attendance logs
 * @returns {Promise<Array>} - Created attendance logs
 */
export const bulkCreateAttendanceLogs = async (attendanceLogs) => {
  try {
    // Get current user ID
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) throw new Error('User not authenticated');
    
    // Prepare attendance logs
    const logs = attendanceLogs.map(log => ({
      institute_id: user.id,
      student_id: log.studentId,
      class_id: log.classId,
      date: log.date || new Date().toISOString().split('T')[0],
      status: log.status,
      verification_method: log.verificationMethod,
      confidence: log.confidence,
      notes: log.notes,
      location: log.location,
      device_info: log.deviceInfo
    }));
    
    // Create attendance logs
    const { data, error } = await supabase
      .from('attendance_logs')
      .insert(logs)
      .select();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Error bulk creating attendance logs:', error);
    throw error;
  }
};

/**
 * Get device information for logging
 * @returns {Object} - Device information
 */
export const getDeviceInfo = () => {
  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    devicePixelRatio: window.devicePixelRatio,
    language: navigator.language,
    timestamp: new Date().toISOString()
  };
};
