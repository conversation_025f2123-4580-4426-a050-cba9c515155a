/**
 * Utility functions for image handling, compression, and optimization
 */
import { v4 as uuidv4 } from 'uuid';
import { supabase } from './supabaseClient';

/**
 * Compresses an image and converts it to WebP format
 * @param {File|Blob} file - The image file to compress
 * @param {number} quality - Compression quality (0-1)
 * @param {number} maxWidth - Maximum width of the compressed image
 * @param {number} maxHeight - Maximum height of the compressed image
 * @returns {Promise<Blob>} - Compressed image as WebP blob
 */
export const compressImage = async (file, quality = 0.7, maxWidth = 800, maxHeight = 800) => {
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader();
      reader.onload = (event) => {
        const img = new Image();
        img.onload = () => {
          // Calculate new dimensions while maintaining aspect ratio
          let width = img.width;
          let height = img.height;
          
          if (width > height) {
            if (width > maxWidth) {
              height = Math.round((height * maxWidth) / width);
              width = maxWidth;
            }
          } else {
            if (height > maxHeight) {
              width = Math.round((width * maxHeight) / height);
              height = maxHeight;
            }
          }
          
          // Create canvas and draw image
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);
          
          // Convert to WebP
          canvas.toBlob((blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to compress image'));
            }
          }, 'image/webp', quality);
        };
        img.onerror = () => {
          reject(new Error('Failed to load image'));
        };
        img.src = event.target.result;
      };
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      reader.readAsDataURL(file);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Uploads a student photo to Supabase Storage
 * @param {string} studentId - Student ID
 * @param {File|Blob} file - Image file to upload
 * @param {Object} options - Upload options
 * @param {number} options.quality - Compression quality (0-1)
 * @param {number} options.maxWidth - Maximum width of the compressed image
 * @param {number} options.maxHeight - Maximum height of the compressed image
 * @returns {Promise<string>} - Public URL of the uploaded image
 */
export const uploadStudentPhoto = async (studentId, file, options = {}) => {
  try {
    const { quality = 0.7, maxWidth = 800, maxHeight = 800 } = options;
    
    // Compress image and convert to WebP
    const compressedFile = await compressImage(file, quality, maxWidth, maxHeight);
    
    // Generate unique filename
    const fileName = `${studentId}_${uuidv4()}.webp`;
    const filePath = `photos/${fileName}`;
    
    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from('student_photos')
      .upload(filePath, compressedFile, {
        contentType: 'image/webp',
        cacheControl: '3600',
        upsert: true,
      });
    
    if (error) throw new Error(`Upload failed: ${error.message}`);
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('student_photos')
      .getPublicUrl(filePath);
    
    // Record the photo update in the database
    await supabase
      .from('photo_updates')
      .insert({
        student_id: studentId,
        update_type: 'upload',
        photo_path: filePath,
      });
    
    return {
      publicUrl,
      filePath,
      size: compressedFile.size,
    };
  } catch (error) {
    console.error('Error uploading student photo:', error);
    throw error;
  }
};

/**
 * Calculates the quality score of a face image
 * @param {HTMLImageElement|HTMLCanvasElement} imageElement - Image element containing a face
 * @param {Object} faceDetection - Face detection result from face-api.js
 * @returns {number} - Quality score (0-1)
 */
export const calculateImageQuality = (imageElement, faceDetection) => {
  if (!faceDetection) return 0;
  
  const { width, height } = imageElement;
  const { box } = faceDetection.detection;
  
  // Calculate face size relative to image
  const faceArea = box.width * box.height;
  const imageArea = width * height;
  const faceRatio = faceArea / imageArea;
  
  // Calculate face position (center is best)
  const faceCenterX = box.x + box.width / 2;
  const faceCenterY = box.y + box.height / 2;
  const imageCenterX = width / 2;
  const imageCenterY = height / 2;
  
  // Calculate distance from center (normalized)
  const maxDistance = Math.sqrt(Math.pow(width / 2, 2) + Math.pow(height / 2, 2));
  const distance = Math.sqrt(Math.pow(faceCenterX - imageCenterX, 2) + Math.pow(faceCenterY - imageCenterY, 2));
  const normalizedDistance = 1 - (distance / maxDistance);
  
  // Calculate aspect ratio score (closer to 1:1 is better)
  const aspectRatio = box.width / box.height;
  const aspectRatioScore = 1 - Math.min(Math.abs(aspectRatio - 1), 0.5) * 2;
  
  // Calculate final quality score (weighted average)
  const sizeWeight = 0.5;
  const positionWeight = 0.3;
  const aspectRatioWeight = 0.2;
  
  // Size score: optimal is 15-25% of image area
  let sizeScore;
  if (faceRatio < 0.05) {
    // Face too small
    sizeScore = faceRatio * 20;
  } else if (faceRatio > 0.6) {
    // Face too large
    sizeScore = Math.max(0, 1 - ((faceRatio - 0.6) * 2.5));
  } else {
    // Face size is good
    sizeScore = 1;
  }
  
  return (
    sizeScore * sizeWeight +
    normalizedDistance * positionWeight +
    aspectRatioScore * aspectRatioWeight
  );
};

/**
 * Extracts metadata from an image
 * @param {File|Blob} file - Image file
 * @returns {Promise<Object>} - Image metadata
 */
export const extractImageMetadata = async (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
          aspectRatio: img.width / img.height,
          size: file.size,
          type: file.type,
        });
      };
      img.src = event.target.result;
    };
    reader.readAsDataURL(file);
  });
};

/**
 * Creates a thumbnail from an image
 * @param {File|Blob} file - Image file
 * @param {number} maxWidth - Maximum width of the thumbnail
 * @param {number} maxHeight - Maximum height of the thumbnail
 * @returns {Promise<Blob>} - Thumbnail as WebP blob
 */
export const createThumbnail = async (file, maxWidth = 100, maxHeight = 100) => {
  return compressImage(file, 0.7, maxWidth, maxHeight);
};

/**
 * Detects faces in an image using face-api.js
 * @param {HTMLImageElement|HTMLVideoElement|HTMLCanvasElement} imageElement - Image element
 * @returns {Promise<Object[]>} - Array of detected faces
 */
export const detectFaces = async (imageElement) => {
  try {
    // Ensure face-api.js models are loaded
    await import('face-api.js').then(async (faceapi) => {
      const modelPath = '/models';
      
      // Check if models are loaded
      if (!faceapi.nets.ssdMobilenetv1.isLoaded) {
        await Promise.all([
          faceapi.nets.ssdMobilenetv1.loadFromUri(modelPath),
          faceapi.nets.faceLandmark68Net.loadFromUri(modelPath),
          faceapi.nets.faceRecognitionNet.loadFromUri(modelPath),
        ]);
      }
    });
    
    // Import dynamically to ensure it's loaded
    const faceapi = await import('face-api.js');
    
    // Detect faces
    const detections = await faceapi.detectAllFaces(imageElement)
      .withFaceLandmarks()
      .withFaceDescriptors();
    
    return detections;
  } catch (error) {
    console.error('Error detecting faces:', error);
    return [];
  }
};

/**
 * Converts a data URL to a Blob
 * @param {string} dataUrl - Data URL
 * @returns {Blob} - Blob object
 */
export const dataURLtoBlob = (dataUrl) => {
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new Blob([u8arr], { type: mime });
};

/**
 * Gets device information for logging
 * @returns {Object} - Device information
 */
export const getDeviceInfo = () => {
  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    devicePixelRatio: window.devicePixelRatio,
    language: navigator.language,
    timestamp: new Date().toISOString(),
  };
};
