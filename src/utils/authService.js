/**
 * Secure Authentication Service
 * Handles <NAME_EMAIL> with enhanced security
 */

import { supabase } from './supabaseClient';
import { verifyPassword, generateSessionToken, generateCSRFToken, getDeviceFingerprint } from './passwordUtils';

// Configuration
const AUTHORIZED_EMAIL = '<EMAIL>';
const MAX_LOGIN_ATTEMPTS = 5;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes in milliseconds
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Authenticate user with email and password
 * @param {string} email - User email
 * @param {string} password - User password
 * @param {string} csrfToken - CSRF token for security
 * @returns {Promise<Object>} - Authentication result
 */
export const authenticateUser = async (email, password, csrfToken) => {
  try {
    // Validate input
    if (!email || !password || !csrfToken) {
      throw new Error('Email, password, and CSRF token are required');
    }

    // Check if email is authorized
    if (email !== AUTHORIZED_EMAIL) {
      // Log unauthorized attempt
      await logLoginAttempt(email, false, 'Unauthorized email');
      throw new Error('Unauthorized email address');
    }

    // Check if we're using mock Supabase
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;

    if (USE_MOCK) {
      // Mock authentication for development
      // For demo purposes, accept any password for the authorized email
      if (email === AUTHORIZED_EMAIL && password.length >= 8) {
        const mockUser = {
          id: '1',
          email: AUTHORIZED_EMAIL,
          last_login: new Date().toISOString()
        };

        // Generate session token
        const sessionToken = generateSessionToken();
        const deviceInfo = getDeviceFingerprint();

        // Store session in localStorage
        const sessionData = {
          token: sessionToken,
          email: email,
          expiresAt: Date.now() + SESSION_DURATION,
          deviceInfo: deviceInfo
        };

        localStorage.setItem('examino_session', JSON.stringify(sessionData));

        // Log successful login
        await logLoginAttempt(email, true, null, deviceInfo);

        return {
          success: true,
          user: mockUser,
          sessionToken: sessionToken
        };
      } else {
        await logLoginAttempt(email, false, 'Invalid credentials');
        throw new Error('Invalid credentials');
      }
    }

    // Check if account is locked
    const isLocked = await checkAccountLock(email);
    if (isLocked) {
      throw new Error('Account is temporarily locked due to too many failed attempts');
    }

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('auth_users')
      .select('*')
      .eq('email', email)
      .single();

    if (userError || !user) {
      await handleFailedLogin(email);
      throw new Error('Invalid credentials');
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.encrypted_password, user.password_salt);

    if (!isValidPassword) {
      await handleFailedLogin(email);
      throw new Error('Invalid credentials');
    }

    // Generate session token
    const sessionToken = generateSessionToken();
    const deviceInfo = getDeviceFingerprint();

    // Handle successful login
    const { data: rpcSessionData, error: sessionError } = await supabase.rpc(
      'handle_successful_login',
      {
        user_email: email,
        client_ip: null, // Will be set by Edge Function
        client_user_agent: navigator.userAgent
      }
    );

    if (sessionError) {
      console.error('Error creating session:', sessionError);
      throw new Error('Failed to create session');
    }

    // Store session in localStorage (encrypted)
    const sessionData = {
      token: sessionToken,
      email: email,
      expiresAt: Date.now() + SESSION_DURATION,
      deviceInfo: deviceInfo
    };

    localStorage.setItem('examino_session', JSON.stringify(sessionData));

    // Log successful login
    await logLoginAttempt(email, true, null, deviceInfo);

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email,
        lastLogin: user.last_login
      },
      sessionToken: sessionToken
    };

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Validate current session
 * @returns {Promise<Object>} - Session validation result
 */
export const validateSession = async () => {
  try {
    const sessionData = localStorage.getItem('examino_session');

    if (!sessionData) {
      return { valid: false, reason: 'No session found' };
    }

    const session = JSON.parse(sessionData);

    // Check if session is expired
    if (Date.now() > session.expiresAt) {
      localStorage.removeItem('examino_session');
      return { valid: false, reason: 'Session expired' };
    }

    // Check if we're using mock Supabase
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;

    if (USE_MOCK) {
      // Mock session validation for development
      return {
        valid: true,
        user: {
          id: '1',
          email: session.email,
          lastLogin: new Date().toISOString()
        }
      };
    }

    // Validate session with database
    const { data: validationResult, error } = await supabase.rpc(
      'validate_session',
      { token: session.token }
    );

    if (error || !validationResult?.valid) {
      localStorage.removeItem('examino_session');
      return { valid: false, reason: 'Invalid session' };
    }

    return {
      valid: true,
      user: {
        id: validationResult.user_id,
        email: validationResult.email,
        lastLogin: validationResult.last_login
      }
    };

  } catch (error) {
    console.error('Session validation error:', error);
    localStorage.removeItem('examino_session');
    return { valid: false, reason: 'Session validation failed' };
  }
};

/**
 * Logout user and invalidate session
 * @returns {Promise<boolean>} - Logout success
 */
export const logoutUser = async () => {
  try {
    const sessionData = localStorage.getItem('examino_session');

    if (sessionData) {
      const session = JSON.parse(sessionData);

      // Invalidate session in database
      await supabase
        .from('admin_sessions')
        .update({ is_active: false })
        .eq('session_token', session.token);
    }

    // Remove session from localStorage
    localStorage.removeItem('examino_session');

    return true;
  } catch (error) {
    console.error('Logout error:', error);
    // Still remove local session even if database update fails
    localStorage.removeItem('examino_session');
    return false;
  }
};

/**
 * Check if account is locked
 * @param {string} email - User email
 * @returns {Promise<boolean>} - True if account is locked
 */
const checkAccountLock = async (email) => {
  try {
    const { data: result, error } = await supabase.rpc(
      'is_account_locked',
      { user_email: email }
    );

    if (error) {
      console.error('Error checking account lock:', error);
      return false;
    }

    return result;
  } catch (error) {
    console.error('Error checking account lock:', error);
    return false;
  }
};

/**
 * Handle failed login attempt
 * @param {string} email - User email
 * @returns {Promise<void>}
 */
const handleFailedLogin = async (email) => {
  try {
    await supabase.rpc('handle_failed_login', {
      user_email: email,
      client_ip: null, // Will be set by Edge Function
      client_user_agent: navigator.userAgent
    });
  } catch (error) {
    console.error('Error handling failed login:', error);
  }
};

/**
 * Log login attempt
 * @param {string} email - User email
 * @param {boolean} success - Whether login was successful
 * @param {string} failureReason - Reason for failure (if any)
 * @param {Object} deviceInfo - Device information
 * @returns {Promise<void>}
 */
const logLoginAttempt = async (email, success, failureReason = null, deviceInfo = null) => {
  try {
    // Check if we're using mock Supabase
    const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;

    if (USE_MOCK) {
      // Mock logging for development
      console.log('Login attempt logged:', {
        email,
        success,
        failure_reason: failureReason,
        user_agent: navigator.userAgent,
        device_info: deviceInfo || getDeviceFingerprint(),
        timestamp: new Date().toISOString()
      });
      return;
    }

    await supabase
      .from('login_attempts')
      .insert({
        email: email,
        success: success,
        failure_reason: failureReason,
        user_agent: navigator.userAgent,
        device_info: deviceInfo || getDeviceFingerprint()
      });
  } catch (error) {
    console.error('Error logging login attempt:', error);
  }
};

/**
 * Get login analytics
 * @param {number} daysBack - Number of days to look back
 * @returns {Promise<Object>} - Login analytics data
 */
export const getLoginAnalytics = async (daysBack = 30) => {
  try {
    const { data: analytics, error } = await supabase.rpc(
      'get_login_analytics',
      { days_back: daysBack }
    );

    if (error) {
      console.error('Error fetching login analytics:', error);
      return null;
    }

    return analytics;
  } catch (error) {
    console.error('Error fetching login analytics:', error);
    return null;
  }
};

/**
 * Generate CSRF token for forms
 * @returns {string} - CSRF token
 */
export const getCSRFToken = () => {
  let token = sessionStorage.getItem('csrf_token');

  if (!token) {
    token = generateCSRFToken();
    sessionStorage.setItem('csrf_token', token);
  }

  return token;
};

/**
 * Clean up expired sessions
 * @returns {Promise<number>} - Number of sessions cleaned up
 */
export const cleanupExpiredSessions = async () => {
  try {
    const { data: result, error } = await supabase.rpc('cleanup_expired_sessions');

    if (error) {
      console.error('Error cleaning up sessions:', error);
      return 0;
    }

    return result;
  } catch (error) {
    console.error('Error cleaning up sessions:', error);
    return 0;
  }
};
