/**
 * Face-API.js Model Manager
 * Handles model loading with fallback mechanisms
 */

import * as faceapi from 'face-api.js';

// Model configuration
const MODEL_CONFIG = {
  BASE_URL: '/models',
  CDN_FALLBACK: 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights',
  REQUIRED_MODELS: [
    'tiny_face_detector_model-weights_manifest.json',
    'face_landmark_68_model-weights_manifest.json',
    'face_recognition_model-weights_manifest.json'
  ],
  TIMEOUT: 10000 // 10 seconds
};

let modelsLoaded = false;
let loadingPromise = null;

/**
 * Check if models are available at the given URL
 */
const checkModelAvailability = async (baseUrl) => {
  try {
    const testUrl = `${baseUrl}/tiny_face_detector_model-weights_manifest.json`;
    const response = await fetch(testUrl, { 
      method: 'HEAD',
      timeout: 5000 
    });
    return response.ok;
  } catch (error) {
    console.warn(`Models not available at ${baseUrl}:`, error.message);
    return false;
  }
};

/**
 * Load face-api.js models with fallback mechanisms
 */
export const loadFaceApiModels = async () => {
  if (modelsLoaded) {
    return { success: true, source: 'cached' };
  }
  
  if (loadingPromise) {
    return loadingPromise;
  }
  
  loadingPromise = (async () => {
    try {
      console.log('🔄 Loading face-api.js models...');
      
      let modelUrl = MODEL_CONFIG.BASE_URL;
      let source = 'local';
      
      // Check if local models are available
      const localAvailable = await checkModelAvailability(MODEL_CONFIG.BASE_URL);
      
      if (!localAvailable) {
        console.warn('⚠️ Local models not found, using CDN fallback');
        modelUrl = MODEL_CONFIG.CDN_FALLBACK;
        source = 'cdn';
      }
      
      // Load models with timeout
      const loadPromise = Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri(modelUrl),
        faceapi.nets.faceLandmark68Net.loadFromUri(modelUrl),
        faceapi.nets.faceRecognitionNet.loadFromUri(modelUrl)
      ]);
      
      // Add timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Model loading timeout')), MODEL_CONFIG.TIMEOUT);
      });
      
      await Promise.race([loadPromise, timeoutPromise]);
      
      modelsLoaded = true;
      console.log(`✅ Face-api.js models loaded successfully from ${source}`);
      
      return { success: true, source };
      
    } catch (error) {
      console.error('❌ Failed to load face-api.js models:', error);
      modelsLoaded = false;
      
      return { 
        success: false, 
        error: error.message,
        fallbackAvailable: true
      };
    }
  })();
  
  return loadingPromise;
};

/**
 * Mock face detection for when models fail to load
 */
export const mockFaceDetection = async (imageElement) => {
  console.log('🎭 Using mock face detection (models not available)');
  
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Simple face detection simulation based on image analysis
  const canvas = document.createElement('canvas');
  canvas.width = imageElement.width;
  canvas.height = imageElement.height;
  const ctx = canvas.getContext('2d');
  ctx.drawImage(imageElement, 0, 0);
  
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const pixels = imageData.data;
  
  // Simple heuristic: look for face-like color patterns
  let skinColorPixels = 0;
  let totalPixels = pixels.length / 4;
  
  for (let i = 0; i < pixels.length; i += 4) {
    const r = pixels[i];
    const g = pixels[i + 1];
    const b = pixels[i + 2];
    
    // Simple skin color detection
    if (r > 95 && g > 40 && b > 20 && 
        Math.max(r, g, b) - Math.min(r, g, b) > 15 &&
        Math.abs(r - g) > 15 && r > g && r > b) {
      skinColorPixels++;
    }
  }
  
  const skinRatio = skinColorPixels / totalPixels;
  const hasFace = skinRatio > 0.1 && skinRatio < 0.6; // Reasonable face ratio
  
  if (!hasFace) {
    return {
      success: false,
      error: 'No face detected in image',
      method: 'mock'
    };
  }
  
  // Generate mock face descriptor (128-dimensional vector)
  const descriptor = new Array(128).fill(0).map(() => Math.random() - 0.5);
  
  // Mock detection score based on skin ratio
  const detectionScore = Math.min(0.95, skinRatio * 2 + 0.5);
  
  return {
    success: true,
    descriptor,
    detectionScore,
    faceBox: {
      x: imageElement.width * 0.3,
      y: imageElement.height * 0.2,
      width: imageElement.width * 0.4,
      height: imageElement.height * 0.5
    },
    landmarks: [], // Mock landmarks
    method: 'mock'
  };
};

/**
 * Detect faces with automatic fallback to mock detection
 */
export const detectFacesWithFallback = async (imageElement) => {
  try {
    // Try to load models first
    const modelResult = await loadFaceApiModels();
    
    if (modelResult.success) {
      // Use real face-api.js detection
      console.log('🔍 Using face-api.js detection');
      
      const detections = await faceapi
        .detectAllFaces(imageElement, new faceapi.TinyFaceDetectorOptions())
        .withFaceLandmarks()
        .withFaceDescriptors();
      
      if (detections.length === 0) {
        return {
          success: false,
          error: 'No face detected in image',
          method: 'faceapi'
        };
      }
      
      if (detections.length > 1) {
        return {
          success: false,
          error: 'Multiple faces detected. Please ensure only one person is in the frame.',
          method: 'faceapi'
        };
      }
      
      const detection = detections[0];
      
      return {
        success: true,
        descriptor: Array.from(detection.descriptor),
        detectionScore: detection.detection.score,
        faceBox: detection.detection.box,
        landmarks: detection.landmarks.positions,
        method: 'faceapi'
      };
      
    } else {
      // Fallback to mock detection
      console.warn('⚠️ Face-api.js models unavailable, using mock detection');
      return await mockFaceDetection(imageElement);
    }
    
  } catch (error) {
    console.error('Face detection error:', error);
    
    // Last resort: try mock detection
    try {
      console.log('🔄 Attempting mock detection as last resort');
      return await mockFaceDetection(imageElement);
    } catch (mockError) {
      return {
        success: false,
        error: `Face detection failed: ${error.message}`,
        method: 'failed'
      };
    }
  }
};

/**
 * Get model loading status
 */
export const getModelStatus = () => {
  return {
    loaded: modelsLoaded,
    loading: loadingPromise !== null && !modelsLoaded,
    available: modelsLoaded || window.faceapi !== undefined
  };
};

/**
 * Preload models (call this on app startup)
 */
export const preloadModels = async () => {
  try {
    console.log('🚀 Preloading face-api.js models...');
    const result = await loadFaceApiModels();
    
    if (result.success) {
      console.log('✅ Models preloaded successfully');
    } else {
      console.warn('⚠️ Model preloading failed, will use fallback detection');
    }
    
    return result;
  } catch (error) {
    console.warn('⚠️ Model preloading error:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Download and setup local models (for development)
 */
export const setupLocalModels = async () => {
  console.log('📥 Setting up local face-api.js models...');
  
  const modelFiles = [
    'tiny_face_detector_model-weights_manifest.json',
    'tiny_face_detector_model-shard1',
    'face_landmark_68_model-weights_manifest.json',
    'face_landmark_68_model-shard1',
    'face_recognition_model-weights_manifest.json',
    'face_recognition_model-shard1',
    'face_recognition_model-shard2'
  ];
  
  const downloadPromises = modelFiles.map(async (filename) => {
    try {
      const response = await fetch(`${MODEL_CONFIG.CDN_FALLBACK}/${filename}`);
      if (!response.ok) throw new Error(`Failed to fetch ${filename}`);
      
      const blob = await response.blob();
      console.log(`✅ Downloaded ${filename} (${(blob.size / 1024).toFixed(1)}KB)`);
      
      return { filename, success: true, size: blob.size };
    } catch (error) {
      console.error(`❌ Failed to download ${filename}:`, error);
      return { filename, success: false, error: error.message };
    }
  });
  
  const results = await Promise.all(downloadPromises);
  const successful = results.filter(r => r.success);
  
  console.log(`📊 Model download complete: ${successful.length}/${results.length} files`);
  
  if (successful.length === results.length) {
    console.log('🎉 All models downloaded successfully!');
    console.log('💡 Models are now available from CDN fallback');
  } else {
    console.warn('⚠️ Some models failed to download, but CDN fallback is available');
  }
  
  return {
    success: successful.length > 0,
    downloaded: successful.length,
    total: results.length,
    results
  };
};

// Auto-preload models when this module is imported
if (typeof window !== 'undefined') {
  // Preload models after a short delay to not block initial page load
  setTimeout(() => {
    preloadModels();
  }, 2000);
}
