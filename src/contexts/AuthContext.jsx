import { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../utils/supabaseClient';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true);

  // Test credentials
  const TEST_EMAIL = '<EMAIL>';
  const TEST_PASSWORD = 'password123';
  const TEST_USER = {
    id: 'test-user-id',
    email: TEST_EMAIL,
  };
  const TEST_PROFILE = {
    id: 'test-user-id',
    first_name: 'Test',
    last_name: 'User',
    student_id: 'S12345',
    course: 'Computer Science',
    semester: '3',
    reference_image: 'https://via.placeholder.com/150',
    reference_image_url: 'https://via.placeholder.com/150',
    face_descriptor: 'mock-face-descriptor-data',
    referenceImage: 'https://via.placeholder.com/150',
    role_id: 1, // Default to admin for test user
    institute_id: null
  };

  // Role definitions
  const ROLES = {
    ADMIN: 'admin',
    INSTITUTE: 'institute',
    STUDENT: 'student'
  };

  async function login(email, password) {
    try {
      // Check if we're using the mock Supabase client
      const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;

      // Block demo accounts in production
      if (process.env.NODE_ENV === 'production') {
        const demoEmails = [TEST_EMAIL, '<EMAIL>', '<EMAIL>'];
        if (demoEmails.includes(email)) {
          throw new Error('Unauthorized access. Please use your institutional credentials.');
        }
      }

      // Handle test credentials in development/mock mode
      if (USE_MOCK && email === TEST_EMAIL && password === TEST_PASSWORD) {
        // Special case for test credentials in mock mode
        console.log('Using test credentials in mock mode');
        setCurrentUser(TEST_USER);
        setUserProfile(TEST_PROFILE);
        setUserRole(ROLES.ADMIN); // Default to admin for test user
        return { user: { ...TEST_USER, role: ROLES.ADMIN } };
      }

      // Handle institute test account
      if (USE_MOCK && email === '<EMAIL>' && password === TEST_PASSWORD) {
        const instituteUser = { ...TEST_USER, email: '<EMAIL>' };
        const instituteProfile = { ...TEST_PROFILE, role_id: 2 };
        setCurrentUser(instituteUser);
        setUserProfile(instituteProfile);
        setUserRole(ROLES.INSTITUTE);
        return { user: { ...instituteUser, role: ROLES.INSTITUTE } };
      }

      // Handle student test account
      if (USE_MOCK && email === '<EMAIL>' && password === TEST_PASSWORD) {
        const studentUser = { ...TEST_USER, email: '<EMAIL>' };
        const studentProfile = { ...TEST_PROFILE, role_id: 3 };
        setCurrentUser(studentUser);
        setUserProfile(studentProfile);
        setUserRole(ROLES.STUDENT);
        return { user: { ...studentUser, role: ROLES.STUDENT } };
      }

      // Real login with Supabase (or mock Supabase)
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;
      if (!data?.user) throw new Error('No user returned from authentication');

      // Set current user
      setCurrentUser(data.user);

      // Fetch user profile and role
      await fetchUserProfile(data.user.id);

      return { ...data, user: { ...data.user, role: userRole } };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async function signup(email, password, firstName, lastName, studentId, course, semester) {
    try {
      // Check if we're in development mode
      const isDevelopment = import.meta.env.DEV || !import.meta.env.VITE_SUPABASE_URL;

      if (isDevelopment) {
        // Mock signup for development
        return new Promise((resolve) => {
          setTimeout(() => {
            // In a real app, this would create a user in Supabase
            // For now, just simulate success
            resolve({ user: { email } });
          }, 500); // Simulate network delay
        });
      } else {
        // Real signup with Supabase
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              first_name: firstName,
              last_name: lastName
            }
          }
        });

        if (error) throw error;

        // Create student profile with default student role
        if (data.user) {
          const studentRoleId = await getRoleId(ROLES.STUDENT);

          const { error: profileError } = await supabase
            .from('users')
            .insert({
              id: data.user.id,
              first_name: firstName,
              last_name: lastName,
              student_id: studentId,
              course: course,
              semester: semester,
              role_id: studentRoleId,
              is_verified: true // Students are auto-verified
            });

          if (profileError) throw profileError;
        }

        return data;
      }
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  }

  // Function to sign up with invite code (for admin/institute)
  async function signupWithInvite(email, password, firstName, lastName, inviteCode) {
    try {
      // Check if we're in development mode
      const isDevelopment = import.meta.env.DEV || !import.meta.env.VITE_SUPABASE_URL;

      if (isDevelopment) {
        // Mock signup for development
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({ user: { email } });
          }, 500); // Simulate network delay
        });
      } else {
        // First verify the invite code
        const { data: inviteData, error: inviteError } = await supabase
          .rpc('verify_invite_code', { code: inviteCode });

        if (inviteError) throw inviteError;
        if (!inviteData || !inviteData.length || !inviteData[0].is_valid) {
          throw new Error('Invalid or expired invite code');
        }

        // Check if the email matches the invited email
        if (inviteData[0].email !== email) {
          throw new Error('This invite code is not valid for this email address');
        }

        // Create the user account
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              first_name: firstName,
              last_name: lastName
            }
          }
        });

        if (error) throw error;

        // Use the invite code to set the correct role
        if (data.user) {
          const { error: useCodeError } = await supabase
            .rpc('use_invite_code', {
              code: inviteCode,
              user_id: data.user.id
            });

          if (useCodeError) throw useCodeError;
        }

        return data;
      }
    } catch (error) {
      console.error('Signup with invite error:', error);
      throw error;
    }
  }

  async function logout() {
    try {
      // Check if we're in development mode
      const isDevelopment = import.meta.env.DEV || !import.meta.env.VITE_SUPABASE_URL;

      if (isDevelopment) {
        // Mock logout for development
        return new Promise((resolve) => {
          setTimeout(() => {
            setCurrentUser(null);
            setUserProfile(null);
            setUserRole(null);
            resolve();
          }, 500); // Simulate network delay
        });
      } else {
        // Real logout with Supabase
        const { error } = await supabase.auth.signOut();
        if (error) throw error;

        setCurrentUser(null);
        setUserProfile(null);
        setUserRole(null);
      }
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  // Helper function to get role ID from role name
  async function getRoleId(roleName) {
    try {
      // Check if we're in development mode
      const isDevelopment = import.meta.env.DEV || !import.meta.env.VITE_SUPABASE_URL;

      if (isDevelopment) {
        // Mock role IDs
        const roleIds = {
          [ROLES.ADMIN]: 1,
          [ROLES.INSTITUTE]: 2,
          [ROLES.STUDENT]: 3
        };
        return roleIds[roleName] || 3; // Default to student
      } else {
        // Fetch role ID from database
        const { data, error } = await supabase
          .from('user_roles')
          .select('role_id')
          .eq('role_name', roleName)
          .single();

        if (error) throw error;
        return data.role_id;
      }
    } catch (error) {
      console.error('Error getting role ID:', error);
      throw error;
    }
  }

  // Helper function to fetch user profile and role
  async function fetchUserProfile(userId) {
    try {
      // Check if we're in development mode
      const isDevelopment = import.meta.env.DEV || !import.meta.env.VITE_SUPABASE_URL;

      if (isDevelopment) {
        // Mock profile for development
        setUserProfile(TEST_PROFILE);
        setUserRole(ROLES.ADMIN); // Default to admin for test user
      } else {
        // Fetch user profile from database
        const { data, error } = await supabase
          .from('users')
          .select(`
            *,
            user_roles(role_name)
          `)
          .eq('id', userId)
          .single();

        if (error) throw error;

        setUserProfile(data);
        setUserRole(data.user_roles.role_name);
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      // Don't throw here to prevent blocking the auth flow
    }
  }

  useEffect(() => {
    // Initialize authentication state
    const initAuth = async () => {
      try {
        // Check if we're in development mode
        const isDevelopment = import.meta.env.DEV || !import.meta.env.VITE_SUPABASE_URL;

        if (isDevelopment) {
          // For development, we'll auto-login with test credentials if AUTO_LOGIN is true
          const AUTO_LOGIN = false; // Set to true to auto-login in development

          if (AUTO_LOGIN) {
            setCurrentUser(TEST_USER);
            setUserProfile(TEST_PROFILE);
            setUserRole(ROLES.ADMIN); // Default to admin for test user
          }

          setLoading(false);
        } else {
          // Check for existing session
          const { data } = await supabase.auth.getSession();

          if (data.session) {
            setCurrentUser(data.session.user);
            await fetchUserProfile(data.session.user.id);
          }

          // Set up auth state change listener
          const { data: authListener } = supabase.auth.onAuthStateChange(
            async (event, session) => {
              if (event === 'SIGNED_IN' && session) {
                setCurrentUser(session.user);
                await fetchUserProfile(session.user.id);
              } else if (event === 'SIGNED_OUT') {
                setCurrentUser(null);
                setUserProfile(null);
                setUserRole(null);
              }
            }
          );

          setLoading(false);

          // Cleanup function
          return () => {
            authListener.subscription.unsubscribe();
          };
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const value = {
    currentUser,
    userProfile,
    userRole,
    login,
    signup,
    signupWithInvite,
    logout,
    loading,
    ROLES, // Export role constants
    isAdmin: userRole === ROLES.ADMIN,
    isInstitute: userRole === ROLES.INSTITUTE,
    isStudent: userRole === ROLES.STUDENT
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}
