import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import './minimal.css'
import './styles/welcome.css'
import './styles/landing-animations.css'
import App from './App.jsx'

// Import test utilities for development
if (import.meta.env.DEV) {
  import('./utils/testAuth.js');
  import('./utils/setupPassword.js');
}

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
