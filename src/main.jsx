import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import './minimal.css'
import './styles/welcome.css'
import './styles/landing-animations.css'
import App from './App.jsx'

// Load test utilities for development
if (import.meta.env.DEV) {
  // Load test utilities asynchronously after the app starts
  setTimeout(() => {
    Promise.all([
      import('./utils/testAuth.js'),
      import('./utils/setupPassword.js'),
      import('./utils/browserTest.js'),
      import('./utils/testAttendanceSystem.js')
    ]).catch(err => {
      console.warn('Could not load development utilities:', err);
    });
  }, 1000);
}

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
