import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import Webcam from 'react-webcam';
import { CheckCircleIcon, CameraIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';

export default function AttendancePanel() {
  const { currentUser, userProfile } = useAuth();
  const [showWebcam, setShowWebcam] = useState(false);
  const webcamRef = useRef(null);
  const [attendanceRecords, setAttendanceRecords] = useState([
    { date: 'Today', time: '9:15 AM', confidence: 98 },
    { date: 'Yesterday', time: '9:02 AM', confidence: 96 },
    { date: 'May 13', time: '9:10 AM', confidence: 97 },
    { date: 'May 12', time: '9:22 AM', confidence: 95 },
    { date: 'May 11', time: '9:05 AM', confidence: 99 },
  ]);
  
  const handleAttendanceClick = () => {
    // Check if user has face data registered
    if (!userProfile?.reference_image_url && !userProfile?.face_descriptor && !userProfile?.referenceImage) {
      alert('Contact admin to register your face for attendance.');
      return;
    }

    // Toggle webcam
    setShowWebcam(!showWebcam);
  };
  
  const videoConstraints = {
    width: 1280,
    height: 720,
    facingMode: "user"
  };
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };
  
  return (
    <motion.div
      className="bg-white rounded-xl shadow-md p-6"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Attendance Tracker</h2>
      
      <div className="flex flex-col items-center mb-6">
        {showWebcam ? (
          <div className="relative mb-4">
            <Webcam
              audio={false}
              ref={webcamRef}
              screenshotFormat="image/jpeg"
              videoConstraints={videoConstraints}
              className="rounded-full w-32 h-32 object-cover border-4 border-blue-500"
              style={{ objectFit: 'cover' }}
            />
          </div>
        ) : (
          <div className="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center mb-4 overflow-hidden">
            {userProfile?.reference_image_url ? (
              <img 
                src={userProfile.reference_image_url} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <CameraIcon className="w-12 h-12 text-gray-400" />
            )}
          </div>
        )}
        
        <motion.button
          onClick={handleAttendanceClick}
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors animate-pulse"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          disabled={!userProfile?.reference_image_url && !userProfile?.face_descriptor && !userProfile?.referenceImage}
        >
          <CameraIcon className="w-5 h-5" /> 
          {showWebcam ? 'Capture Photo' : 'Mark Attendance'}
        </motion.button>
      </div>
      
      <h3 className="text-md font-medium text-gray-700 mb-2">Recent Attendance</h3>
      <div className="space-y-3">
        {attendanceRecords.map((record, index) => (
          <motion.div 
            key={index}
            className="flex gap-2 items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <CheckCircleIcon className="text-green-500 w-5 h-5 flex-shrink-0" /> 
            <div className="text-sm">
              <span className="font-medium">{record.date}</span> at {record.time} 
              <span className="text-green-600 ml-1">({record.confidence}% match)</span>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}
