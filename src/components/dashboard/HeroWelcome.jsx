import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { SunIcon, MoonIcon, CloudIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';

export default function HeroWelcome({ attendanceRate = 82, examsCount = 3, pendingActions = 2 }) {
  const { userProfile } = useAuth();
  const [greeting, setGreeting] = useState('');
  const [currentTime, setCurrentTime] = useState(new Date());
  
  // Update greeting based on time of day
  useEffect(() => {
    const updateGreeting = () => {
      const hour = new Date().getHours();
      if (hour < 12) {
        setGreeting('Good morning');
      } else if (hour < 18) {
        setGreeting('Good afternoon');
      } else {
        setGreeting('Good evening');
      }
    };
    
    updateGreeting();
    const interval = setInterval(() => {
      setCurrentTime(new Date());
      updateGreeting();
    }, 60000); // Update every minute
    
    return () => clearInterval(interval);
  }, []);
  
  // Get weather icon based on time of day (mock)
  const getWeatherIcon = () => {
    const hour = currentTime.getHours();
    if (hour >= 6 && hour < 18) {
      return <SunIcon className="w-5 h-5 text-amber-400" />;
    } else if (hour >= 18 && hour < 20) {
      return <CloudIcon className="w-5 h-5 text-blue-300" />;
    } else {
      return <MoonIcon className="w-5 h-5 text-indigo-300" />;
    }
  };
  
  // Format date
  const formattedDate = currentTime.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  });
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };
  
  return (
    <motion.div
      className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-6 shadow-xl"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <motion.div variants={itemVariants}>
          <h1 className="text-2xl md:text-3xl font-bold text-white">
            {greeting}, <span className="text-yellow-300">{userProfile?.first_name || 'Test'}</span>!
          </h1>
          <p className="text-blue-100 mt-1">Here's your dashboard overview</p>
        </motion.div>
        
        <motion.div 
          className="bg-white/10 backdrop-blur-md rounded-xl px-4 py-2 mt-4 md:mt-0"
          variants={itemVariants}
        >
          <span className="flex items-center gap-2 text-white">
            {getWeatherIcon()} 
            {formattedDate} • Sunny • 28°C
          </span>
        </motion.div>
      </div>
      
      <motion.div 
        className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-6"
        variants={containerVariants}
      >
        {/* Attendance Stat */}
        <motion.div 
          className="bg-white/20 backdrop-blur-sm rounded-lg p-4"
          variants={itemVariants}
          whileHover={{ scale: 1.02 }}
          transition={{ type: 'spring', stiffness: 400 }}
        >
          <h3 className="text-white text-sm font-medium mb-2">Today's Attendance</h3>
          <div className="flex items-center">
            <div className="relative h-12 w-12 mr-3">
              <svg className="h-12 w-12" viewBox="0 0 36 36">
                <circle cx="18" cy="18" r="16" fill="none" stroke="rgba(255,255,255,0.3)" strokeWidth="2"></circle>
                <circle 
                  cx="18" cy="18" r="16" fill="none" stroke="white" strokeWidth="2" 
                  strokeDasharray={`${attendanceRate}, 100`} 
                  strokeLinecap="round" 
                  transform="rotate(-90 18 18)"
                ></circle>
              </svg>
              <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                <span className="text-white font-bold">{attendanceRate}%</span>
              </div>
            </div>
            <div className="text-white">
              <div className="text-sm opacity-80">Class Average</div>
              <div className="font-medium">78%</div>
            </div>
          </div>
        </motion.div>
        
        {/* Exams Stat */}
        <motion.div 
          className="bg-white/20 backdrop-blur-sm rounded-lg p-4"
          variants={itemVariants}
          whileHover={{ scale: 1.02 }}
          transition={{ type: 'spring', stiffness: 400 }}
        >
          <h3 className="text-white text-sm font-medium mb-2">Exams This Week</h3>
          <div className="flex items-center">
            <div className="bg-indigo-500 h-12 w-12 rounded-full flex items-center justify-center mr-3">
              <span className="text-white text-xl font-bold">{examsCount}</span>
            </div>
            <div className="text-white">
              <div className="text-sm opacity-80">Next Exam</div>
              <div className="font-medium">Tomorrow, 10 AM</div>
            </div>
          </div>
        </motion.div>
        
        {/* Pending Actions */}
        <motion.div 
          className="bg-white/20 backdrop-blur-sm rounded-lg p-4"
          variants={itemVariants}
          whileHover={{ scale: 1.02 }}
          transition={{ type: 'spring', stiffness: 400 }}
        >
          <h3 className="text-white text-sm font-medium mb-2">Pending Actions</h3>
          <div className="flex items-center">
            <div className="relative h-12 w-12 rounded-full flex items-center justify-center mr-3">
              <div className="bg-red-500 h-12 w-12 rounded-full flex items-center justify-center animate-pulse">
                <span className="text-white text-xl font-bold">{pendingActions}</span>
              </div>
            </div>
            <div className="text-white">
              <div className="text-sm opacity-80">Action Required</div>
              <div className="font-medium">Complete Profile</div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
