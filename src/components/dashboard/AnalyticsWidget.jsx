import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, PointElement, LineElement, Title } from 'chart.js';
import { Doughnut, Line } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  ArcElement, 
  Tooltip, 
  Legend, 
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  Title
);

export default function AnalyticsWidget() {
  // Attendance data for doughnut chart
  const attendanceData = {
    labels: ['Present', 'Absent', 'Excused'],
    datasets: [
      {
        data: [85, 10, 5],
        backgroundColor: [
          'rgba(52, 211, 153, 0.8)',
          'rgba(248, 113, 113, 0.8)',
          'rgba(251, 191, 36, 0.8)'
        ],
        borderColor: [
          'rgba(52, 211, 153, 1)',
          'rgba(248, 113, 113, 1)',
          'rgba(251, 191, 36, 1)'
        ],
        borderWidth: 1,
      },
    ],
  };
  
  // Performance data for line chart
  const performanceData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    datasets: [
      {
        label: 'Your Score',
        data: [65, 72, 68, 78, 85],
        borderColor: 'rgba(59, 130, 246, 1)',
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        tension: 0.3,
      },
      {
        label: 'Class Average',
        data: [60, 65, 63, 70, 72],
        borderColor: 'rgba(156, 163, 175, 1)',
        backgroundColor: 'rgba(156, 163, 175, 0.5)',
        tension: 0.3,
      },
    ],
  };
  
  const lineOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        min: 0,
        max: 100,
      },
    },
  };
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };
  
  return (
    <motion.div
      className="bg-white rounded-xl shadow-md p-6"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Performance Analytics</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-md font-medium text-gray-700 mb-3 text-center">Attendance Overview</h3>
          <div className="h-48 flex items-center justify-center">
            <Doughnut data={attendanceData} />
          </div>
        </div>
        
        <div>
          <h3 className="text-md font-medium text-gray-700 mb-3 text-center">Performance Trend</h3>
          <div className="h-48">
            <Line options={lineOptions} data={performanceData} />
          </div>
        </div>
      </div>
      
      <div className="mt-4 grid grid-cols-3 gap-4">
        <div className="bg-blue-50 p-3 rounded-lg text-center">
          <div className="text-sm text-blue-700">Avg. Score</div>
          <div className="text-xl font-bold text-blue-900">78%</div>
        </div>
        
        <div className="bg-green-50 p-3 rounded-lg text-center">
          <div className="text-sm text-green-700">Attendance</div>
          <div className="text-xl font-bold text-green-900">92%</div>
        </div>
        
        <div className="bg-purple-50 p-3 rounded-lg text-center">
          <div className="text-sm text-purple-700">Class Rank</div>
          <div className="text-xl font-bold text-purple-900">#3</div>
        </div>
      </div>
    </motion.div>
  );
}
