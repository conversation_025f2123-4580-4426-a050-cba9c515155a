import { motion } from 'framer-motion';
import { 
  CameraIcon, 
  AcademicCapIcon, 
  DocumentTextIcon, 
  UserIcon, 
  ChartBarIcon, 
  CalendarIcon 
} from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';

export default function QuickActions() {
  const actions = [
    {
      title: 'Mark Attendance',
      icon: <CameraIcon className="w-6 h-6" />,
      link: '/attendance',
      color: 'bg-gradient-to-br from-green-400 to-green-600'
    },
    {
      title: 'Take Exam',
      icon: <AcademicCapIcon className="w-6 h-6" />,
      link: '/exams',
      color: 'bg-gradient-to-br from-blue-400 to-blue-600'
    },
    {
      title: 'View Results',
      icon: <DocumentTextIcon className="w-6 h-6" />,
      link: '/results',
      color: 'bg-gradient-to-br from-purple-400 to-purple-600'
    },
    {
      title: 'My Profile',
      icon: <UserIcon className="w-6 h-6" />,
      link: '/profile',
      color: 'bg-gradient-to-br from-yellow-400 to-yellow-600'
    },
    {
      title: 'Analytics',
      icon: <ChartBarIcon className="w-6 h-6" />,
      link: '/dashboard',
      color: 'bg-gradient-to-br from-red-400 to-red-600'
    },
    {
      title: 'Schedule',
      icon: <CalendarIcon className="w-6 h-6" />,
      link: '/dashboard',
      color: 'bg-gradient-to-br from-indigo-400 to-indigo-600'
    }
  ];
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };
  
  return (
    <motion.div
      className="bg-white rounded-xl shadow-md p-6"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Quick Actions</h2>
      
      <motion.div 
        className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4"
        variants={containerVariants}
      >
        {actions.map((action, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            whileHover={{ 
              scale: 1.05,
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
            }}
            whileTap={{ scale: 0.95 }}
          >
            <Link 
              to={action.link}
              className="flex flex-col items-center justify-center p-4 rounded-lg text-white h-full"
              style={{ background: action.color }}
            >
              <div className="mb-2">
                {action.icon}
              </div>
              <span className="text-sm font-medium">{action.title}</span>
            </Link>
          </motion.div>
        ))}
      </motion.div>
    </motion.div>
  );
}
