import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ClockIcon, AcademicCapIcon } from '@heroicons/react/24/outline';
import Countdown from 'react-countdown';
import { Link } from 'react-router-dom';

export default function ExamsOverview({ activeExams = [] }) {
  const [mockExams, setMockExams] = useState([
    {
      id: 'exam-1',
      title: 'Midterm Examination',
      subject: 'Computer Science',
      date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
      duration: 120, // minutes
    },
    {
      id: 'exam-2',
      title: 'Weekly Quiz',
      subject: 'Mathematics',
      date: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000), // 4 days from now
      duration: 45, // minutes
    }
  ]);
  
  const [results, setResults] = useState([
    {
      id: 'result-1',
      title: 'Preliminary Test',
      score: 85,
      classAverage: 72,
      date: '2 days ago'
    }
  ]);
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15,
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };
  
  // Countdown renderer
  const countdownRenderer = ({ days, hours, minutes, seconds, completed }) => {
    if (completed) {
      return <span>Exam is starting!</span>;
    } else {
      return (
        <span className="font-medium">
          {days > 0 ? `${days}d ` : ''}
          {hours}h {minutes}m {seconds}s
        </span>
      );
    }
  };
  
  // Combine real and mock exams
  const exams = [...(activeExams || []), ...mockExams];
  
  return (
    <motion.div
      className="bg-white rounded-xl shadow-md p-6"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Exams Overview</h2>
      
      {/* Upcoming Exams */}
      <div className="mb-6">
        <h3 className="text-md font-medium text-gray-700 mb-3">Upcoming Exams</h3>
        
        {exams.length === 0 ? (
          <motion.div 
            className="bg-yellow-50 border-l-4 border-yellow-400 p-4"
            variants={itemVariants}
          >
            <p className="text-yellow-700">No exams scheduled yet. <span className="font-medium">Check back later.</span></p>
          </motion.div>
        ) : (
          <div className="space-y-4">
            {exams.map((exam, index) => (
              <motion.div 
                key={exam.id || index}
                className="bg-blue-50 rounded-lg p-4 border border-blue-100"
                variants={itemVariants}
                whileHover={{ scale: 1.02 }}
                transition={{ type: 'spring', stiffness: 400 }}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium text-blue-800">{exam.title}</h4>
                    <p className="text-sm text-blue-600">{exam.subject}</p>
                  </div>
                  <div className="bg-blue-200 text-blue-800 text-xs px-2 py-1 rounded-full">
                    {exam.duration} min
                  </div>
                </div>
                
                <div className="mt-3 flex items-center text-sm text-blue-700">
                  <ClockIcon className="w-4 h-4 mr-1" />
                  <span>Starts in: </span>
                  <Countdown 
                    date={exam.date} 
                    renderer={countdownRenderer}
                  />
                </div>
                
                <div className="mt-3">
                  <Link 
                    to="/exams" 
                    className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800"
                  >
                    Prepare Now
                    <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
                    </svg>
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
      
      {/* Recent Results */}
      <div>
        <h3 className="text-md font-medium text-gray-700 mb-3">Recent Results</h3>
        
        {results.length === 0 ? (
          <motion.div 
            className="bg-gray-50 border-l-4 border-gray-300 p-4"
            variants={itemVariants}
          >
            <p className="text-gray-600">No exam results available yet.</p>
          </motion.div>
        ) : (
          <div className="space-y-4">
            {results.map((result, index) => (
              <motion.div 
                key={result.id || index}
                className="bg-gray-50 rounded-lg p-4"
                variants={itemVariants}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-800">{result.title}</h4>
                  <span className="text-sm text-gray-500">{result.date}</span>
                </div>
                
                <div className="mb-1 text-sm flex justify-between">
                  <span>Your Score</span>
                  <span className="font-medium">{result.score}%</span>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                  <motion.div 
                    className="bg-blue-600 h-2.5 rounded-full" 
                    initial={{ width: 0 }}
                    animate={{ width: `${result.score}%` }}
                    transition={{ duration: 1, ease: "easeOut" }}
                  ></motion.div>
                </div>
                
                <div className="mb-1 text-sm flex justify-between">
                  <span>Class Average</span>
                  <span className="font-medium">{result.classAverage}%</span>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <motion.div 
                    className="bg-gray-500 h-2.5 rounded-full" 
                    initial={{ width: 0 }}
                    animate={{ width: `${result.classAverage}%` }}
                    transition={{ duration: 1, ease: "easeOut", delay: 0.3 }}
                  ></motion.div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
}
