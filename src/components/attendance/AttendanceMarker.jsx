import { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Webcam from 'react-webcam';
import * as faceapi from 'face-api.js';
import { supabase } from '../../utils/supabaseClient';
import {
  CameraIcon,
  UserPlusIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

/**
 * AttendanceMarker Component
 * Real-time face recognition attendance marking system
 */
export default function AttendanceMarker({ classId, onAttendanceMarked }) {
  const [isModelLoaded, setIsModelLoaded] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  const [students, setStudents] = useState([]);
  const [recognitionResult, setRecognitionResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [manualMode, setManualMode] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState('');

  const webcamRef = useRef(null);
  const canvasRef = useRef(null);
  const intervalRef = useRef(null);

  // Load face-api models and students on component mount
  useEffect(() => {
    loadModels();
    loadStudents();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [classId]);

  // Load face-api.js models
  const loadModels = async () => {
    try {
      setLoading(true);

      const MODEL_URL = '/models'; // Place models in public/models folder

      await Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),
        faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
        faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL),
        faceapi.nets.faceExpressionNet.loadFromUri(MODEL_URL)
      ]);

      setIsModelLoaded(true);
      console.log('Face-api models loaded successfully');
    } catch (err) {
      console.error('Error loading face-api models:', err);
      setError('Failed to load face recognition models. Using manual mode.');
      setManualMode(true);
    } finally {
      setLoading(false);
    }
  };

  // Load students for the class
  const loadStudents = async () => {
    try {
      const query = supabase
        .from('students')
        .select('*')
        .eq('status', 'active');

      if (classId) {
        query.eq('class_id', classId);
      }

      const { data, error } = await query.order('name');

      if (error) throw error;
      setStudents(data || []);
    } catch (err) {
      console.error('Error loading students:', err);
      setError('Failed to load students');
    }
  };

  // Start face recognition
  const startRecognition = useCallback(async () => {
    if (!isModelLoaded || !webcamRef.current) return;

    setIsCapturing(true);
    setError(null);

    intervalRef.current = setInterval(async () => {
      await detectFaces();
    }, 1000); // Check every second
  }, [isModelLoaded]);

  // Stop face recognition
  const stopRecognition = () => {
    setIsCapturing(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // Detect faces in webcam feed
  const detectFaces = async () => {
    if (!webcamRef.current || !canvasRef.current) return;

    const video = webcamRef.current.video;
    const canvas = canvasRef.current;

    if (video.readyState !== 4) return;

    const displaySize = { width: video.videoWidth, height: video.videoHeight };
    faceapi.matchDimensions(canvas, displaySize);

    try {
      // Detect faces with landmarks and descriptors
      const detections = await faceapi
        .detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
        .withFaceLandmarks()
        .withFaceDescriptors();

      if (detections.length > 0) {
        // For now, take the first detected face
        const detection = detections[0];
        await recognizeFace(detection.descriptor);
      }

      // Draw detections on canvas
      const resizedDetections = faceapi.resizeResults(detections, displaySize);
      canvas.getContext('2d').clearRect(0, 0, canvas.width, canvas.height);
      faceapi.draw.drawDetections(canvas, resizedDetections);
      faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);

    } catch (err) {
      console.error('Face detection error:', err);
    }
  };

  // Recognize face against stored student faces
  const recognizeFace = async (faceDescriptor) => {
    try {
      // In a real implementation, you would:
      // 1. Compare the faceDescriptor with stored face embeddings
      // 2. Find the best match above a confidence threshold
      // 3. Return the matched student

      // For demo purposes, we'll simulate recognition
      const confidence = Math.random() * 0.4 + 0.6; // Random confidence between 0.6-1.0
      const randomStudent = students[Math.floor(Math.random() * students.length)];

      if (confidence > 0.7 && randomStudent) {
        setRecognitionResult({
          student: randomStudent,
          confidence: confidence,
          timestamp: new Date()
        });

        stopRecognition();

        // Auto-mark attendance after 2 seconds
        setTimeout(() => {
          markAttendance(randomStudent, 'face_recognition', confidence);
        }, 2000);
      }
    } catch (err) {
      console.error('Face recognition error:', err);
    }
  };

  // Mark attendance for a student
  const markAttendance = async (student, method = 'manual', confidence = null) => {
    try {
      setLoading(true);

      // Get current location (optional)
      let location = null;
      if (navigator.geolocation) {
        try {
          const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              timeout: 5000,
              enableHighAccuracy: false
            });
          });
          location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
        } catch (geoError) {
          console.warn('Could not get location:', geoError);
        }
      }

      // Mark attendance using the database function
      const { data, error } = await supabase.rpc('mark_attendance', {
        p_student_id: student.student_id,
        p_class_id: classId,
        p_status: 'present',
        p_verification_method: method,
        p_confidence: confidence,
        p_location_lat: location?.lat,
        p_location_lng: location?.lng,
        p_notes: method === 'face_recognition' ? `Auto-marked with ${(confidence * 100).toFixed(1)}% confidence` : null
      });

      if (error) throw error;

      // Log face recognition attempt if applicable
      if (method === 'face_recognition') {
        await supabase.from('face_recognition_logs').insert([{
          student_id: student.student_id,
          confidence_score: confidence,
          recognition_status: 'success',
          processing_time_ms: 1000 // Placeholder
        }]);
      }

      // Show success message
      setRecognitionResult({
        ...recognitionResult,
        marked: true
      });

      // Call callback
      if (onAttendanceMarked) {
        onAttendanceMarked(student, method, confidence);
      }

      // Reset after delay
      setTimeout(() => {
        setRecognitionResult(null);
        setSelectedStudent('');
      }, 3000);

    } catch (err) {
      console.error('Error marking attendance:', err);
      setError(err.message || 'Failed to mark attendance');
    } finally {
      setLoading(false);
    }
  };

  // Handle manual attendance marking
  const handleManualAttendance = () => {
    const student = students.find(s => s.student_id === selectedStudent);
    if (student) {
      markAttendance(student, 'manual');
    }
  };

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <UserPlusIcon className="h-12 w-12 text-green-600 mx-auto mb-2" />
        <h2 className="text-2xl font-bold text-gray-900">Attendance Marking</h2>
        <p className="text-gray-600">
          {manualMode ? 'Manual attendance marking' : 'Face recognition attendance system'}
        </p>
      </div>

      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4"
          >
            <div className="flex items-center">
              <XCircleIcon className="h-5 w-5 mr-2" />
              {error}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mode Toggle */}
      <div className="flex justify-center mb-6">
        <div className="inline-flex rounded-md shadow-sm" role="group">
          <button
            type="button"
            onClick={() => setManualMode(false)}
            disabled={!isModelLoaded}
            className={`px-4 py-2 text-sm font-medium rounded-l-lg border ${
              !manualMode && isModelLoaded
                ? 'bg-blue-600 text-white border-blue-600'
                : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-50'
            } ${!isModelLoaded ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <CameraIcon className="h-4 w-4 mr-2 inline" />
            Face Recognition
          </button>
          <button
            type="button"
            onClick={() => setManualMode(true)}
            className={`px-4 py-2 text-sm font-medium rounded-r-lg border ${
              manualMode
                ? 'bg-blue-600 text-white border-blue-600'
                : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-50'
            }`}
          >
            <ClockIcon className="h-4 w-4 mr-2 inline" />
            Manual Entry
          </button>
        </div>
      </div>

      {/* Face Recognition Mode */}
      {!manualMode && (
        <div className="text-center">
          {loading && !isModelLoaded && (
            <div className="mb-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading face recognition models...</p>
            </div>
          )}

          {isModelLoaded && (
            <div className="relative inline-block">
              <Webcam
                ref={webcamRef}
                audio={false}
                className="rounded-lg border-4 border-blue-200"
                width={640}
                height={480}
              />
              <canvas
                ref={canvasRef}
                className="absolute top-0 left-0 rounded-lg"
                width={640}
                height={480}
              />

              {/* Recognition Overlay */}
              <AnimatePresence>
                {recognitionResult && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0 }}
                    className="absolute top-4 left-4 right-4 bg-white bg-opacity-95 rounded-lg p-4 shadow-lg"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <img
                          src={recognitionResult.student.photo_url}
                          alt={recognitionResult.student.name}
                          className="w-12 h-12 rounded-full object-cover mr-3"
                        />
                        <div className="text-left">
                          <p className="font-semibold text-gray-900">
                            {recognitionResult.student.name}
                          </p>
                          <p className="text-sm text-gray-600">
                            ID: {recognitionResult.student.student_id}
                          </p>
                          <p className="text-sm text-green-600">
                            Confidence: {(recognitionResult.confidence * 100).toFixed(1)}%
                          </p>
                        </div>
                      </div>

                      {recognitionResult.marked ? (
                        <CheckCircleIcon className="h-8 w-8 text-green-600" />
                      ) : (
                        <div className="animate-pulse">
                          <ExclamationTriangleIcon className="h-8 w-8 text-yellow-600" />
                        </div>
                      )}
                    </div>

                    {recognitionResult.marked && (
                      <p className="text-center text-green-600 font-medium mt-2">
                        Attendance Marked Successfully!
                      </p>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}

          {isModelLoaded && (
            <div className="mt-4">
              {!isCapturing ? (
                <button
                  onClick={startRecognition}
                  disabled={loading}
                  className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center mx-auto"
                >
                  <CameraIcon className="h-5 w-5 mr-2" />
                  Start Recognition
                </button>
              ) : (
                <button
                  onClick={stopRecognition}
                  className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 flex items-center mx-auto"
                >
                  <XCircleIcon className="h-5 w-5 mr-2" />
                  Stop Recognition
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Manual Mode */}
      {manualMode && (
        <div className="max-w-md mx-auto">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Student
            </label>
            <select
              value={selectedStudent}
              onChange={(e) => setSelectedStudent(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Choose a student...</option>
              {students.map(student => (
                <option key={student.student_id} value={student.student_id}>
                  {student.name} ({student.student_id})
                </option>
              ))}
            </select>
          </div>

          <button
            onClick={handleManualAttendance}
            disabled={!selectedStudent || loading}
            className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Marking Attendance...
              </>
            ) : (
              <>
                <UserPlusIcon className="h-5 w-5 mr-2" />
                Mark Present
              </>
            )}
          </button>
        </div>
      )}

      {/* Student Count */}
      <div className="mt-6 text-center text-gray-600">
        <p>{students.length} students loaded for attendance</p>
      </div>
    </div>
  );
}
