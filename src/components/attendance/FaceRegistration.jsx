import { useState, useRef, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import Webcam from 'react-webcam';
import { supabase } from '../../utils/supabaseClient';
import { useAuth } from '../../contexts/AuthContext';
import {
  CameraIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  FaceSmileIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import * as faceapi from 'face-api.js';
import { encryptData } from '../../utils/encryption';

export default function FaceRegistration({ onSuccess, onCancel }) {
  const { currentUser, userProfile } = useAuth();
  const [step, setStep] = useState(1); // 1: Instructions, 2: Capture, 3: Processing, 4: Success
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [capturedImage, setCapturedImage] = useState(null);
  const [faceDetected, setFaceDetected] = useState(false);
  const [modelsLoaded, setModelsLoaded] = useState(false);
  const [faceDescriptor, setFaceDescriptor] = useState(null);
  const [detectionProgress, setDetectionProgress] = useState(0);
  const [captureCount, setCaptureCount] = useState(0);
  const [captureQuality, setCaptureQuality] = useState(0);
  const webcamRef = useRef(null);
  const detectionInterval = useRef(null);

  // Video constraints for webcam
  const videoConstraints = {
    width: 1280,
    height: 720,
    facingMode: "user",
    aspectRatio: 1
  };

  // Load face-api.js models
  useEffect(() => {
    async function loadModels() {
      try {
        setLoading(true);

        // Check if we're in development mode
        const isDevelopment = import.meta.env.DEV || !import.meta.env.PROD;

        if (isDevelopment) {
          console.log('Development mode detected. Using mock face detection.');
          // In development, we'll just pretend the models loaded successfully
          setModelsLoaded(true);
          setLoading(false);
          return;
        }

        // In production, actually try to load the models
        // Check if models directory exists by trying to load a test file
        const testRequest = await fetch('/models/ssd_mobilenetv1_model-weights_manifest.json', { method: 'HEAD' })
          .catch(() => ({ ok: false }));

        if (!testRequest.ok) {
          throw new Error('Face detection models not found. Please make sure the models are in the public/models directory.');
        }

        // Load models from public directory
        await Promise.all([
          faceapi.nets.ssdMobilenetv1.loadFromUri('/models'),
          faceapi.nets.faceLandmark68Net.loadFromUri('/models'),
          faceapi.nets.faceRecognitionNet.loadFromUri('/models')
        ]);

        setModelsLoaded(true);
        setLoading(false);
      } catch (error) {
        console.error('Error loading face detection models:', error);

        // In development, we'll still proceed despite errors
        if (import.meta.env.DEV) {
          console.log('Development mode: Continuing despite model loading failure');
          setModelsLoaded(true);
          setLoading(false);
        } else {
          setError(
            'Failed to load face detection models. ' +
            'Please make sure the face-api.js model files are in the public/models directory. ' +
            'You can run "node download-face-models.js" to download them.'
          );
          setLoading(false);
        }
      }
    }

    loadModels();

    // Cleanup function
    return () => {
      if (detectionInterval.current) {
        clearInterval(detectionInterval.current);
      }
    };
  }, []);

  // Start real-time face detection when in capture mode
  useEffect(() => {
    if (step === 2 && modelsLoaded && !capturedImage) {
      startFaceDetection();
    }

    return () => {
      if (detectionInterval.current) {
        clearInterval(detectionInterval.current);
        detectionInterval.current = null;
      }
    };
  }, [step, modelsLoaded, capturedImage]);

  // Start real-time face detection at 5fps
  const startFaceDetection = () => {
    if (detectionInterval.current) {
      clearInterval(detectionInterval.current);
    }

    detectionInterval.current = setInterval(async () => {
      if (webcamRef.current && webcamRef.current.video.readyState === 4) {
        try {
          // Check if we're in development mode
          const isDevelopment = import.meta.env.DEV || !import.meta.env.PROD;

          if (isDevelopment) {
            // In development, simulate face detection
            setFaceDetected(true);

            // Simulate varying quality scores for realism
            const randomQuality = Math.floor(70 + Math.random() * 30); // 70-100
            setCaptureQuality(randomQuality);

            // Auto-capture simulation
            if (randomQuality > 90) {
              setCaptureCount(prev => prev + 1);
              if (captureCount >= 3) {
                captureImage();
              }
            } else {
              setCaptureCount(0);
            }
            return;
          }

          // In production, do actual face detection
          const video = webcamRef.current.video;
          const detections = await faceapi.detectSingleFace(video)
            .withFaceLandmarks();

          if (detections) {
            setFaceDetected(true);

            // Calculate quality based on face size and position
            const faceWidth = detections.detection.box.width;
            const faceHeight = detections.detection.box.height;
            const centerX = detections.detection.box.x + faceWidth / 2;
            const centerY = detections.detection.box.y + faceHeight / 2;

            const idealWidth = video.videoWidth * 0.4; // Face should take up ~40% of frame
            const idealHeight = video.videoHeight * 0.4;
            const idealCenterX = video.videoWidth / 2;
            const idealCenterY = video.videoHeight / 2;

            // Calculate quality score (0-100)
            const sizeScore = Math.min(100, (faceWidth / idealWidth) * 100);
            const positionScore = 100 - Math.min(100,
              (Math.abs(centerX - idealCenterX) / idealCenterX +
               Math.abs(centerY - idealCenterY) / idealCenterY) * 50);

            const qualityScore = Math.round((sizeScore * 0.6) + (positionScore * 0.4));
            setCaptureQuality(qualityScore);

            // Auto-capture if quality is excellent for 3 consecutive checks
            if (qualityScore > 90) {
              setCaptureCount(prev => prev + 1);
              if (captureCount >= 3) {
                captureImage();
              }
            } else {
              setCaptureCount(0);
            }
          } else {
            setFaceDetected(false);
            setCaptureQuality(0);
            setCaptureCount(0);
          }
        } catch (error) {
          console.error('Error during face detection:', error);

          // In development, continue despite errors
          if (import.meta.env.DEV) {
            console.log('Development mode: Simulating face detection despite error');
            setFaceDetected(true);
            setCaptureQuality(85); // Simulate good quality
          }
        }
      }
    }, 200); // 5fps (200ms interval)
  };

  // Capture image from webcam
  const captureImage = useCallback(() => {
    if (!webcamRef.current || !modelsLoaded) return;

    const imageSrc = webcamRef.current.getScreenshot();
    setCapturedImage(imageSrc);

    // Stop the detection interval
    if (detectionInterval.current) {
      clearInterval(detectionInterval.current);
      detectionInterval.current = null;
    }

    // Process the captured image
    processImage(imageSrc);
  }, [webcamRef, modelsLoaded]);

  // Process the captured image to extract face descriptor
  const processImage = async (imageSrc) => {
    setStep(3); // Move to processing step
    setLoading(true);
    setError(null);
    setDetectionProgress(10);

    try {
      // Check if we're in development mode
      const isDevelopment = import.meta.env.DEV || !import.meta.env.PROD;

      if (isDevelopment) {
        console.log('Development mode: Simulating face processing');

        // Simulate processing steps with delays
        setDetectionProgress(30);
        await new Promise(resolve => setTimeout(resolve, 300));

        setDetectionProgress(60);
        await new Promise(resolve => setTimeout(resolve, 300));

        setDetectionProgress(90);
        await new Promise(resolve => setTimeout(resolve, 300));

        // Create a mock descriptor (128-element Float32Array with random values)
        const mockDescriptor = new Float32Array(128);
        for (let i = 0; i < 128; i++) {
          mockDescriptor[i] = Math.random() * 2 - 1; // Values between -1 and 1
        }

        setFaceDescriptor(mockDescriptor);
        setDetectionProgress(100);

        // Short delay before moving to success step
        setTimeout(() => {
          setLoading(false);
          setStep(4); // Move to success step
        }, 500);

        return;
      }

      // Production mode - actual face processing
      // Create an HTML image element from the captured image
      const img = new Image();
      img.src = imageSrc;
      await new Promise(resolve => { img.onload = resolve; });
      setDetectionProgress(30);

      // Detect face in the image
      const detection = await faceapi.detectSingleFace(img)
        .withFaceLandmarks()
        .withFaceDescriptor();
      setDetectionProgress(60);

      if (!detection) {
        throw new Error('No face detected in the captured image. Please try again.');
      }

      // Store the face descriptor
      setFaceDescriptor(detection.descriptor);
      setDetectionProgress(100);

      // Short delay before moving to success step
      setTimeout(() => {
        setLoading(false);
        setStep(4); // Move to success step
      }, 500);
    } catch (error) {
      console.error('Error processing face:', error);

      // In development, continue despite errors
      if (import.meta.env.DEV) {
        console.log('Development mode: Simulating successful face processing despite error');

        // Create a mock descriptor
        const mockDescriptor = new Float32Array(128).fill(0.5);
        setFaceDescriptor(mockDescriptor);
        setDetectionProgress(100);

        setTimeout(() => {
          setLoading(false);
          setStep(4); // Move to success step
        }, 500);
      } else {
        setError(error.message || 'Failed to process face. Please try again.');
        setLoading(false);
        // Go back to capture step
        setStep(2);
        setCapturedImage(null);
      }
    }
  };

  // Retake the photo
  const retakeImage = () => {
    setCapturedImage(null);
    setFaceDescriptor(null);
    setError(null);
    setStep(2); // Back to capture step
  };

  // Save the face data to Supabase
  const saveFaceData = async () => {
    if (!faceDescriptor || !currentUser) {
      setError('Face data not available. Please try again.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Check if we're in development mode
      const isDevelopment = import.meta.env.DEV || !import.meta.env.PROD;

      if (isDevelopment) {
        console.log('Development mode: Simulating face data storage');

        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Call the success callback with mock data
        if (onSuccess) {
          onSuccess({
            imageUrl: 'https://example.com/mock-face-image.webp',
            timestamp: new Date().toISOString()
          });
        }
      } else {
        // Production mode - actual storage
        // Convert Float32Array to regular array for storage
        const descriptorArray = Array.from(faceDescriptor);

        // Encrypt the descriptor for security
        const encryptedDescriptor = encryptData(JSON.stringify(descriptorArray));

        // Upload the image to Supabase Storage
        const fileName = `${currentUser.id}_${Date.now()}.webp`;
        const filePath = `reference_faces/${fileName}`;

        // Convert data URL to Blob
        const base64Data = capturedImage.split(',')[1];
        const blob = await fetch(`data:image/webp;base64,${base64Data}`).then(res => res.blob());

        // Upload to Supabase Storage
        const { error: uploadError, data: uploadData } = await supabase.storage
          .from('reference')
          .upload(filePath, blob, {
            contentType: 'image/webp',
            upsert: true
          });

        if (uploadError) throw uploadError;

        // Get the public URL
        const { data: urlData } = supabase.storage
          .from('reference')
          .getPublicUrl(filePath);

        const imageUrl = urlData.publicUrl;

        // Update the student record with the face descriptor
        const { error: updateError } = await supabase
          .from('students')
          .update({
            face_descriptor: encryptedDescriptor,
            reference_image_url: imageUrl
          })
          .eq('id', currentUser.id);

        if (updateError) throw updateError;

        // Call the success callback
        if (onSuccess) {
          onSuccess({
            imageUrl,
            timestamp: new Date().toISOString()
          });
        }
      }
    } catch (error) {
      console.error('Error saving face data:', error);

      // In development, continue despite errors
      if (import.meta.env.DEV) {
        console.log('Development mode: Simulating successful save despite error');

        if (onSuccess) {
          onSuccess({
            imageUrl: 'https://example.com/mock-face-image.webp',
            timestamp: new Date().toISOString()
          });
        }
      } else {
        setError('Failed to save face data. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.3,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    },
    exit: { opacity: 0 }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  };

  // Render different steps
  const renderStep = () => {
    switch (step) {
      case 1: // Instructions
        return (
          <motion.div
            className="text-center"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <motion.div variants={itemVariants} className="mb-6">
              <FaceSmileIcon className="w-16 h-16 mx-auto text-blue-500 mb-4" />
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Face Registration</h2>
              <p className="text-gray-600 mb-4">
                We'll use your webcam to capture your face for secure attendance tracking.
              </p>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-blue-50 p-4 rounded-lg mb-6 text-left">
              <h3 className="font-semibold text-blue-800 mb-2">For best results:</h3>
              <ul className="list-disc list-inside text-blue-700 space-y-1">
                <li>Ensure good lighting on your face</li>
                <li>Remove glasses, hats, or masks</li>
                <li>Look directly at the camera</li>
                <li>Position your face in the center of the frame</li>
                <li>Keep a neutral expression</li>
              </ul>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-yellow-50 p-4 rounded-lg mb-6 text-left">
              <h3 className="font-semibold text-yellow-800 mb-2">Privacy Notice:</h3>
              <p className="text-yellow-700">
                Your face data is encrypted and securely stored. It will only be used for attendance verification.
              </p>
            </motion.div>

            <motion.div variants={itemVariants} className="flex justify-center space-x-4">
              <button
                onClick={() => onCancel && onCancel()}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => setStep(2)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Continue
              </button>
            </motion.div>
          </motion.div>
        );

      case 2: // Capture
        return (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <motion.h2 variants={itemVariants} className="text-xl font-semibold text-gray-800 mb-4 text-center">
              Position Your Face
            </motion.h2>

            {error && (
              <motion.div variants={itemVariants} className="mb-4 bg-red-100 text-red-700 p-3 rounded-md">
                {error}
              </motion.div>
            )}

            <motion.div variants={itemVariants} className="mb-6">
              <div className="relative rounded-lg overflow-hidden">
                {!capturedImage ? (
                  <>
                    <Webcam
                      audio={false}
                      ref={webcamRef}
                      screenshotFormat="image/webp"
                      videoConstraints={videoConstraints}
                      className="w-full rounded-lg"
                      mirrored={true}
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={`w-64 h-64 border-4 border-dashed rounded-full transition-all duration-300 ${
                        faceDetected
                          ? captureQuality > 80
                            ? 'border-green-500 opacity-80'
                            : 'border-yellow-500 opacity-70'
                          : 'border-red-500 opacity-50'
                      }`}></div>
                    </div>

                    {/* Quality indicator */}
                    {faceDetected && (
                      <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                        <div className="bg-black bg-opacity-50 rounded-full px-3 py-1 text-white text-sm flex items-center">
                          <div className="mr-2">Quality:</div>
                          <div className="w-24 h-3 bg-gray-300 rounded-full overflow-hidden">
                            <div
                              className={`h-full ${
                                captureQuality > 80 ? 'bg-green-500' :
                                captureQuality > 50 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${captureQuality}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Face detection status */}
                    <div className="absolute top-4 right-4">
                      {!modelsLoaded ? (
                        <div className="bg-blue-500 text-white px-2 py-1 rounded text-sm flex items-center">
                          <ArrowPathIcon className="w-4 h-4 mr-1 animate-spin" />
                          Loading...
                        </div>
                      ) : faceDetected ? (
                        <div className="bg-green-500 text-white px-2 py-1 rounded text-sm flex items-center">
                          <CheckCircleIcon className="w-4 h-4 mr-1" />
                          Face Detected
                        </div>
                      ) : (
                        <div className="bg-red-500 text-white px-2 py-1 rounded text-sm flex items-center">
                          <XCircleIcon className="w-4 h-4 mr-1" />
                          No Face Detected
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <img
                    src={capturedImage}
                    alt="Captured"
                    className="w-full rounded-lg"
                  />
                )}
              </div>
            </motion.div>

            <motion.div variants={itemVariants} className="flex justify-center space-x-4">
              <button
                onClick={() => setStep(1)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Back
              </button>

              <button
                onClick={captureImage}
                disabled={loading || !modelsLoaded || !faceDetected || captureQuality < 50}
                className={`px-4 py-2 rounded-md flex items-center ${
                  loading || !modelsLoaded || !faceDetected || captureQuality < 50
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                <CameraIcon className="w-5 h-5 mr-2" />
                Capture Photo
              </button>
            </motion.div>
          </motion.div>
        );

      case 3: // Processing
        return (
          <motion.div
            className="text-center"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <motion.div variants={itemVariants} className="mb-6">
              <img
                src={capturedImage}
                alt="Processing"
                className="w-64 h-64 mx-auto object-cover rounded-full border-4 border-blue-500 mb-4"
              />
              <h2 className="text-xl font-semibold text-gray-800 mb-2">Processing Your Face</h2>
              <p className="text-gray-600">
                Please wait while we process your face data...
              </p>
            </motion.div>

            <motion.div variants={itemVariants} className="mb-6">
              <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-600 transition-all duration-300"
                  style={{ width: `${detectionProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                {detectionProgress < 30 ? 'Analyzing image...' :
                 detectionProgress < 60 ? 'Detecting facial features...' :
                 detectionProgress < 100 ? 'Generating secure descriptor...' :
                 'Processing complete!'}
              </p>
            </motion.div>

            {error && (
              <motion.div variants={itemVariants} className="mb-4 bg-red-100 text-red-700 p-3 rounded-md">
                {error}
              </motion.div>
            )}
          </motion.div>
        );

      case 4: // Success
        return (
          <motion.div
            className="text-center"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <motion.div variants={itemVariants} className="mb-6">
              <CheckCircleIcon className="w-16 h-16 mx-auto text-green-500 mb-4" />
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Face Captured Successfully!</h2>
              <p className="text-gray-600 mb-4">
                Your face has been successfully processed and is ready to be saved.
              </p>
            </motion.div>

            <motion.div variants={itemVariants} className="mb-6">
              <div className="relative w-64 h-64 mx-auto">
                <img
                  src={capturedImage}
                  alt="Captured"
                  className="w-full h-full object-cover rounded-lg"
                />
                <div className="absolute bottom-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-sm flex items-center">
                  <ShieldCheckIcon className="w-4 h-4 mr-1" />
                  Secured
                </div>
              </div>
            </motion.div>

            <motion.div variants={itemVariants} className="bg-blue-50 p-4 rounded-lg mb-6 text-left">
              <h3 className="font-semibold text-blue-800 mb-2">What happens next?</h3>
              <p className="text-blue-700">
                Your encrypted face data will be securely stored and used only for attendance verification.
                You can now mark your attendance using the face recognition system.
              </p>
            </motion.div>

            {error && (
              <motion.div variants={itemVariants} className="mb-4 bg-red-100 text-red-700 p-3 rounded-md">
                {error}
              </motion.div>
            )}

            <motion.div variants={itemVariants} className="flex justify-center space-x-4">
              <button
                onClick={retakeImage}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                disabled={loading}
              >
                Retake Photo
              </button>
              <button
                onClick={saveFaceData}
                className={`px-4 py-2 rounded-md flex items-center ${
                  loading
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <ArrowPathIcon className="w-5 h-5 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <CheckCircleIcon className="w-5 h-5 mr-2" />
                    Save & Continue
                  </>
                )}
              </button>
            </motion.div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-xl p-6 max-w-2xl mx-auto">
      {renderStep()}
    </div>
  );
}
