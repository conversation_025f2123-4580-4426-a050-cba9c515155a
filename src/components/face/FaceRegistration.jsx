import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Webcam from 'react-webcam';
import { 
  CameraIcon, 
  UserPlusIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { 
  KNOWN_STUDENTS, 
  loadFaceApiModels, 
  registerFaceEmbedding, 
  validateImageQuality,
  areModelsLoaded 
} from '../../utils/faceVerification';
import { supabase } from '../../utils/supabaseClient';

/**
 * FaceRegistration Component
 * Register original face embeddings for known students
 */
export default function FaceRegistration() {
  const [selectedStudent, setSelectedStudent] = useState(KNOWN_STUDENTS[0]);
  const [capturedPhoto, setCapturedPhoto] = useState(null);
  const [isModelLoaded, setIsModelLoaded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [registering, setRegistering] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [qualityCheck, setQualityCheck] = useState(null);
  
  const webcamRef = useRef(null);
  
  // Load face-api models on component mount
  useEffect(() => {
    const initializeModels = async () => {
      setLoading(true);
      try {
        if (!areModelsLoaded()) {
          const success = await loadFaceApiModels();
          setIsModelLoaded(success);
          if (!success) {
            setError('Failed to load face recognition models. Registration will use mock mode.');
          }
        } else {
          setIsModelLoaded(true);
        }
      } catch (err) {
        console.error('Error initializing models:', err);
        setError('Failed to initialize face recognition system.');
        setIsModelLoaded(false);
      } finally {
        setLoading(false);
      }
    };
    
    initializeModels();
  }, []);
  
  // Capture photo from webcam
  const capturePhoto = async () => {
    try {
      if (!webcamRef.current) {
        throw new Error('Webcam not available');
      }
      
      const imageSrc = webcamRef.current.getScreenshot();
      if (!imageSrc) {
        throw new Error('Failed to capture image');
      }
      
      setCapturedPhoto(imageSrc);
      setError(null);
      
      // Validate image quality if models are loaded
      if (isModelLoaded) {
        const img = new Image();
        img.onload = async () => {
          const quality = await validateImageQuality(img);
          setQualityCheck(quality);
        };
        img.src = imageSrc;
      }
    } catch (err) {
      console.error('Error capturing photo:', err);
      setError(err.message);
    }
  };
  
  // Retake photo
  const retakePhoto = () => {
    setCapturedPhoto(null);
    setQualityCheck(null);
    setResult(null);
    setError(null);
  };
  
  // Convert base64 to blob
  const base64ToBlob = (base64, mimeType) => {
    const byteCharacters = atob(base64.split(',')[1]);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  };
  
  // Register face embedding
  const handleRegister = async () => {
    if (!capturedPhoto) {
      setError('Please capture a photo first');
      return;
    }
    
    setRegistering(true);
    setError(null);
    setResult(null);
    
    try {
      // Create image element for face processing
      const img = new Image();
      
      const registrationPromise = new Promise((resolve, reject) => {
        img.onload = async () => {
          try {
            // Upload photo to storage (if using real Supabase)
            let photoUrl = null;
            const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
            
            if (!USE_MOCK) {
              const photoBlob = base64ToBlob(capturedPhoto, 'image/jpeg');
              const fileName = `face_registration_${selectedStudent.student_id}_${Date.now()}.jpg`;
              
              const { data: uploadData, error: uploadError } = await supabase.storage
                .from('student-photos')
                .upload(fileName, photoBlob, {
                  contentType: 'image/jpeg',
                  upsert: false
                });
              
              if (uploadError) throw uploadError;
              
              const { data: { publicUrl } } = supabase.storage
                .from('student-photos')
                .getPublicUrl(fileName);
              
              photoUrl = publicUrl;
            } else {
              photoUrl = `mock://face_registration_${selectedStudent.student_id}.jpg`;
            }
            
            // Register face embedding
            const registrationResult = await registerFaceEmbedding(
              selectedStudent.student_id,
              img,
              photoUrl
            );
            
            resolve(registrationResult);
          } catch (error) {
            reject(error);
          }
        };
        
        img.onerror = () => {
          reject(new Error('Failed to load captured image'));
        };
      });
      
      img.src = capturedPhoto;
      
      const registrationResult = await registrationPromise;
      
      if (registrationResult.success) {
        setResult({
          success: true,
          message: registrationResult.message,
          studentName: selectedStudent.name,
          studentId: selectedStudent.student_id,
          embeddingLength: registrationResult.embeddingLength,
          detectionScore: registrationResult.detectionScore
        });
        
        // Clear captured photo after successful registration
        setTimeout(() => {
          retakePhoto();
        }, 3000);
      } else {
        throw new Error(registrationResult.error);
      }
    } catch (err) {
      console.error('Error registering face:', err);
      setError(err.message || 'Failed to register face');
    } finally {
      setRegistering(false);
    }
  };
  
  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <UserPlusIcon className="h-12 w-12 text-blue-600 mx-auto mb-2" />
        <h2 className="text-2xl font-bold text-gray-900">Face Registration</h2>
        <p className="text-gray-600">Register original face embeddings for student verification</p>
      </div>
      
      {/* Loading State */}
      {loading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading face recognition models...</p>
        </div>
      )}
      
      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4"
          >
            <div className="flex items-center">
              <XCircleIcon className="h-5 w-5 mr-2" />
              {error}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Success Message */}
      <AnimatePresence>
        {result && result.success && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0 }}
            className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4"
          >
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              <div>
                <p className="font-medium">{result.message}</p>
                <p className="text-sm">
                  Student: {result.studentName} ({result.studentId}) | 
                  Detection Score: {(result.detectionScore * 100).toFixed(1)}% | 
                  Embedding Size: {result.embeddingLength}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {!loading && (
        <div className="space-y-6">
          {/* Student Selection */}
          <div className="bg-gray-50 rounded-lg p-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Student for Face Registration
            </label>
            <select
              value={selectedStudent.id}
              onChange={(e) => {
                const student = KNOWN_STUDENTS.find(s => s.id === parseInt(e.target.value));
                setSelectedStudent(student);
                retakePhoto(); // Clear any existing photo when switching students
              }}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {KNOWN_STUDENTS.map(student => (
                <option key={student.id} value={student.id}>
                  {student.name} ({student.student_id}) - {student.email}
                </option>
              ))}
            </select>
          </div>
          
          {/* Camera Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Live Camera */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Live Camera</h3>
              <div className="relative">
                <Webcam
                  ref={webcamRef}
                  audio={false}
                  screenshotFormat="image/jpeg"
                  className="w-full rounded-lg border-4 border-blue-200"
                  width={400}
                  height={300}
                />
                <div className="absolute inset-0 border-2 border-dashed border-blue-400 rounded-lg pointer-events-none">
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 border-2 border-blue-500 rounded-full opacity-50"></div>
                </div>
              </div>
              
              <button
                onClick={capturePhoto}
                disabled={registering}
                className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
              >
                <CameraIcon className="h-5 w-5 mr-2" />
                Capture Photo
              </button>
            </div>
            
            {/* Captured Photo */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Captured Photo</h3>
              <div className="w-full h-[300px] bg-gray-100 rounded-lg border-4 border-gray-200 flex items-center justify-center">
                {capturedPhoto ? (
                  <div className="relative">
                    <img
                      src={capturedPhoto}
                      alt="Captured face"
                      className="max-w-full max-h-full rounded-lg"
                    />
                    {qualityCheck && (
                      <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs font-medium ${
                        qualityCheck.valid 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        Quality: {(qualityCheck.score * 100).toFixed(1)}%
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-gray-500">
                    <CameraIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No photo captured</p>
                  </div>
                )}
              </div>
              
              {capturedPhoto && (
                <div className="flex space-x-3">
                  <button
                    onClick={retakePhoto}
                    disabled={registering}
                    className="flex-1 bg-gray-600 text-white px-4 py-3 rounded-lg hover:bg-gray-700 disabled:opacity-50 flex items-center justify-center"
                  >
                    <ArrowPathIcon className="h-5 w-5 mr-2" />
                    Retake
                  </button>
                  
                  <button
                    onClick={handleRegister}
                    disabled={registering || (qualityCheck && !qualityCheck.valid)}
                    className="flex-1 bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
                  >
                    {registering ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Registering...
                      </>
                    ) : (
                      <>
                        <UserPlusIcon className="h-5 w-5 mr-2" />
                        Register {selectedStudent.name}'s Face
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
          
          {/* Quality Check Results */}
          {qualityCheck && (
            <div className={`p-4 rounded-lg border ${
              qualityCheck.valid 
                ? 'bg-green-50 border-green-200 text-green-800' 
                : 'bg-yellow-50 border-yellow-200 text-yellow-800'
            }`}>
              <div className="flex items-center">
                {qualityCheck.valid ? (
                  <CheckCircleIcon className="h-5 w-5 mr-2" />
                ) : (
                  <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
                )}
                <div>
                  <p className="font-medium">Image Quality Check</p>
                  <p className="text-sm">
                    {qualityCheck.reason} | 
                    Detection Score: {(qualityCheck.detectionScore * 100).toFixed(1)}% | 
                    Face Ratio: {(qualityCheck.faceRatio * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          )}
          
          {/* System Status */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">System Status</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  isModelLoaded ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                Face Models: {isModelLoaded ? 'Loaded' : 'Not Loaded'}
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                Mode: {import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' ? 'Mock' : 'Production'}
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                Students: {KNOWN_STUDENTS.length} Available
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
