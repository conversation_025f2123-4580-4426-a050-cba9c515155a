import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  UserGroupIcon, 
  ShieldCheckIcon,
  ClockIcon,
  EyeIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { CSVLink } from 'react-csv';
import { KNOWN_STUDENTS, getFaceVerificationStats } from '../../utils/faceVerification';
import { supabase } from '../../utils/supabaseClient';

/**
 * FaceVerificationDashboard Component
 * Comprehensive dashboard for face verification analytics and monitoring
 */
export default function FaceVerificationDashboard() {
  const [stats, setStats] = useState(null);
  const [verificationLogs, setVerificationLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedStudent, setSelectedStudent] = useState('all');
  const [timeRange, setTimeRange] = useState(30);
  
  useEffect(() => {
    loadDashboardData();
  }, [selectedStudent, timeRange]);
  
  // Load dashboard data
  const loadDashboardData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Load verification statistics
      const statsResult = await getFaceVerificationStats(
        selectedStudent === 'all' ? null : selectedStudent,
        timeRange
      );
      
      if (statsResult.success) {
        setStats(statsResult.stats);
      } else {
        throw new Error(statsResult.error);
      }
      
      // Load recent verification logs (mock data for development)
      await loadVerificationLogs();
      
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError(err.message);
      
      // Fallback to mock data
      setStats({
        total_attempts: 156,
        successful_matches: 142,
        failed_matches: 14,
        success_rate: 91.03,
        avg_confidence: 87.5,
        max_confidence: 98.7,
        min_confidence: 45.2,
        avg_verification_time_ms: 1250
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Load verification logs
  const loadVerificationLogs = async () => {
    try {
      // Check if we're using mock Supabase
      const USE_MOCK = import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' || !import.meta.env.VITE_SUPABASE_URL;
      
      if (USE_MOCK) {
        // Generate mock verification logs
        const mockLogs = [];
        const statuses = ['success', 'failed', 'low_confidence'];
        const methods = ['1:1', '1:N'];
        
        for (let i = 0; i < 20; i++) {
          const student = KNOWN_STUDENTS[Math.floor(Math.random() * KNOWN_STUDENTS.length)];
          const isSuccess = Math.random() > 0.2; // 80% success rate
          const confidence = isSuccess ? Math.random() * 30 + 70 : Math.random() * 50 + 20;
          
          mockLogs.push({
            id: `mock_${i}`,
            student_id: student.student_id,
            student_name: student.name,
            confidence_percentage: parseFloat(confidence.toFixed(2)),
            is_match: isSuccess,
            verification_method: methods[Math.floor(Math.random() * methods.length)],
            verification_time_ms: Math.floor(Math.random() * 2000) + 500,
            created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
            device_info: {
              userAgent: navigator.userAgent,
              platform: navigator.platform
            }
          });
        }
        
        setVerificationLogs(mockLogs.sort((a, b) => new Date(b.created_at) - new Date(a.created_at)));
        return;
      }
      
      // Load from database
      let query = supabase
        .from('face_verification_attempts')
        .select(`
          *,
          students(name)
        `)
        .order('created_at', { ascending: false })
        .limit(50);
      
      if (selectedStudent !== 'all') {
        query = query.eq('student_id', selectedStudent);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      setVerificationLogs(data || []);
    } catch (err) {
      console.error('Error loading verification logs:', err);
    }
  };
  
  // Get status color
  const getStatusColor = (isMatch, confidence) => {
    if (!isMatch) return 'text-red-600 bg-red-100';
    if (confidence > 90) return 'text-green-600 bg-green-100';
    if (confidence > 75) return 'text-yellow-600 bg-yellow-100';
    return 'text-orange-600 bg-orange-100';
  };
  
  // Prepare CSV data
  const csvData = verificationLogs.map(log => ({
    'Date': new Date(log.created_at).toLocaleDateString(),
    'Time': new Date(log.created_at).toLocaleTimeString(),
    'Student ID': log.student_id,
    'Student Name': log.student_name || log.students?.name || 'Unknown',
    'Confidence (%)': log.confidence_percentage,
    'Match': log.is_match ? 'Yes' : 'No',
    'Method': log.verification_method,
    'Processing Time (ms)': log.verification_time_ms,
    'Device': log.device_info?.platform || 'Unknown'
  }));
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Face Verification Dashboard</h1>
          <p className="text-gray-600">Monitor and analyze face verification performance</p>
        </div>
        
        <div className="flex space-x-3">
          <CSVLink
            data={csvData}
            filename={`face-verification-logs-${new Date().toISOString().split('T')[0]}.csv`}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
            Export Logs
          </CSVLink>
        </div>
      </div>
      
      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Student Filter
            </label>
            <select
              value={selectedStudent}
              onChange={(e) => setSelectedStudent(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Students</option>
              {KNOWN_STUDENTS.map(student => (
                <option key={student.student_id} value={student.student_id}>
                  {student.name} ({student.student_id})
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Time Range
            </label>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(parseInt(e.target.value))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={7}>Last 7 days</option>
              <option value={30}>Last 30 days</option>
              <option value={90}>Last 90 days</option>
              <option value={365}>Last year</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* Statistics Cards */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      ) : stats ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-md p-6"
          >
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <ChartBarIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Attempts</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_attempts}</p>
              </div>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-lg shadow-md p-6"
          >
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <ShieldCheckIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">{stats.success_rate}%</p>
              </div>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-lg shadow-md p-6"
          >
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <UserGroupIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Confidence</p>
                <p className="text-2xl font-bold text-gray-900">{stats.avg_confidence}%</p>
              </div>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-lg shadow-md p-6"
          >
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <ClockIcon className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Time</p>
                <p className="text-2xl font-bold text-gray-900">{stats.avg_verification_time_ms}ms</p>
              </div>
            </div>
          </motion.div>
        </div>
      ) : null}
      
      {/* Detailed Statistics */}
      {stats && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Detailed Statistics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="bg-gray-50 p-3 rounded">
              <div className="font-medium text-gray-700">Successful Matches</div>
              <div className="text-lg font-bold text-green-600">{stats.successful_matches}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="font-medium text-gray-700">Failed Matches</div>
              <div className="text-lg font-bold text-red-600">{stats.failed_matches}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="font-medium text-gray-700">Max Confidence</div>
              <div className="text-lg font-bold text-blue-600">{stats.max_confidence}%</div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="font-medium text-gray-700">Min Confidence</div>
              <div className="text-lg font-bold text-orange-600">{stats.min_confidence}%</div>
            </div>
          </div>
        </div>
      )}
      
      {/* Verification Logs */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Verification Attempts</h3>
        </div>
        
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading verification logs...</span>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Confidence
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {verificationLogs.map((log, index) => (
                  <motion.tr
                    key={log.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {log.student_name || log.students?.name || 'Unknown'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {log.student_id}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {log.confidence_percentage}%
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        getStatusColor(log.is_match, log.confidence_percentage)
                      }`}>
                        {log.is_match ? 'Match' : 'No Match'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.verification_method}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.verification_time_ms}ms
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(log.created_at).toLocaleDateString()} {new Date(log.created_at).toLocaleTimeString()}
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        
        {!loading && verificationLogs.length === 0 && (
          <div className="text-center py-12">
            <EyeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No verification logs found for the selected filters.</p>
          </div>
        )}
      </div>
    </div>
  );
}
