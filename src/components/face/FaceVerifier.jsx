import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Webcam from 'react-webcam';
import { 
  CameraIcon, 
  ShieldCheckIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  IdentificationIcon
} from '@heroicons/react/24/outline';
import { 
  KNOWN_STUDENTS, 
  loadFaceApiModels, 
  verifyStudentFace, 
  identifyStudent,
  validateImageQuality,
  areModelsLoaded 
} from '../../utils/faceVerification';

/**
 * FaceVerifier Component
 * Verify student identity using face recognition
 */
export default function FaceVerifier() {
  const [selectedStudentId, setSelectedStudentId] = useState(KNOWN_STUDENTS[0].student_id);
  const [capturedPhoto, setCapturedPhoto] = useState(null);
  const [isModelLoaded, setIsModelLoaded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [qualityCheck, setQualityCheck] = useState(null);
  const [verificationMode, setVerificationMode] = useState('1:1'); // '1:1' or '1:N'
  
  const webcamRef = useRef(null);
  
  // Load face-api models on component mount
  useEffect(() => {
    const initializeModels = async () => {
      setLoading(true);
      try {
        if (!areModelsLoaded()) {
          const success = await loadFaceApiModels();
          setIsModelLoaded(success);
          if (!success) {
            setError('Failed to load face recognition models. Verification will use mock mode.');
          }
        } else {
          setIsModelLoaded(true);
        }
      } catch (err) {
        console.error('Error initializing models:', err);
        setError('Failed to initialize face recognition system.');
        setIsModelLoaded(false);
      } finally {
        setLoading(false);
      }
    };
    
    initializeModels();
  }, []);
  
  // Capture photo from webcam
  const capturePhoto = async () => {
    try {
      if (!webcamRef.current) {
        throw new Error('Webcam not available');
      }
      
      const imageSrc = webcamRef.current.getScreenshot();
      if (!imageSrc) {
        throw new Error('Failed to capture image');
      }
      
      setCapturedPhoto(imageSrc);
      setError(null);
      setResult(null);
      
      // Validate image quality if models are loaded
      if (isModelLoaded) {
        const img = new Image();
        img.onload = async () => {
          const quality = await validateImageQuality(img);
          setQualityCheck(quality);
        };
        img.src = imageSrc;
      }
    } catch (err) {
      console.error('Error capturing photo:', err);
      setError(err.message);
    }
  };
  
  // Retake photo
  const retakePhoto = () => {
    setCapturedPhoto(null);
    setQualityCheck(null);
    setResult(null);
    setError(null);
  };
  
  // Handle verification (1:1 matching)
  const handleVerify = async () => {
    if (!capturedPhoto) {
      setError('Please capture a photo first');
      return;
    }
    
    setVerifying(true);
    setError(null);
    setResult(null);
    
    try {
      const img = new Image();
      
      const verificationPromise = new Promise((resolve, reject) => {
        img.onload = async () => {
          try {
            const verificationResult = await verifyStudentFace(
              img,
              selectedStudentId,
              `verification_${Date.now()}.jpg`
            );
            resolve(verificationResult);
          } catch (error) {
            reject(error);
          }
        };
        
        img.onerror = () => {
          reject(new Error('Failed to load captured image'));
        };
      });
      
      img.src = capturedPhoto;
      
      const verificationResult = await verificationPromise;
      
      if (verificationResult.success) {
        setResult({
          ...verificationResult,
          mode: '1:1',
          selectedStudent: KNOWN_STUDENTS.find(s => s.student_id === selectedStudentId)
        });
      } else {
        throw new Error(verificationResult.error);
      }
    } catch (err) {
      console.error('Error verifying face:', err);
      setError(err.message || 'Failed to verify face');
    } finally {
      setVerifying(false);
    }
  };
  
  // Handle identification (1:N matching)
  const handleIdentify = async () => {
    if (!capturedPhoto) {
      setError('Please capture a photo first');
      return;
    }
    
    setVerifying(true);
    setError(null);
    setResult(null);
    
    try {
      const img = new Image();
      
      const identificationPromise = new Promise((resolve, reject) => {
        img.onload = async () => {
          try {
            const identificationResult = await identifyStudent(img);
            resolve(identificationResult);
          } catch (error) {
            reject(error);
          }
        };
        
        img.onerror = () => {
          reject(new Error('Failed to load captured image'));
        };
      });
      
      img.src = capturedPhoto;
      
      const identificationResult = await identificationPromise;
      
      if (identificationResult.success) {
        setResult({
          ...identificationResult,
          mode: '1:N',
          isMatch: identificationResult.identified,
          studentName: identificationResult.student?.name || 'Unknown',
          studentId: identificationResult.student?.student_id || 'N/A'
        });
      } else {
        throw new Error(identificationResult.error);
      }
    } catch (err) {
      console.error('Error identifying face:', err);
      setError(err.message || 'Failed to identify face');
    } finally {
      setVerifying(false);
    }
  };
  
  // Get result color based on verification outcome
  const getResultColor = () => {
    if (!result) return '';
    
    if (result.isMatch || result.identified) {
      return result.confidence > 90 ? 'bg-green-100 border-green-200 text-green-800' :
             result.confidence > 75 ? 'bg-yellow-100 border-yellow-200 text-yellow-800' :
             'bg-orange-100 border-orange-200 text-orange-800';
    }
    
    return 'bg-red-100 border-red-200 text-red-800';
  };
  
  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <ShieldCheckIcon className="h-12 w-12 text-green-600 mx-auto mb-2" />
        <h2 className="text-2xl font-bold text-gray-900">Face Verification</h2>
        <p className="text-gray-600">Verify student identity using advanced face recognition</p>
      </div>
      
      {/* Loading State */}
      {loading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading face recognition models...</p>
        </div>
      )}
      
      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4"
          >
            <div className="flex items-center">
              <XCircleIcon className="h-5 w-5 mr-2" />
              {error}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {!loading && (
        <div className="space-y-6">
          {/* Verification Mode Selection */}
          <div className="bg-gray-50 rounded-lg p-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Verification Mode
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="1:1"
                  checked={verificationMode === '1:1'}
                  onChange={(e) => setVerificationMode(e.target.value)}
                  className="mr-2"
                />
                <span className="text-sm">1:1 Verification (Select specific student)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="1:N"
                  checked={verificationMode === '1:N'}
                  onChange={(e) => setVerificationMode(e.target.value)}
                  className="mr-2"
                />
                <span className="text-sm">1:N Identification (Identify from all students)</span>
              </label>
            </div>
          </div>
          
          {/* Student Selection (only for 1:1 mode) */}
          {verificationMode === '1:1' && (
            <div className="bg-gray-50 rounded-lg p-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Student to Verify
              </label>
              <select
                value={selectedStudentId}
                onChange={(e) => setSelectedStudentId(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {KNOWN_STUDENTS.map(student => (
                  <option key={student.student_id} value={student.student_id}>
                    {student.name} ({student.student_id}) - {student.email}
                  </option>
                ))}
              </select>
            </div>
          )}
          
          {/* Camera and Verification Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Live Camera */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Live Camera</h3>
              <div className="relative">
                <Webcam
                  ref={webcamRef}
                  audio={false}
                  screenshotFormat="image/jpeg"
                  className="w-full rounded-lg border-4 border-green-200"
                  width={400}
                  height={300}
                />
                <div className="absolute inset-0 border-2 border-dashed border-green-400 rounded-lg pointer-events-none">
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 border-2 border-green-500 rounded-full opacity-50"></div>
                </div>
              </div>
              
              <button
                onClick={capturePhoto}
                disabled={verifying}
                className="w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
              >
                <CameraIcon className="h-5 w-5 mr-2" />
                Capture Photo
              </button>
            </div>
            
            {/* Captured Photo and Actions */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Verification Photo</h3>
              <div className="w-full h-[300px] bg-gray-100 rounded-lg border-4 border-gray-200 flex items-center justify-center">
                {capturedPhoto ? (
                  <div className="relative">
                    <img
                      src={capturedPhoto}
                      alt="Verification face"
                      className="max-w-full max-h-full rounded-lg"
                    />
                    {qualityCheck && (
                      <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs font-medium ${
                        qualityCheck.valid 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        Quality: {(qualityCheck.score * 100).toFixed(1)}%
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-gray-500">
                    <CameraIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No photo captured</p>
                  </div>
                )}
              </div>
              
              {capturedPhoto && (
                <div className="space-y-3">
                  <button
                    onClick={retakePhoto}
                    disabled={verifying}
                    className="w-full bg-gray-600 text-white px-4 py-3 rounded-lg hover:bg-gray-700 disabled:opacity-50 flex items-center justify-center"
                  >
                    <ArrowPathIcon className="h-5 w-5 mr-2" />
                    Retake Photo
                  </button>
                  
                  {verificationMode === '1:1' ? (
                    <button
                      onClick={handleVerify}
                      disabled={verifying || (qualityCheck && !qualityCheck.valid)}
                      className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
                    >
                      {verifying ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Verifying...
                        </>
                      ) : (
                        <>
                          <ShieldCheckIcon className="h-5 w-5 mr-2" />
                          Verify Identity
                        </>
                      )}
                    </button>
                  ) : (
                    <button
                      onClick={handleIdentify}
                      disabled={verifying || (qualityCheck && !qualityCheck.valid)}
                      className="w-full bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center justify-center"
                    >
                      {verifying ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Identifying...
                        </>
                      ) : (
                        <>
                          <IdentificationIcon className="h-5 w-5 mr-2" />
                          Identify Student
                        </>
                      )}
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
          
          {/* Verification Results */}
          <AnimatePresence>
            {result && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0 }}
                className={`p-6 rounded-lg border ${getResultColor()}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    {result.isMatch || result.identified ? (
                      <CheckCircleIcon className="h-8 w-8 mr-3 flex-shrink-0" />
                    ) : (
                      <XCircleIcon className="h-8 w-8 mr-3 flex-shrink-0" />
                    )}
                    <div>
                      <h4 className="text-lg font-bold">
                        {result.mode === '1:1' ? 'Verification Result' : 'Identification Result'}
                      </h4>
                      <p className="text-sm opacity-90">{result.message}</p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-2xl font-bold">
                      {result.confidence.toFixed(1)}%
                    </div>
                    <div className="text-xs opacity-75">Confidence</div>
                  </div>
                </div>
                
                <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <div className="font-medium">Student</div>
                    <div>{result.studentName}</div>
                  </div>
                  <div>
                    <div className="font-medium">Student ID</div>
                    <div>{result.studentId}</div>
                  </div>
                  <div>
                    <div className="font-medium">Detection Score</div>
                    <div>{(result.detectionScore * 100).toFixed(1)}%</div>
                  </div>
                  <div>
                    <div className="font-medium">Processing Time</div>
                    <div>{result.verificationTimeMs}ms</div>
                  </div>
                </div>
                
                {result.mode === '1:1' && (
                  <div className="mt-2 text-sm opacity-75">
                    Threshold: {result.threshold}% | 
                    Status: {result.isMatch ? '✅ Verified' : '❌ Not Matched'}
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
          
          {/* Quality Check Results */}
          {qualityCheck && (
            <div className={`p-4 rounded-lg border ${
              qualityCheck.valid 
                ? 'bg-green-50 border-green-200 text-green-800' 
                : 'bg-yellow-50 border-yellow-200 text-yellow-800'
            }`}>
              <div className="flex items-center">
                {qualityCheck.valid ? (
                  <CheckCircleIcon className="h-5 w-5 mr-2" />
                ) : (
                  <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
                )}
                <div>
                  <p className="font-medium">Image Quality Check</p>
                  <p className="text-sm">
                    {qualityCheck.reason} | 
                    Detection Score: {(qualityCheck.detectionScore * 100).toFixed(1)}% | 
                    Face Ratio: {(qualityCheck.faceRatio * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          )}
          
          {/* System Status */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">System Status</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  isModelLoaded ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                Models: {isModelLoaded ? 'Loaded' : 'Not Loaded'}
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                Mode: {verificationMode} {verificationMode === '1:1' ? 'Verification' : 'Identification'}
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                Students: {KNOWN_STUDENTS.length} Available
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
                Environment: {import.meta.env.VITE_USE_MOCK_SUPABASE === 'true' ? 'Mock' : 'Production'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
