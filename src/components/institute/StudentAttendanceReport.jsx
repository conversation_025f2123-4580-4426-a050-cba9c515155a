import { useState, useEffect, useRef } from 'react';
import { supabase } from '../../utils/supabaseClient';
import { motion, AnimatePresence } from 'framer-motion';
import { format, parseISO, subDays } from 'date-fns';
import { CSVLink } from 'react-csv';
import { getStudentAttendanceSummary } from '../../utils/attendanceService';

/**
 * StudentAttendanceReport Component
 * Displays attendance reports for students
 */
export default function StudentAttendanceReport() {
  // State for filters
  const [dateRange, setDateRange] = useState({
    startDate: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
    endDate: format(new Date(), 'yyyy-MM-dd')
  });
  const [classFilter, setClassFilter] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  
  // State for data
  const [attendanceSummary, setAttendanceSummary] = useState([]);
  const [filteredSummary, setFilteredSummary] = useState([]);
  const [classes, setClasses] = useState([]);
  
  // State for UI
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // Refs
  const csvLink = useRef();
  
  // Fetch classes on component mount
  useEffect(() => {
    const fetchClasses = async () => {
      try {
        const { data, error } = await supabase
          .from('classes')
          .select('id, name')
          .order('name');
        
        if (error) throw error;
        
        setClasses(data || []);
      } catch (err) {
        console.error('Error fetching classes:', err);
        setError('Failed to load classes. Please try again.');
      }
    };
    
    fetchClasses();
  }, []);
  
  // Fetch attendance summary when filters change
  useEffect(() => {
    fetchAttendanceSummary();
  }, [dateRange, classFilter]);
  
  // Apply search filter when search query changes
  useEffect(() => {
    applySearchFilter();
  }, [searchQuery, attendanceSummary]);
  
  // Fetch attendance summary
  const fetchAttendanceSummary = async () => {
    try {
      setLoading(true);
      
      const data = await getStudentAttendanceSummary({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        classId: classFilter || undefined
      });
      
      setAttendanceSummary(data || []);
      setFilteredSummary(data || []);
    } catch (err) {
      console.error('Error fetching attendance summary:', err);
      setError('Failed to load attendance summary. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Apply search filter
  const applySearchFilter = () => {
    if (!searchQuery) {
      setFilteredSummary(attendanceSummary);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const filtered = attendanceSummary.filter(student => 
      student.student_name.toLowerCase().includes(query) ||
      student.student_id_text.toLowerCase().includes(query)
    );
    
    setFilteredSummary(filtered);
  };
  
  // Handle date range change
  const handleDateRangeChange = (e) => {
    const { name, value } = e.target;
    setDateRange(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle class filter change
  const handleClassFilterChange = (e) => {
    setClassFilter(e.target.value);
  };
  
  // Handle search query change
  const handleSearchQueryChange = (e) => {
    setSearchQuery(e.target.value);
  };
  
  // Handle filter change
  const handleFilterChange = () => {
    fetchAttendanceSummary();
  };
  
  // Prepare CSV data for export
  const prepareCSVData = () => {
    return filteredSummary.map(student => ({
      'Student ID': student.student_id_text,
      'Student Name': student.student_name,
      'Total Days': student.total_days,
      'Present Days': student.present_days,
      'Absent Days': student.absent_days,
      'Attendance Rate (%)': student.attendance_rate.toFixed(2)
    }));
  };
  
  // Get attendance rate color
  const getAttendanceRateColor = (rate) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 75) return 'text-green-500';
    if (rate >= 60) return 'text-yellow-500';
    if (rate >= 40) return 'text-orange-500';
    return 'text-red-600';
  };
  
  // Get attendance rate background color
  const getAttendanceRateBgColor = (rate) => {
    if (rate >= 90) return 'bg-green-600';
    if (rate >= 75) return 'bg-green-500';
    if (rate >= 60) return 'bg-yellow-500';
    if (rate >= 40) return 'bg-orange-500';
    return 'bg-red-600';
  };
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Student Attendance Report</h1>
      
      {/* Error and Success Messages */}
      <AnimatePresence>
        {error && (
          <motion.div 
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
          >
            <span className="font-bold">Error:</span> {error}
            <button 
              className="float-right"
              onClick={() => setError(null)}
            >
              &times;
            </button>
          </motion.div>
        )}
        
        {success && (
          <motion.div 
            className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
          >
            <span className="font-bold">Success:</span> {success}
            <button 
              className="float-right"
              onClick={() => setSuccess(null)}
            >
              &times;
            </button>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Filters</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              name="startDate"
              value={dateRange.startDate}
              onChange={handleDateRangeChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              name="endDate"
              value={dateRange.endDate}
              onChange={handleDateRangeChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Class
            </label>
            <select
              value={classFilter}
              onChange={handleClassFilterChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">All Classes</option>
              {classes.map(cls => (
                <option key={cls.id} value={cls.id}>
                  {cls.name}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row md:items-end gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search Student
            </label>
            <input
              type="text"
              placeholder="Search by name or ID..."
              value={searchQuery}
              onChange={handleSearchQueryChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={handleFilterChange}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Apply Filters
            </button>
            
            <CSVLink
              data={prepareCSVData()}
              filename={`student_attendance_report_${dateRange.startDate}_to_${dateRange.endDate}.csv`}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 inline-block"
              ref={csvLink}
            >
              Export CSV
            </CSVLink>
          </div>
        </div>
      </div>
      
      {/* Attendance Summary */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Student Attendance Summary</h2>
        
        {loading ? (
          <div className="text-center py-8">
            <svg className="animate-spin h-8 w-8 mx-auto text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="mt-2 text-gray-500">Loading attendance summary...</p>
          </div>
        ) : filteredSummary.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No attendance records found for the selected filters.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Days
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Present
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Absent
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Attendance Rate
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredSummary.map(student => (
                  <tr key={student.student_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {student.student_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        ID: {student.student_id_text}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {student.total_days}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-green-600">
                        {student.present_days}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-red-600">
                        {student.absent_days}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2.5 mr-2">
                          <div 
                            className={`h-2.5 rounded-full ${getAttendanceRateBgColor(student.attendance_rate)}`}
                            style={{ width: `${Math.min(100, student.attendance_rate)}%` }}
                          ></div>
                        </div>
                        <span className={`text-sm font-medium ${getAttendanceRateColor(student.attendance_rate)}`}>
                          {student.attendance_rate.toFixed(2)}%
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
