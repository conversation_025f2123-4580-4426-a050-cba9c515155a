import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { format, subDays } from 'date-fns';

/**
 * InstituteAttendanceLogsWrapper Component
 * A wrapper component that handles errors in the InstituteAttendanceLogs component
 */
export default function InstituteAttendanceLogsWrapper() {
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'calendar'
  const [showBulkAttendanceModal, setShowBulkAttendanceModal] = useState(false);
  
  // Mock data
  const mockStudents = [
    { id: '1', first_name: '<PERSON>', last_name: '<PERSON><PERSON>', student_id: 'S1001', reference_image_url: 'https://randomuser.me/api/portraits/men/1.jpg' },
    { id: '2', first_name: '<PERSON>', last_name: '<PERSON>', student_id: 'S1002', reference_image_url: 'https://randomuser.me/api/portraits/women/1.jpg' },
    { id: '3', first_name: '<PERSON>', last_name: '<PERSON>', student_id: 'S1003', reference_image_url: 'https://randomuser.me/api/portraits/men/2.jpg' }
  ];
  
  const mockClasses = [
    { id: '1', name: 'Computer Science 101' },
    { id: '2', name: 'Mathematics 202' }
  ];
  
  const mockAttendance = [
    { 
      id: '1', 
      student_id: '1', 
      class_id: '1', 
      date: '2023-12-01', 
      status: true, 
      verification_method: 'face', 
      created_at: '2023-12-01T09:00:00Z',
      students: mockStudents[0],
      classes: mockClasses[0]
    },
    { 
      id: '2', 
      student_id: '2', 
      class_id: '1', 
      date: '2023-12-01', 
      status: true, 
      verification_method: 'face', 
      created_at: '2023-12-01T09:05:00Z',
      students: mockStudents[1],
      classes: mockClasses[0]
    },
    { 
      id: '3', 
      student_id: '3', 
      class_id: '2', 
      date: '2023-12-01', 
      status: false, 
      verification_method: 'manual', 
      created_at: '2023-12-01T09:10:00Z',
      students: mockStudents[2],
      classes: mockClasses[1]
    }
  ];
  
  // Format date for display
  const formatDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Attendance Logs</h1>
      
      {/* Error and Success Messages */}
      <AnimatePresence>
        {error && (
          <motion.div 
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
          >
            <span className="font-bold">Error:</span> {error}
            <button 
              className="float-right"
              onClick={() => setError(null)}
            >
              &times;
            </button>
          </motion.div>
        )}
        
        {success && (
          <motion.div 
            className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
          >
            <span className="font-bold">Success:</span> {success}
            <button 
              className="float-right"
              onClick={() => setSuccess(null)}
            >
              &times;
            </button>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Filters</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              name="startDate"
              defaultValue={format(subDays(new Date(), 7), 'yyyy-MM-dd')}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              name="endDate"
              defaultValue={format(new Date(), 'yyyy-MM-dd')}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Class
            </label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">All Classes</option>
              {mockClasses.map(cls => (
                <option key={cls.id} value={cls.id}>
                  {cls.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="all">All</option>
              <option value="present">Present</option>
              <option value="absent">Absent</option>
            </select>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row md:items-end gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search Student
            </label>
            <input
              type="text"
              placeholder="Search by name or ID..."
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div className="flex gap-2">
            <button
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Apply Filters
            </button>
            
            <button
              onClick={() => setShowBulkAttendanceModal(true)}
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
              Bulk Attendance
            </button>
            
            <button
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            >
              Export CSV
            </button>
          </div>
        </div>
      </div>
      
      {/* Statistics */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Attendance Statistics</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gray-100 rounded-md p-4 text-center">
            <p className="text-2xl font-bold text-gray-800">3</p>
            <p className="text-sm text-gray-600">Total Records</p>
          </div>
          
          <div className="bg-green-100 rounded-md p-4 text-center">
            <p className="text-2xl font-bold text-green-800">2</p>
            <p className="text-sm text-green-600">Present</p>
          </div>
          
          <div className="bg-red-100 rounded-md p-4 text-center">
            <p className="text-2xl font-bold text-red-800">1</p>
            <p className="text-sm text-red-600">Absent</p>
          </div>
          
          <div className="bg-blue-100 rounded-md p-4 text-center">
            <p className="text-2xl font-bold text-blue-800">66.67%</p>
            <p className="text-sm text-blue-600">Attendance Rate</p>
          </div>
        </div>
        
        {/* Attendance Chart */}
        <div className="mt-6">
          <div className="w-full h-6 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-green-500"
              style={{ width: '66.67%' }}
            ></div>
          </div>
          <div className="flex justify-between mt-1 text-xs text-gray-500">
            <span>0%</span>
            <span>25%</span>
            <span>50%</span>
            <span>75%</span>
            <span>100%</span>
          </div>
        </div>
      </div>
      
      {/* View Mode Toggle */}
      <div className="flex justify-end mb-4">
        <div className="inline-flex rounded-md shadow-sm" role="group">
          <button
            type="button"
            onClick={() => setViewMode('list')}
            className={`px-4 py-2 text-sm font-medium rounded-l-lg ${
              viewMode === 'list'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            List View
          </button>
          <button
            type="button"
            onClick={() => setViewMode('calendar')}
            className={`px-4 py-2 text-sm font-medium rounded-r-lg ${
              viewMode === 'calendar'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            Calendar View
          </button>
        </div>
      </div>
      
      {/* Attendance Records */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Attendance Records</h2>
        
        {viewMode === 'list' ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Class
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Verification
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {mockAttendance.map(record => (
                  <tr key={record.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(record.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <img 
                            className="h-10 w-10 rounded-full object-cover" 
                            src={record.students.reference_image_url} 
                            alt={`${record.students.first_name} ${record.students.last_name}`} 
                          />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {record.students.first_name} {record.students.last_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {record.students.student_id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.classes.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        record.status 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {record.status ? 'Present' : 'Absent'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.verification_method}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          className={`text-xs px-2 py-1 rounded ${
                            record.status 
                              ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                              : 'bg-green-100 text-green-700 hover:bg-green-200'
                          }`}
                        >
                          Mark as {record.status ? 'Absent' : 'Present'}
                        </button>
                        <button
                          className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-gray-100 px-4 py-2 flex justify-between items-center">
                <h3 className="font-medium">December 1, 2023</h3>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-green-500 mr-1"></div>
                  <span className="text-xs text-gray-600">67%</span>
                </div>
              </div>
              <div className="p-4">
                <div className="text-sm text-gray-500 mb-2">
                  3 records (2 present, 1 absent)
                </div>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {mockAttendance.map(record => (
                    <div key={record.id} className="flex items-center p-2 border-b">
                      <div className="flex-shrink-0 h-8 w-8">
                        <img 
                          className="h-8 w-8 rounded-full object-cover" 
                          src={record.students.reference_image_url} 
                          alt={`${record.students.first_name} ${record.students.last_name}`} 
                        />
                      </div>
                      <div className="ml-2 flex-1">
                        <div className="text-sm font-medium">{record.students.first_name} {record.students.last_name}</div>
                        <div className="text-xs text-gray-500">{format(new Date(record.created_at), 'HH:mm')}</div>
                      </div>
                      <div>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          record.status 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {record.status ? 'Present' : 'Absent'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
