import { useState, useEffect } from 'react';
import { supabase } from '../../utils/supabaseClient';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';
import { bulkCreateAttendanceLogs, getDeviceInfo } from '../../utils/attendanceService';

/**
 * BulkAttendanceMarking Component
 * Allows marking attendance for multiple students at once
 */
export default function BulkAttendanceMarking({ onClose, onSuccess }) {
  // State for form data
  const [formData, setFormData] = useState({
    classId: '',
    date: format(new Date(), 'yyyy-MM-dd'),
    verificationMethod: 'manual',
    notes: ''
  });
  
  // State for students
  const [students, setStudents] = useState([]);
  const [selectedStudents, setSelectedStudents] = useState({});
  const [selectAll, setSelectAll] = useState(false);
  
  // State for classes
  const [classes, setClasses] = useState([]);
  
  // State for UI
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // Fetch classes and students on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch classes
        const { data: classesData, error: classesError } = await supabase
          .from('classes')
          .select('id, name')
          .order('name');
        
        if (classesError) throw classesError;
        setClasses(classesData || []);
      } catch (err) {
        console.error('Error fetching classes:', err);
        setError('Failed to load classes. Please try again.');
      }
    };
    
    fetchData();
  }, []);
  
  // Fetch students when class changes
  useEffect(() => {
    const fetchStudents = async () => {
      if (!formData.classId) {
        setStudents([]);
        setSelectedStudents({});
        return;
      }
      
      try {
        setLoading(true);
        
        // Fetch students in the selected class
        const { data, error } = await supabase
          .from('student_classes')
          .select(`
            student_id,
            students (
              id,
              first_name,
              last_name,
              student_id,
              reference_image_url
            )
          `)
          .eq('class_id', formData.classId);
        
        if (error) throw error;
        
        // Extract student data
        const studentData = data
          .map(item => item.students)
          .filter(Boolean)
          .sort((a, b) => a.last_name.localeCompare(b.last_name));
        
        setStudents(studentData);
        
        // Reset selected students
        const initialSelectedState = {};
        studentData.forEach(student => {
          initialSelectedState[student.id] = false;
        });
        
        setSelectedStudents(initialSelectedState);
        setSelectAll(false);
      } catch (err) {
        console.error('Error fetching students:', err);
        setError('Failed to load students. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchStudents();
  }, [formData.classId]);
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle student selection
  const handleStudentSelection = (studentId) => {
    setSelectedStudents(prev => ({
      ...prev,
      [studentId]: !prev[studentId]
    }));
    
    // Update selectAll state
    const updatedSelectedStudents = {
      ...selectedStudents,
      [studentId]: !selectedStudents[studentId]
    };
    
    const allSelected = students.every(student => updatedSelectedStudents[student.id]);
    setSelectAll(allSelected);
  };
  
  // Handle select all
  const handleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    
    const updatedSelectedStudents = {};
    students.forEach(student => {
      updatedSelectedStudents[student.id] = newSelectAll;
    });
    
    setSelectedStudents(updatedSelectedStudents);
  };
  
  // Handle form submission
  const handleSubmit = async (e, status) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.classId) {
      setError('Please select a class.');
      return;
    }
    
    if (!formData.date) {
      setError('Please select a date.');
      return;
    }
    
    // Get selected students
    const selectedStudentIds = Object.entries(selectedStudents)
      .filter(([_, isSelected]) => isSelected)
      .map(([studentId]) => studentId);
    
    if (selectedStudentIds.length === 0) {
      setError('Please select at least one student.');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Get device info
      const deviceInfo = getDeviceInfo();
      
      // Prepare attendance logs
      const attendanceLogs = selectedStudentIds.map(studentId => ({
        studentId,
        classId: formData.classId,
        date: formData.date,
        status,
        verificationMethod: formData.verificationMethod,
        notes: formData.notes,
        deviceInfo
      }));
      
      // Create attendance logs
      await bulkCreateAttendanceLogs(attendanceLogs);
      
      // Show success message
      setSuccess(`Attendance marked successfully for ${selectedStudentIds.length} students.`);
      
      // Call onSuccess callback
      if (onSuccess) {
        onSuccess();
      }
      
      // Close modal after a delay
      setTimeout(() => {
        if (onClose) {
          onClose();
        }
      }, 2000);
    } catch (err) {
      console.error('Error marking attendance:', err);
      setError('Failed to mark attendance. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Bulk Attendance Marking</h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Error and Success Messages */}
        <AnimatePresence>
          {error && (
            <motion.div 
              className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0 }}
            >
              <span className="font-bold">Error:</span> {error}
              <button 
                className="float-right"
                onClick={() => setError(null)}
              >
                &times;
              </button>
            </motion.div>
          )}
          
          {success && (
            <motion.div 
              className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0 }}
            >
              <span className="font-bold">Success:</span> {success}
              <button 
                className="float-right"
                onClick={() => setSuccess(null)}
              >
                &times;
              </button>
            </motion.div>
          )}
        </AnimatePresence>
        
        <form>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Class <span className="text-red-500">*</span>
              </label>
              <select
                name="classId"
                value={formData.classId}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              >
                <option value="">-- Select Class --</option>
                {classes.map(cls => (
                  <option key={cls.id} value={cls.id}>
                    {cls.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Verification Method
              </label>
              <select
                name="verificationMethod"
                value={formData.verificationMethod}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="manual">Manual</option>
                <option value="face">Face Recognition</option>
                <option value="qr">QR Code</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <input
                type="text"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                placeholder="Optional notes about this attendance"
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>
          
          {/* Student Selection */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium">Select Students</h3>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="selectAll"
                  checked={selectAll}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="selectAll" className="ml-2 text-sm text-gray-700">
                  Select All
                </label>
              </div>
            </div>
            
            {loading ? (
              <div className="text-center py-4">
                <svg className="animate-spin h-8 w-8 mx-auto text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p className="mt-2 text-gray-500">Loading students...</p>
              </div>
            ) : students.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                {formData.classId ? 'No students found in this class.' : 'Please select a class to view students.'}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 max-h-60 overflow-y-auto p-2 border border-gray-200 rounded-md">
                {students.map(student => (
                  <div 
                    key={student.id} 
                    className={`flex items-center p-2 rounded-md cursor-pointer ${
                      selectedStudents[student.id] ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
                    }`}
                    onClick={() => handleStudentSelection(student.id)}
                  >
                    <input
                      type="checkbox"
                      checked={selectedStudents[student.id] || false}
                      onChange={() => handleStudentSelection(student.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div className="flex items-center ml-2">
                      <div className="flex-shrink-0 h-8 w-8">
                        {student.reference_image_url ? (
                          <img 
                            className="h-8 w-8 rounded-full object-cover" 
                            src={student.reference_image_url} 
                            alt={`${student.first_name} ${student.last_name}`} 
                          />
                        ) : (
                          <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                        )}
                      </div>
                      <div className="ml-2">
                        <div className="text-sm font-medium text-gray-900">
                          {student.first_name} {student.last_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          ID: {student.student_id}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={(e) => handleSubmit(e, false)}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400"
              disabled={loading}
            >
              Mark as Absent
            </button>
            <button
              type="button"
              onClick={(e) => handleSubmit(e, true)}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400"
              disabled={loading}
            >
              Mark as Present
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
