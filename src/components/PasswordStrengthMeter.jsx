import { useMemo } from 'react';
import zxcvbn from 'zxcvbn';

/**
 * Password strength meter component
 * Uses zxcvbn to calculate password strength and displays a visual indicator
 * 
 * @param {Object} props Component props
 * @param {string} props.password The password to evaluate
 * @param {boolean} props.showText Whether to show the strength text
 * @param {string} props.className Additional CSS classes
 */
export default function PasswordStrengthMeter({ password, showText = true, className = '' }) {
  // Calculate password strength score (0-4)
  const passwordScore = useMemo(() => {
    if (!password) return 0;
    return zxcvbn(password).score;
  }, [password]);

  // Labels for each strength level
  const strengthLabels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
  
  // Colors for each strength level
  const strengthColors = [
    'bg-red-500',     // Very Weak
    'bg-orange-500',  // Weak
    'bg-yellow-500',  // Fair
    'bg-green-500',   // Good
    'bg-emerald-500'  // Strong
  ];

  // Calculate width percentage based on score
  const strengthPercentage = (passwordScore + 1) * 20;

  return (
    <div className={`w-full ${className}`}>
      {showText && (
        <div className="text-xs text-gray-500 mb-1">
          Password strength: {strengthLabels[passwordScore]}
        </div>
      )}
      <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
        <div 
          className={`h-full ${strengthColors[passwordScore]}`}
          style={{ width: `${strengthPercentage}%` }}
        />
      </div>
    </div>
  );
}
