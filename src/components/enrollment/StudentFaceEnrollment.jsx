import { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Webcam from 'react-webcam';
import { 
  CameraIcon, 
  DocumentArrowUpIcon,
  CheckCircleIcon, 
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  UserPlusIcon,
  PhotoIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { enrollStudentWithFace, enableManualOverride } from '../../utils/studentEnrollmentService';

/**
 * StudentFaceEnrollment Component
 * Complete student enrollment with manual identity input and face capture
 */
export default function StudentFaceEnrollment({ onEnrollmentComplete, onCancel }) {
  const [studentData, setStudentData] = useState({
    studentId: '',
    name: '',
    email: ''
  });
  
  const [captureMode, setCaptureMode] = useState('webcam'); // 'webcam' or 'upload'
  const [capturedPhoto, setCapturedPhoto] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [enrolling, setEnrolling] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [step, setStep] = useState(1); // 1: Form, 2: Capture/Upload, 3: Results
  const [showManualOverride, setShowManualOverride] = useState(false);
  const [overrideReason, setOverrideReason] = useState('');
  
  const webcamRef = useRef(null);
  const fileInputRef = useRef(null);
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setStudentData(prev => ({ ...prev, [name]: value }));
    setError(null);
  };
  
  // Validate form data
  const validateForm = () => {
    if (!studentData.studentId.trim()) {
      setError('Student ID is required (e.g., E22273735500014)');
      return false;
    }
    
    if (!studentData.name.trim()) {
      setError('Student name is required (e.g., Anupam)');
      return false;
    }
    
    if (!studentData.email.trim()) {
      setError('Email is required (e.g., <EMAIL>)');
      return false;
    }
    
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(studentData.email)) {
      setError('Please enter a valid email address');
      return false;
    }
    
    return true;
  };
  
  // Capture photo from webcam
  const capturePhoto = useCallback(() => {
    const imageSrc = webcamRef.current.getScreenshot();
    setCapturedPhoto(imageSrc);
    setUploadedFile(null);
    setError(null);
  }, [webcamRef]);
  
  // Handle file upload
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setUploadedFile(file);
      setCapturedPhoto(null);
      setError(null);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setCapturedPhoto(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Retake photo or clear upload
  const retakePhoto = () => {
    setCapturedPhoto(null);
    setUploadedFile(null);
    setResult(null);
    setError(null);
    setShowManualOverride(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Handle enrollment
  const handleEnrollment = async () => {
    if (!capturedPhoto && !uploadedFile) {
      setError('Please capture a photo or upload an image');
      return;
    }
    
    setEnrolling(true);
    setError(null);
    setResult(null);
    
    try {
      // Use uploaded file or captured photo
      const imageSource = uploadedFile || capturedPhoto;
      
      // Enroll student with face
      const enrollmentResult = await enrollStudentWithFace(studentData, imageSource);
      
      setResult(enrollmentResult);
      
      if (enrollmentResult.success) {
        setStep(3);
        
        // Call completion callback
        if (onEnrollmentComplete) {
          onEnrollmentComplete(enrollmentResult);
        }
      } else {
        setError(enrollmentResult.message);
        
        // Show manual override option for certain failures
        if (['no_face', 'multiple_faces', 'poor_quality'].includes(enrollmentResult.enrollmentStatus)) {
          setShowManualOverride(true);
        }
      }
    } catch (err) {
      console.error('Enrollment error:', err);
      setError(err.message || 'Enrollment failed');
    } finally {
      setEnrolling(false);
    }
  };
  
  // Handle manual override
  const handleManualOverride = async () => {
    if (!overrideReason.trim()) {
      setError('Please provide a reason for manual override');
      return;
    }
    
    setEnrolling(true);
    setError(null);
    
    try {
      const imageUrl = result?.imageUrl || (uploadedFile ? 'manual_override_image' : capturedPhoto);
      const overrideResult = await enableManualOverride(studentData.studentId, imageUrl, overrideReason);
      
      if (overrideResult.success) {
        const successResult = {
          ...result,
          success: true,
          message: 'Student enrolled successfully with manual override. Image uploaded. Data saved to Supabase.',
          enrollmentStatus: 'success'
        };
        
        setResult(successResult);
        setStep(3);
        setShowManualOverride(false);
        
        if (onEnrollmentComplete) {
          onEnrollmentComplete(successResult);
        }
      } else {
        setError(overrideResult.error || 'Manual override failed');
      }
    } catch (err) {
      console.error('Manual override error:', err);
      setError(err.message || 'Manual override failed');
    } finally {
      setEnrolling(false);
    }
  };
  
  // Reset form
  const resetForm = () => {
    setStudentData({ studentId: '', name: '', email: '' });
    setCapturedPhoto(null);
    setUploadedFile(null);
    setResult(null);
    setError(null);
    setStep(1);
    setCaptureMode('webcam');
    setShowManualOverride(false);
    setOverrideReason('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Get result color based on enrollment status
  const getResultColor = () => {
    if (!result) return '';
    
    switch (result.enrollmentStatus) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'no_face':
      case 'multiple_faces':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'poor_quality':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };
  
  // Get result icon
  const getResultIcon = () => {
    if (!result) return null;
    
    switch (result.enrollmentStatus) {
      case 'success':
        return <CheckCircleIcon className="h-8 w-8" />;
      case 'no_face':
      case 'multiple_faces':
        return <XCircleIcon className="h-8 w-8" />;
      case 'poor_quality':
        return <ExclamationTriangleIcon className="h-8 w-8" />;
      default:
        return <XCircleIcon className="h-8 w-8" />;
    }
  };
  
  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <UserPlusIcon className="h-12 w-12 text-blue-600 mx-auto mb-2" />
        <h2 className="text-2xl font-bold text-gray-900">Enroll Student Face</h2>
        <p className="text-gray-600">Manual identity input with face capture verification</p>
      </div>
      
      {/* Progress Indicator */}
      <div className="flex items-center justify-center mb-8">
        {[1, 2, 3].map((stepNumber) => (
          <div key={stepNumber} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step >= stepNumber 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-600'
            }`}>
              {stepNumber}
            </div>
            {stepNumber < 3 && (
              <div className={`w-16 h-1 mx-2 ${
                step > stepNumber ? 'bg-blue-600' : 'bg-gray-200'
              }`} />
            )}
          </div>
        ))}
      </div>
      
      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4"
          >
            <div className="flex items-center">
              <XCircleIcon className="h-5 w-5 mr-2" />
              {error}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Step Content */}
      <AnimatePresence mode="wait">
        {step === 1 && (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Student Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Student ID *
                </label>
                <input
                  type="text"
                  name="studentId"
                  value={studentData.studentId}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., E22273735500014"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Enter the unique student identification number</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={studentData.name}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Anupam"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  name="email"
                  value={studentData.email}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., <EMAIL>"
                  required
                />
              </div>
            </div>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
              <h4 className="font-medium text-blue-900 mb-2">📋 Enrollment Requirements</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Provide accurate student information</li>
                <li>• Ensure exactly one clear, centered face in photo</li>
                <li>• Use good lighting for better face detection</li>
                <li>• Manual override available if face detection fails</li>
              </ul>
            </div>
          </motion.div>
        )}
        
        {step === 2 && (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Capture Student Face</h3>
            
            {/* Capture Mode Selection */}
            <div className="flex justify-center space-x-4 mb-6">
              <button
                onClick={() => setCaptureMode('webcam')}
                className={`px-4 py-2 rounded-lg flex items-center ${
                  captureMode === 'webcam'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <CameraIcon className="h-4 w-4 mr-2" />
                Use Webcam
              </button>
              <button
                onClick={() => setCaptureMode('upload')}
                className={`px-4 py-2 rounded-lg flex items-center ${
                  captureMode === 'upload'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <DocumentArrowUpIcon className="h-4 w-4 mr-2" />
                Upload Photo
              </button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Capture/Upload Section */}
              <div className="space-y-4">
                {captureMode === 'webcam' ? (
                  <>
                    <h4 className="font-medium text-gray-900">Live Camera</h4>
                    <div className="relative">
                      <Webcam
                        ref={webcamRef}
                        audio={false}
                        screenshotFormat="image/jpeg"
                        className="w-full rounded-lg border-4 border-blue-200"
                        width={400}
                        height={300}
                      />
                      <div className="absolute inset-0 border-2 border-dashed border-blue-400 rounded-lg pointer-events-none">
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 border-2 border-blue-500 rounded-full opacity-50"></div>
                      </div>
                    </div>
                    <button
                      onClick={capturePhoto}
                      disabled={enrolling}
                      className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
                    >
                      <CameraIcon className="h-5 w-5 mr-2" />
                      Capture Photo
                    </button>
                  </>
                ) : (
                  <>
                    <h4 className="font-medium text-gray-900">Upload Photo</h4>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                      <PhotoIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-sm text-gray-500 mb-4">
                        PNG, JPG, JPEG, WebP up to 5MB
                      </p>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        disabled={enrolling}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                      >
                        Choose File
                      </button>
                    </div>
                  </>
                )}
              </div>
              
              {/* Preview Section */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Preview</h4>
                <div className="w-full h-[300px] bg-gray-100 rounded-lg border-4 border-gray-200 flex items-center justify-center">
                  {capturedPhoto ? (
                    <img
                      src={capturedPhoto}
                      alt="Student face"
                      className="max-w-full max-h-full rounded-lg"
                    />
                  ) : (
                    <div className="text-center text-gray-500">
                      <PhotoIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>No image captured</p>
                    </div>
                  )}
                </div>
                
                {capturedPhoto && (
                  <div className="flex space-x-3">
                    <button
                      onClick={retakePhoto}
                      disabled={enrolling}
                      className="flex-1 bg-gray-600 text-white px-4 py-3 rounded-lg hover:bg-gray-700 disabled:opacity-50 flex items-center justify-center"
                    >
                      <ArrowPathIcon className="h-5 w-5 mr-2" />
                      Retake
                    </button>
                    
                    <button
                      onClick={handleEnrollment}
                      disabled={enrolling}
                      className="flex-1 bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
                    >
                      {enrolling ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Enrolling...
                        </>
                      ) : (
                        <>
                          <UserPlusIcon className="h-5 w-5 mr-2" />
                          Enroll Student
                        </>
                      )}
                    </button>
                  </div>
                )}
              </div>
            </div>
            
            {/* Manual Override Section */}
            <AnimatePresence>
              {showManualOverride && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="bg-yellow-50 border border-yellow-200 rounded-lg p-4"
                >
                  <div className="flex items-center mb-3">
                    <ShieldCheckIcon className="h-5 w-5 text-yellow-600 mr-2" />
                    <h4 className="font-medium text-yellow-900">Manual Override Available</h4>
                  </div>
                  <p className="text-sm text-yellow-800 mb-3">
                    Face detection failed, but you can manually override this decision if you're confident the image is valid.
                  </p>
                  <div className="space-y-3">
                    <textarea
                      value={overrideReason}
                      onChange={(e) => setOverrideReason(e.target.value)}
                      placeholder="Provide a reason for manual override (e.g., 'Image quality is acceptable despite low detection score')"
                      className="w-full p-3 border border-yellow-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                      rows={3}
                    />
                    <button
                      onClick={handleManualOverride}
                      disabled={enrolling || !overrideReason.trim()}
                      className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 disabled:opacity-50 flex items-center"
                    >
                      {enrolling ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Applying Override...
                        </>
                      ) : (
                        <>
                          <ShieldCheckIcon className="h-4 w-4 mr-2" />
                          Apply Manual Override
                        </>
                      )}
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        )}
        
        {step === 3 && result && (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="text-center"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-6">Enrollment Results</h3>
            
            <div className={`p-6 rounded-lg border ${getResultColor()} mb-6`}>
              <div className="flex items-center justify-center mb-4">
                {getResultIcon()}
              </div>
              
              <h4 className="text-xl font-bold mb-2">{result.message}</h4>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 text-sm">
                <div>
                  <div className="font-medium">Student ID</div>
                  <div>{result.studentId}</div>
                </div>
                <div>
                  <div className="font-medium">Name</div>
                  <div>{result.name}</div>
                </div>
                <div>
                  <div className="font-medium">Faces Detected</div>
                  <div>{result.facesDetected}</div>
                </div>
                <div>
                  <div className="font-medium">Processing Time</div>
                  <div>{result.processingTimeMs}ms</div>
                </div>
              </div>
              
              {result.success && (
                <div className="mt-4 text-sm">
                  <p>✅ Image saved to: supabase.storage.from('student_faces')</p>
                  <p>✅ Student data saved to Supabase students table</p>
                </div>
              )}
            </div>
            
            <div className="flex space-x-4 justify-center">
              <button
                onClick={resetForm}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center"
              >
                <UserPlusIcon className="h-5 w-5 mr-2" />
                Enroll Another Student
              </button>
              
              {onCancel && (
                <button
                  onClick={onCancel}
                  className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700"
                >
                  Back to Dashboard
                </button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Navigation Buttons */}
      {step < 3 && (
        <div className="flex justify-between mt-8">
          <div>
            {step > 1 && (
              <button
                onClick={() => setStep(step - 1)}
                disabled={enrolling}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
              >
                ← Previous
              </button>
            )}
            
            {onCancel && step === 1 && (
              <button
                onClick={onCancel}
                disabled={enrolling}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
              >
                Cancel
              </button>
            )}
          </div>
          
          <div>
            {step === 1 && (
              <button
                onClick={() => {
                  if (validateForm()) {
                    setStep(2);
                  }
                }}
                disabled={enrolling}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                Next →
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
