import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useState } from 'react';
import RoleIndicator from './RoleIndicator';

export default function Header() {
  const { userProfile, logout, currentUser, isAdmin, isInstitute, isStudent, userRole } = useAuth();
  const [showMenu, setShowMenu] = useState(false);
  const location = useLocation();

  // Check which dashboard the user is on
  const isOnAdminPage = location.pathname.startsWith('/admin');
  const isOnInstitutePage = location.pathname.startsWith('/institute');
  const isOnStudentPage = location.pathname.startsWith('/student');

  return (
    <header className="bg-white shadow-sm py-4 px-6 flex justify-between items-center">
      <div>
        <Link to="/dashboard" className="text-2xl font-inter font-semibold text-brand">
          Examino
        </Link>
        <div className="flex items-center gap-2">
          <h2 className="text-sm text-gray-600">
            Welcome, {userProfile?.first_name || 'User'}
          </h2>
          <RoleIndicator />
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative">
          <button
            onClick={() => setShowMenu(!showMenu)}
            className="flex items-center gap-2 hover:text-brand transition-colors"
          >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-label="Profile"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
          />
        </svg>
        <span className="md:inline hidden">Profile</span>
          </button>

          {showMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
              <Link
                to="/profile"
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                onClick={() => setShowMenu(false)}
              >
                Profile Settings
              </Link>
              <Link
                to="/"
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                onClick={() => setShowMenu(false)}
              >
                Landing Page
              </Link>
              {/* Role-specific dashboard links */}
              {isAdmin && (
                <Link
                  to={isOnAdminPage ? "/dashboard" : "/admin"}
                  className="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                  onClick={() => setShowMenu(false)}
                >
                  {isOnAdminPage ? "Main Dashboard" : "Admin Dashboard"}
                </Link>
              )}
              {isInstitute && (
                <Link
                  to={isOnInstitutePage ? "/dashboard" : "/institute"}
                  className="block px-4 py-2 text-sm text-purple-600 hover:bg-gray-100"
                  onClick={() => setShowMenu(false)}
                >
                  {isOnInstitutePage ? "Main Dashboard" : "Institute Dashboard"}
                </Link>
              )}
              {isStudent && (
                <Link
                  to={isOnStudentPage ? "/dashboard" : "/student"}
                  className="block px-4 py-2 text-sm text-blue-600 hover:bg-gray-100"
                  onClick={() => setShowMenu(false)}
                >
                  {isOnStudentPage ? "Main Dashboard" : "Student Dashboard"}
                </Link>
              )}
              <button
                onClick={() => {
                  logout();
                  setShowMenu(false);
                }}
                className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
              >
                Sign Out
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
