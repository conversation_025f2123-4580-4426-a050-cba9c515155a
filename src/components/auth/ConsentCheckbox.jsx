import { useState } from 'react';
import { motion } from 'framer-motion';
import { CheckIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';

export default function ConsentCheckbox({ onSubmit, termsLink = '/privacy', required = true }) {
  const [isChecked, setIsChecked] = useState(false);
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.3,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (required && !isChecked) {
      setError('You must agree to the terms and conditions to continue');
      return;
    }
    
    setError('');
    setIsSubmitting(true);
    
    try {
      await onSubmit({ consent: isChecked });
    } catch (error) {
      console.error('Consent submission error:', error);
      setError(error.message || 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.h2 variants={itemVariants} className="text-xl font-semibold text-gray-800 mb-4 text-center">
        Terms & Conditions
      </motion.h2>

      <motion.div variants={itemVariants} className="bg-gray-50 p-6 rounded-lg mb-6 max-h-60 overflow-y-auto">
        <h3 className="font-semibold text-gray-800 mb-2">Privacy Policy and Terms of Use</h3>
        
        <p className="text-gray-700 mb-4">
          By creating an account, you agree to the following terms regarding face recognition data:
        </p>
        
        <ol className="list-decimal list-inside text-gray-700 space-y-2">
          <li>
            <strong>Data Collection:</strong> We collect and store your facial biometric data for the sole purpose of attendance verification.
          </li>
          <li>
            <strong>Data Security:</strong> Your facial data is encrypted using AES-256 encryption and stored securely in our database.
          </li>
          <li>
            <strong>Data Usage:</strong> Your facial data will only be used for:
            <ul className="list-disc list-inside ml-6 mt-1">
              <li>Verifying your identity during attendance marking</li>
              <li>Preventing attendance fraud</li>
            </ul>
          </li>
          <li>
            <strong>Data Sharing:</strong> We do not share your facial data with any third parties except as required by law.
          </li>
          <li>
            <strong>Data Retention:</strong> Your facial data will be retained for the duration of your account and will be deleted within 30 days of account closure.
          </li>
          <li>
            <strong>Your Rights:</strong> You have the right to:
            <ul className="list-disc list-inside ml-6 mt-1">
              <li>Access your facial data</li>
              <li>Request deletion of your facial data</li>
              <li>Opt out of facial recognition (alternative authentication methods will be provided)</li>
            </ul>
          </li>
        </ol>
      </motion.div>

      {error && (
        <motion.div variants={itemVariants} className="mb-4 bg-red-100 text-red-700 p-3 rounded-md flex items-start">
          <ExclamationCircleIcon className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
          <span>{error}</span>
        </motion.div>
      )}

      <motion.div variants={itemVariants} className="mb-6">
        <label className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="consent"
              name="consent"
              type="checkbox"
              checked={isChecked}
              onChange={() => setIsChecked(!isChecked)}
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
            />
          </div>
          <div className="ml-3 text-sm">
            <span className="text-gray-700">
              I agree to the{' '}
              <a 
                href={termsLink} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                Privacy Policy and Terms of Use
              </a>
              {required && <span className="text-red-500">*</span>}
            </span>
            <p className="text-gray-500 mt-1">
              By checking this box, you consent to the collection, storage, and use of your facial biometric data for attendance verification purposes.
            </p>
          </div>
        </label>
      </motion.div>

      <motion.div variants={itemVariants}>
        <button
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting || (required && !isChecked)}
          className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
            isSubmitting || (required && !isChecked)
              ? 'bg-blue-400 cursor-not-allowed' 
              : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          }`}
        >
          {isSubmitting ? 'Processing...' : 'I Agree & Continue'}
        </button>
      </motion.div>
    </motion.div>
  );
}
