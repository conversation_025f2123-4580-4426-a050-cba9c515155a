import { useState } from 'react';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import zxcvbn from 'zxcvbn';

export default function EmailPasswordForm({ onSubmit, initialValues = {}, errors = {}, setFieldValue }) {
  const [showPassword, setShowPassword] = useState(false);
  const [passwordScore, setPasswordScore] = useState(0);
  const [formValues, setFormValues] = useState({
    email: initialValues.email || '',
    password: initialValues.password || '',
    firstName: initialValues.firstName || '',
    lastName: initialValues.lastName || '',
    ...initialValues
  });
  const [formErrors, setFormErrors] = useState(errors);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Animation variants
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  };

  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Update form values
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }));
    
    // If using Formik's setFieldValue
    if (setFieldValue) {
      setFieldValue(name, value);
    }
    
    // Clear error when field is edited
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Check password strength
    if (name === 'password') {
      const result = zxcvbn(value);
      setPasswordScore(result.score);
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    // Email validation
    if (!formValues.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formValues.email)) {
      newErrors.email = 'Invalid email address';
    }
    
    // Password validation
    if (!formValues.password) {
      newErrors.password = 'Password is required';
    } else if (formValues.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    } else if (!/\d/.test(formValues.password)) {
      newErrors.password = 'Password must contain at least one number';
    } else if (!/[!@#$%^&*(),.?":{}|<>]/.test(formValues.password)) {
      newErrors.password = 'Password must contain at least one special character';
    } else if (passwordScore < 3) {
      newErrors.password = 'Password is too weak';
    }
    
    // First name validation
    if (!formValues.firstName) {
      newErrors.firstName = 'First name is required';
    }
    
    // Last name validation
    if (!formValues.lastName) {
      newErrors.lastName = 'Last name is required';
    }
    
    setFormErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await onSubmit(formValues);
    } catch (error) {
      console.error('Form submission error:', error);
      setFormErrors(prev => ({
        ...prev,
        form: error.message || 'An error occurred during submission'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get password strength color
  const getPasswordStrengthColor = () => {
    if (passwordScore === 0) return 'bg-red-500';
    if (passwordScore === 1) return 'bg-red-500';
    if (passwordScore === 2) return 'bg-yellow-500';
    if (passwordScore === 3) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // Get password strength text
  const getPasswordStrengthText = () => {
    if (!formValues.password) return '';
    if (passwordScore === 0) return 'Very Weak';
    if (passwordScore === 1) return 'Weak';
    if (passwordScore === 2) return 'Fair';
    if (passwordScore === 3) return 'Good';
    return 'Strong';
  };

  return (
    <motion.form className="space-y-6" onSubmit={handleSubmit}>
      {formErrors.form && (
        <motion.div 
          variants={itemVariants}
          className="bg-red-100 text-red-700 p-3 rounded-md"
        >
          {formErrors.form}
        </motion.div>
      )}

      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
              First Name
            </label>
            <input
              id="firstName"
              name="firstName"
              type="text"
              autoComplete="given-name"
              required
              value={formValues.firstName}
              onChange={handleChange}
              className={`appearance-none block w-full px-3 py-2 border ${
                formErrors.firstName ? 'border-red-500' : 'border-gray-300'
              } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
            />
            {formErrors.firstName && (
              <p className="mt-1 text-sm text-red-600">{formErrors.firstName}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
              Last Name
            </label>
            <input
              id="lastName"
              name="lastName"
              type="text"
              autoComplete="family-name"
              required
              value={formValues.lastName}
              onChange={handleChange}
              className={`appearance-none block w-full px-3 py-2 border ${
                formErrors.lastName ? 'border-red-500' : 'border-gray-300'
              } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
            />
            {formErrors.lastName && (
              <p className="mt-1 text-sm text-red-600">{formErrors.lastName}</p>
            )}
          </div>
        </div>
      </motion.div>

      <motion.div variants={itemVariants}>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
          Email address
        </label>
        <input
          id="email"
          name="email"
          type="email"
          autoComplete="email"
          required
          value={formValues.email}
          onChange={handleChange}
          className={`appearance-none block w-full px-3 py-2 border ${
            formErrors.email ? 'border-red-500' : 'border-gray-300'
          } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
        />
        {formErrors.email && (
          <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>
        )}
      </motion.div>

      <motion.div variants={itemVariants}>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
          Password
        </label>
        <div className="relative">
          <input
            id="password"
            name="password"
            type={showPassword ? "text" : "password"}
            autoComplete="new-password"
            required
            value={formValues.password}
            onChange={handleChange}
            className={`appearance-none block w-full px-3 py-2 border ${
              formErrors.password ? 'border-red-500' : 'border-gray-300'
            } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm pr-10`}
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeSlashIcon className="h-5 w-5" />
            ) : (
              <EyeIcon className="h-5 w-5" />
            )}
          </button>
        </div>
        
        {/* Password strength meter */}
        {formValues.password && (
          <div className="mt-2">
            <div className="flex items-center justify-between mb-1">
              <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className={`h-full ${getPasswordStrengthColor()} transition-all duration-300`}
                  style={{ width: `${(passwordScore + 1) * 20}%` }}
                ></div>
              </div>
              <span className="ml-2 text-xs text-gray-500">{getPasswordStrengthText()}</span>
            </div>
            <p className="text-xs text-gray-500">
              Password must be at least 8 characters with 1 number and 1 special character
            </p>
          </div>
        )}
        
        {formErrors.password && (
          <p className="mt-1 text-sm text-red-600">{formErrors.password}</p>
        )}
      </motion.div>

      <motion.div variants={itemVariants}>
        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
            isSubmitting 
              ? 'bg-blue-400 cursor-not-allowed' 
              : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          }`}
        >
          {isSubmitting ? 'Creating Account...' : 'Continue'}
        </button>
      </motion.div>

      <motion.div variants={itemVariants} className="text-center mt-4">
        <p className="text-sm text-gray-600">
          Already have an account?{' '}
          <a href="/login" className="font-medium text-blue-600 hover:text-blue-500">
            Sign in
          </a>
        </p>
      </motion.div>
    </motion.form>
  );
}
