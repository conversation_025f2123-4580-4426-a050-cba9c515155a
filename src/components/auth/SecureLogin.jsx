import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { EyeIcon, EyeSlashIcon, LockClosedIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { authenticateUser, getCSRFToken } from '../../utils/authService';
import { checkPasswordStrength } from '../../utils/passwordUtils';

/**
 * SecureLogin Component
 * Secure login <NAME_EMAIL> with enhanced security features
 */
export default function SecureLogin({ onLoginSuccess, onLoginError }) {
  // Form state
  const [formData, setFormData] = useState({
    email: '<EMAIL>',
    password: '',
    csrfToken: ''
  });
  
  // UI state
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [passwordStrength, setPasswordStrength] = useState(null);
  const [attempts, setAttempts] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  const [lockoutTime, setLockoutTime] = useState(null);
  
  // Initialize CSRF token
  useEffect(() => {
    const csrfToken = getCSRFToken();
    setFormData(prev => ({ ...prev, csrfToken }));
  }, []);
  
  // Check password strength as user types
  useEffect(() => {
    if (formData.password) {
      const strength = checkPasswordStrength(formData.password);
      setPasswordStrength(strength);
    } else {
      setPasswordStrength(null);
    }
  }, [formData.password]);
  
  // Handle lockout timer
  useEffect(() => {
    let timer;
    if (isLocked && lockoutTime) {
      timer = setInterval(() => {
        const remaining = lockoutTime - Date.now();
        if (remaining <= 0) {
          setIsLocked(false);
          setLockoutTime(null);
          setAttempts(0);
        }
      }, 1000);
    }
    
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isLocked, lockoutTime]);
  
  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Check if account is locked
    if (isLocked) {
      const remaining = Math.ceil((lockoutTime - Date.now()) / 1000 / 60);
      setError(`Account is locked. Try again in ${remaining} minutes.`);
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await authenticateUser(
        formData.email,
        formData.password,
        formData.csrfToken
      );
      
      if (result.success) {
        // Reset attempts on successful login
        setAttempts(0);
        setIsLocked(false);
        setLockoutTime(null);
        
        // Call success callback
        if (onLoginSuccess) {
          onLoginSuccess(result.user, result.sessionToken);
        }
      } else {
        // Handle failed login
        const newAttempts = attempts + 1;
        setAttempts(newAttempts);
        
        // Lock account after 5 failed attempts
        if (newAttempts >= 5) {
          setIsLocked(true);
          setLockoutTime(Date.now() + 15 * 60 * 1000); // 15 minutes
          setError('Too many failed attempts. Account locked for 15 minutes.');
        } else {
          setError(result.error || 'Login failed. Please try again.');
        }
        
        // Call error callback
        if (onLoginError) {
          onLoginError(result.error);
        }
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('An unexpected error occurred. Please try again.');
      
      if (onLoginError) {
        onLoginError(err.message);
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  
  // Get password strength color
  const getPasswordStrengthColor = () => {
    if (!passwordStrength) return 'bg-gray-200';
    
    switch (passwordStrength.strength) {
      case 'Strong':
        return 'bg-green-500';
      case 'Medium':
        return 'bg-yellow-500';
      case 'Weak':
        return 'bg-red-500';
      default:
        return 'bg-gray-200';
    }
  };
  
  // Get remaining lockout time
  const getRemainingLockoutTime = () => {
    if (!isLocked || !lockoutTime) return '';
    
    const remaining = Math.ceil((lockoutTime - Date.now()) / 1000 / 60);
    return `${remaining} minute${remaining !== 1 ? 's' : ''}`;
  };
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 px-4 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="max-w-md w-full space-y-8"
      >
        {/* Header */}
        <div className="text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
          >
            <ShieldCheckIcon className="h-8 w-8 text-white" />
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="mt-6 text-3xl font-extrabold text-white"
          >
            Secure Admin Access
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="mt-2 text-sm text-gray-300"
          >
            Enhanced security for Examino administration
          </motion.p>
        </div>
        
        {/* Login Form */}
        <motion.form
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mt-8 space-y-6 bg-white/10 backdrop-blur-lg rounded-xl p-8 border border-white/20"
          onSubmit={handleSubmit}
        >
          {/* CSRF Token (hidden) */}
          <input
            type="hidden"
            name="csrfToken"
            value={formData.csrfToken}
          />
          
          {/* Error Message */}
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="bg-red-500/20 border border-red-500/50 text-red-200 px-4 py-3 rounded-lg"
                role="alert"
                aria-live="polite"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {error}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          
          {/* Lockout Warning */}
          <AnimatePresence>
            {isLocked && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="bg-orange-500/20 border border-orange-500/50 text-orange-200 px-4 py-3 rounded-lg"
                role="alert"
                aria-live="polite"
              >
                <div className="flex items-center">
                  <LockClosedIcon className="w-5 h-5 mr-2" />
                  Account locked for {getRemainingLockoutTime()}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          
          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-200 mb-2">
              Email Address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              readOnly
              value={formData.email}
              className="appearance-none relative block w-full px-3 py-3 border border-gray-600 placeholder-gray-400 text-gray-300 bg-gray-800/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              aria-describedby="email-description"
            />
            <p id="email-description" className="mt-1 text-xs text-gray-400">
              Authorized email address (read-only)
            </p>
          </div>
          
          {/* Password Field */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-200 mb-2">
              Password
            </label>
            <div className="relative">
              <input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                autoComplete="current-password"
                required
                value={formData.password}
                onChange={handleInputChange}
                disabled={isLocked}
                className="appearance-none relative block w-full px-3 py-3 pr-12 border border-gray-600 placeholder-gray-400 text-gray-300 bg-gray-800/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter your secure password"
                aria-describedby="password-strength password-requirements"
              />
              
              <button
                type="button"
                onClick={togglePasswordVisibility}
                disabled={isLocked}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-200 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? (
                  <EyeSlashIcon className="h-5 w-5" />
                ) : (
                  <EyeIcon className="h-5 w-5" />
                )}
              </button>
            </div>
            
            {/* Password Strength Indicator */}
            <AnimatePresence>
              {passwordStrength && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-2"
                  id="password-strength"
                >
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 h-2 bg-gray-600 rounded-full overflow-hidden">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${(passwordStrength.score / 6) * 100}%` }}
                        transition={{ duration: 0.3 }}
                        className={`h-full ${getPasswordStrengthColor()} transition-colors duration-300`}
                      />
                    </div>
                    <span className="text-xs text-gray-300 min-w-0">
                      {passwordStrength.strength}
                    </span>
                  </div>
                  
                  {passwordStrength.feedback.length > 0 && (
                    <ul className="mt-1 text-xs text-gray-400 space-y-1" id="password-requirements">
                      {passwordStrength.feedback.map((feedback, index) => (
                        <li key={index} className="flex items-center">
                          <span className="w-1 h-1 bg-gray-400 rounded-full mr-2" />
                          {feedback}
                        </li>
                      ))}
                    </ul>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          {/* Failed Attempts Counter */}
          {attempts > 0 && attempts < 5 && (
            <div className="text-sm text-yellow-300">
              Failed attempts: {attempts}/5
            </div>
          )}
          
          {/* Submit Button */}
          <motion.button
            type="submit"
            disabled={loading || isLocked || !formData.password}
            whileHover={{ scale: loading || isLocked ? 1 : 1.02 }}
            whileTap={{ scale: loading || isLocked ? 1 : 0.98 }}
            className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            {loading ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Authenticating...
              </div>
            ) : isLocked ? (
              'Account Locked'
            ) : (
              'Sign In Securely'
            )}
          </motion.button>
          
          {/* Security Notice */}
          <div className="text-center">
            <p className="text-xs text-gray-400">
              🔒 This login is protected by advanced security measures including rate limiting, 
              device fingerprinting, and encrypted session management.
            </p>
          </div>
        </motion.form>
      </motion.div>
    </div>
  );
}
