import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import <PERSON> from 'papapar<PERSON>';
import { supabase } from '../../utils/supabaseClient';

export default function StudentUpload() {
  const [csvData, setCsvData] = useState(null);
  const [parsedData, setParsedData] = useState([]);
  const [errors, setErrors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [validationErrors, setValidationErrors] = useState([]);

  // Function to generate a random password
  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  };

  // Function to validate email format
  const validateEmail = (email) => {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return re.test(String(email).toLowerCase());
  };

  // Function to validate CSV data
  const validateCsvData = (data) => {
    const errors = [];
    const requiredFields = ['name', 'email', 'class_id'];

    // Check if required fields exist
    const headers = Object.keys(data[0] || {});
    const missingFields = requiredFields.filter(field => !headers.includes(field));

    if (missingFields.length > 0) {
      errors.push(`Missing required fields: ${missingFields.join(', ')}`);
      return { valid: false, errors };
    }

    // Validate each row
    const rowErrors = [];
    data.forEach((row, index) => {
      const rowNum = index + 1;
      if (!row.name || row.name.trim() === '') {
        rowErrors.push(`Row ${rowNum}: Name is required`);
      }

      if (!row.email || row.email.trim() === '') {
        rowErrors.push(`Row ${rowNum}: Email is required`);
      } else if (!validateEmail(row.email)) {
        rowErrors.push(`Row ${rowNum}: Invalid email format`);
      }

      if (!row.class_id || row.class_id.trim() === '') {
        rowErrors.push(`Row ${rowNum}: Class ID is required`);
      }
    });

    return { valid: rowErrors.length === 0, errors: rowErrors };
  };

  // Function to process CSV file
  const processCsvFile = (file) => {
    setCsvData(file);
    setErrors([]);
    setValidationErrors([]);
    setSuccess(false);

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        const { data, errors: parseErrors } = results;

        if (parseErrors.length > 0) {
          setErrors(parseErrors.map(err => `Line ${err.row}: ${err.message}`));
          return;
        }

        // Validate the data
        const { valid, errors: validationErrors } = validateCsvData(data);
        if (!valid) {
          setValidationErrors(validationErrors);
          return;
        }

        // Process the data
        const processedData = data.map(row => {
          // Split name into first_name and last_name if not provided separately
          let firstName = row.first_name;
          let lastName = row.last_name;

          if (!firstName && !lastName && row.name) {
            const nameParts = row.name.trim().split(' ');
            if (nameParts.length > 1) {
              firstName = nameParts[0];
              lastName = nameParts.slice(1).join(' ');
            } else {
              firstName = nameParts[0];
              lastName = '';
            }
          }

          return {
            first_name: firstName,
            last_name: lastName,
            email: row.email.trim().toLowerCase(),
            student_id: row.student_id || `S${Math.floor(10000 + Math.random() * 90000)}`,
            class_id: row.class_id.trim(),
            course: row.course || 'General',
            semester: row.semester || '1',
            temporary_password: generatePassword()
          };
        });

        setParsedData(processedData);
      }
    });
  };

  // Dropzone configuration
  const onDrop = useCallback(acceptedFiles => {
    if (acceptedFiles.length > 0) {
      processCsvFile(acceptedFiles[0]);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv']
    },
    maxFiles: 1
  });

  // Function to import students
  const importStudents = async () => {
    if (parsedData.length === 0) return;

    setLoading(true);
    setErrors([]);
    setSuccess(false);

    // Check if any classes exist
    const { data: existingClasses, error: classError } = await supabase
      .from('classes')
      .select('id')
      .limit(1);

    if (classError) {
      setErrors([`Failed to check for classes: ${classError.message}`]);
      setLoading(false);
      return;
    }

    if (!existingClasses || existingClasses.length === 0) {
      setErrors(["No classes found. Please create at least one class before importing students."]);
      setLoading(false);
      return;
    }

    try {
      // First, create Supabase auth users
      const importErrors = [];
      const successfulImports = [];

      // Process in batches to avoid overwhelming the database
      const batchSize = 50;
      for (let i = 0; i < parsedData.length; i += batchSize) {
        const batch = parsedData.slice(i, i + batchSize);

        // Insert students into the students table
        const { data, error } = await supabase
          .from('students')
          .upsert(batch, {
            onConflict: 'email',
            ignoreDuplicates: false
          });

        if (error) {
          importErrors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${error.message}`);
        } else {
          successfulImports.push(...batch);
        }

        // For each student, create a student_classes entry
        for (const student of batch) {
          // Check if the class exists
          const { data: classData, error: classError } = await supabase
            .from('classes')
            .select('id')
            .eq('name', student.class_id)
            .maybeSingle();

          if (classError || !classData) {
            importErrors.push(`Class not found: ${student.class_id}`);
            continue;
          }

          // Get the student ID
          const { data: studentData, error: studentError } = await supabase
            .from('students')
            .select('id')
            .eq('email', student.email)
            .maybeSingle();

          if (studentError || !studentData) {
            importErrors.push(`Student not found: ${student.email}`);
            continue;
          }

          // Create the student_classes entry
          const { error: relationError } = await supabase
            .from('student_classes')
            .upsert({
              student_id: studentData.id,
              class_id: classData.id
            });

          if (relationError) {
            importErrors.push(`Failed to assign student to class: ${student.email} -> ${student.class_id}`);
          }
        }
      }

      if (importErrors.length > 0) {
        setErrors(importErrors);
      }

      if (successfulImports.length > 0) {
        setSuccess(true);
        setParsedData([]);
        setCsvData(null);
      }
    } catch (error) {
      setErrors([`Import failed: ${error.message}`]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-4">Student CSV Upload</h2>

      {/* Class Check Warning */}
      <div className="mb-4 bg-blue-50 p-3 rounded-md">
        <p className="text-blue-700">
          <strong>Note:</strong> You need to create classes before uploading students.
          Go to the <strong>Classes</strong> tab to create classes first.
        </p>
      </div>

      {/* Instructions */}
      <div className="mb-6 bg-blue-50 p-4 rounded-md">
        <h3 className="font-medium text-blue-800 mb-2">Instructions</h3>
        <p className="text-blue-700 mb-2">Upload a CSV file with the following columns:</p>
        <ul className="list-disc list-inside text-blue-700 mb-2">
          <li><strong>name</strong> or <strong>first_name</strong> and <strong>last_name</strong> (required)</li>
          <li><strong>email</strong> (required, must be unique)</li>
          <li><strong>class_id</strong> (required, must match an existing class name)</li>
          <li><strong>student_id</strong> (optional, will be generated if not provided)</li>
          <li><strong>course</strong> (optional)</li>
          <li><strong>semester</strong> (optional)</li>
        </ul>
        <p className="text-blue-700">
          <strong>Note:</strong> Temporary passwords will be generated automatically.
        </p>
      </div>

      {/* Dropzone */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer mb-6 ${
          isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-500'
        }`}
      >
        <input {...getInputProps()} />
        {isDragActive ? (
          <p className="text-blue-500">Drop the CSV file here...</p>
        ) : (
          <div>
            <p className="text-gray-600 mb-2">Drag and drop a CSV file here, or click to select a file</p>
            <p className="text-sm text-gray-500">Only CSV files are accepted</p>
          </div>
        )}
        {csvData && (
          <div className="mt-4">
            <p className="text-green-600 font-medium">{csvData.name} selected</p>
            <p className="text-sm text-gray-500">{(csvData.size / 1024).toFixed(2)} KB</p>
          </div>
        )}
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="mb-6 bg-red-50 p-4 rounded-md">
          <h3 className="font-medium text-red-800 mb-2">Validation Errors</h3>
          <ul className="list-disc list-inside text-red-700">
            {validationErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Parse Errors */}
      {errors.length > 0 && (
        <div className="mb-6 bg-red-50 p-4 rounded-md">
          <h3 className="font-medium text-red-800 mb-2">Errors</h3>
          <ul className="list-disc list-inside text-red-700">
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="mb-6 bg-green-50 p-4 rounded-md">
          <h3 className="font-medium text-green-800 mb-2">Success!</h3>
          <p className="text-green-700">Students have been imported successfully.</p>
        </div>
      )}

      {/* Preview Table */}
      {parsedData.length > 0 && (
        <div className="mb-6">
          <h3 className="font-medium text-gray-800 mb-2">Preview ({parsedData.length} students)</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th className="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
                  <th className="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                  <th className="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                  <th className="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Semester</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {parsedData.slice(0, 10).map((student, index) => (
                  <tr key={index}>
                    <td className="py-2 px-4 whitespace-nowrap">
                      {student.first_name} {student.last_name}
                    </td>
                    <td className="py-2 px-4 whitespace-nowrap">{student.email}</td>
                    <td className="py-2 px-4 whitespace-nowrap">{student.student_id}</td>
                    <td className="py-2 px-4 whitespace-nowrap">{student.class_id}</td>
                    <td className="py-2 px-4 whitespace-nowrap">{student.course}</td>
                    <td className="py-2 px-4 whitespace-nowrap">{student.semester}</td>
                  </tr>
                ))}
                {parsedData.length > 10 && (
                  <tr>
                    <td colSpan="6" className="py-2 px-4 text-center text-gray-500">
                      ... and {parsedData.length - 10} more students
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Import Button */}
      <div className="flex justify-end">
        <button
          onClick={importStudents}
          disabled={parsedData.length === 0 || loading}
          className={`px-4 py-2 rounded-md ${
            parsedData.length === 0 || loading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {loading ? 'Importing...' : `Import ${parsedData.length} Students`}
        </button>
      </div>
    </div>
  );
}
