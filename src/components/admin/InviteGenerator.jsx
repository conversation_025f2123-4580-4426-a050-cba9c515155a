import { useState } from 'react';
import { supabase } from '../../utils/supabaseClient';
import { useAuth } from '../../contexts/AuthContext';

export default function InviteGenerator() {
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('institute');
  const [expiryDays, setExpiryDays] = useState(7);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [inviteCode, setInviteCode] = useState('');
  const [inviteLink, setInviteLink] = useState('');
  const [invites, setInvites] = useState([]);
  const [loadingInvites, setLoadingInvites] = useState(false);
  
  const { ROLES, isAdmin } = useAuth();
  
  // Generate invite code
  const generateInvite = async (e) => {
    e.preventDefault();
    
    if (!email) {
      setError('Email is required');
      return;
    }
    
    try {
      setError('');
      setLoading(true);
      
      // Check if we're in development mode
      const isDevelopment = import.meta.env.DEV || !import.meta.env.VITE_SUPABASE_URL;
      
      if (isDevelopment) {
        // Mock invite generation for development
        setTimeout(() => {
          const mockCode = Math.random().toString(36).substring(2, 8);
          setInviteCode(mockCode);
          setInviteLink(`${window.location.origin}/invite-signup?code=${mockCode}`);
          setLoading(false);
        }, 1000);
      } else {
        // Real invite generation with Supabase
        const { data, error } = await supabase.rpc('generate_invite_code', {
          role_name: role,
          email,
          expiry_days: expiryDays
        });
        
        if (error) throw error;
        
        setInviteCode(data);
        setInviteLink(`${window.location.origin}/invite-signup?code=${data}`);
        
        // Refresh invite list
        fetchInvites();
      }
    } catch (error) {
      console.error('Error generating invite code:', error);
      setError(error.message || 'Failed to generate invite code. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch existing invites
  const fetchInvites = async () => {
    try {
      setLoadingInvites(true);
      
      // Check if we're in development mode
      const isDevelopment = import.meta.env.DEV || !import.meta.env.VITE_SUPABASE_URL;
      
      if (isDevelopment) {
        // Mock invites for development
        setTimeout(() => {
          const mockInvites = [
            {
              id: '1',
              code: 'abc123',
              email: '<EMAIL>',
              role_name: 'admin',
              created_at: new Date().toISOString(),
              expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
              used_at: null
            },
            {
              id: '2',
              code: 'def456',
              email: '<EMAIL>',
              role_name: 'institute',
              created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
              expires_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
              used_at: null
            },
            {
              id: '3',
              code: 'ghi789',
              email: '<EMAIL>',
              role_name: 'institute',
              created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
              expires_at: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
              used_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
            }
          ];
          
          setInvites(mockInvites);
          setLoadingInvites(false);
        }, 1000);
      } else {
        // Real fetch with Supabase
        const { data, error } = await supabase
          .from('invite_codes')
          .select(`
            *,
            user_roles(role_name)
          `)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        
        setInvites(data.map(invite => ({
          ...invite,
          role_name: invite.user_roles.role_name
        })));
      }
    } catch (error) {
      console.error('Error fetching invites:', error);
    } finally {
      setLoadingInvites(false);
    }
  };
  
  // Copy invite link to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(inviteLink);
    alert('Invite link copied to clipboard!');
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };
  
  // Load invites on component mount
  useState(() => {
    if (isAdmin) {
      fetchInvites();
    }
  }, [isAdmin]);
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Generate Invite Code</h2>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      <form onSubmit={generateInvite} className="mb-6">
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="email">
            Email Address
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="Enter email address"
            required
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="role">
            Role
          </label>
          <select
            id="role"
            value={role}
            onChange={(e) => setRole(e.target.value)}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          >
            <option value={ROLES.ADMIN}>Admin</option>
            <option value={ROLES.INSTITUTE}>Institute</option>
          </select>
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="expiryDays">
            Expires After (days)
          </label>
          <input
            id="expiryDays"
            type="number"
            value={expiryDays}
            onChange={(e) => setExpiryDays(parseInt(e.target.value))}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            min="1"
            max="30"
            required
          />
        </div>
        
        <button
          type="submit"
          className={`bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
          disabled={loading}
        >
          {loading ? 'Generating...' : 'Generate Invite'}
        </button>
      </form>
      
      {inviteCode && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
          <h3 className="font-bold">Invite Generated!</h3>
          <p className="mb-2">Code: <span className="font-mono">{inviteCode}</span></p>
          <p className="mb-4">Link: <span className="font-mono break-all">{inviteLink}</span></p>
          <button
            onClick={copyToClipboard}
            className="bg-green-600 hover:bg-green-700 text-white font-bold py-1 px-3 rounded text-sm"
          >
            Copy Link
          </button>
        </div>
      )}
      
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Existing Invites</h3>
        
        {loadingInvites ? (
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : invites.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-2 px-4 text-left">Email</th>
                  <th className="py-2 px-4 text-left">Role</th>
                  <th className="py-2 px-4 text-left">Code</th>
                  <th className="py-2 px-4 text-left">Created</th>
                  <th className="py-2 px-4 text-left">Expires</th>
                  <th className="py-2 px-4 text-left">Status</th>
                </tr>
              </thead>
              <tbody>
                {invites.map((invite) => (
                  <tr key={invite.id} className="border-t">
                    <td className="py-2 px-4">{invite.email}</td>
                    <td className="py-2 px-4 capitalize">{invite.role_name}</td>
                    <td className="py-2 px-4 font-mono">{invite.code}</td>
                    <td className="py-2 px-4">{formatDate(invite.created_at)}</td>
                    <td className="py-2 px-4">{formatDate(invite.expires_at)}</td>
                    <td className="py-2 px-4">
                      {invite.used_at ? (
                        <span className="bg-gray-200 text-gray-800 py-1 px-2 rounded text-xs">Used</span>
                      ) : new Date(invite.expires_at) < new Date() ? (
                        <span className="bg-red-200 text-red-800 py-1 px-2 rounded text-xs">Expired</span>
                      ) : (
                        <span className="bg-green-200 text-green-800 py-1 px-2 rounded text-xs">Active</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-600">No invites found.</p>
        )}
      </div>
    </div>
  );
}
