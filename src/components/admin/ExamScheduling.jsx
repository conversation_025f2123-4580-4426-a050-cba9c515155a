import React, { useState, useEffect, Suspense } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { supabase } from '../../utils/supabaseClient';
import { useAuth } from '../../contexts/AuthContext';
import 'react-datepicker/dist/react-datepicker.css';

// Import DatePicker dynamically to avoid SSR issues
const DatePicker = React.lazy(() => import('react-datepicker'));

export default function ExamScheduling() {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [classes, setClasses] = useState([]);
  const [loadingClasses, setLoadingClasses] = useState(true);
  const [exams, setExams] = useState([]);
  const [loadingExams, setLoadingExams] = useState(true);
  const [selectedExam, setSelectedExam] = useState(null);

  const { register, handleSubmit, control, reset, setValue, formState: { errors } } = useForm();

  useEffect(() => {
    fetchClasses();
    fetchExams();
  }, []);

  useEffect(() => {
    if (selectedExam) {
      // Convert class_ids array to selected classes
      setValue('title', selectedExam.title);
      setValue('description', selectedExam.description);
      setValue('duration', selectedExam.duration_minutes);
      setValue('scheduledAt', selectedExam.scheduled_at ? new Date(selectedExam.scheduled_at) : null);
      setValue('classes', selectedExam.class_ids || []);
      setValue('totalQuestions', selectedExam.total_questions);
      setValue('passingScore', selectedExam.passing_score);
    } else {
      reset();
    }
  }, [selectedExam, setValue, reset]);

  const fetchClasses = async () => {
    try {
      setLoadingClasses(true);

      const { data, error } = await supabase
        .from('classes')
        .select('id, name')
        .order('name', { ascending: true });

      if (error) throw error;

      setClasses(data || []);
    } catch (err) {
      console.error('Error fetching classes:', err);
      setError('Failed to load classes');
    } finally {
      setLoadingClasses(false);
    }
  };

  const fetchExams = async () => {
    try {
      setLoadingExams(true);

      const { data, error } = await supabase
        .from('exams')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setExams(data || []);
    } catch (err) {
      console.error('Error fetching exams:', err);
    } finally {
      setLoadingExams(false);
    }
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);

      // Format the data
      const examData = {
        title: data.title,
        description: data.description,
        duration_minutes: parseInt(data.duration),
        scheduled_at: data.scheduledAt ? data.scheduledAt.toISOString() : null,
        class_ids: data.classes || [],
        total_questions: parseInt(data.totalQuestions) || 10,
        passing_score: parseInt(data.passingScore) || 60,
        created_by: currentUser.id
      };

      if (selectedExam) {
        // Update existing exam
        const { error: updateError } = await supabase
          .from('exams')
          .update(examData)
          .eq('id', selectedExam.id);

        if (updateError) throw updateError;
      } else {
        // Create new exam
        const { error: insertError } = await supabase
          .from('exams')
          .insert(examData);

        if (insertError) throw insertError;
      }

      // Reset form and refresh exams
      reset();
      setSelectedExam(null);
      setSuccess(true);
      fetchExams();
    } catch (err) {
      console.error('Error saving exam:', err);
      setError(`Failed to ${selectedExam ? 'update' : 'create'} exam: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteExam = async (examId) => {
    if (!confirm('Are you sure you want to delete this exam?')) {
      return;
    }

    try {
      setLoading(true);

      const { error } = await supabase
        .from('exams')
        .delete()
        .eq('id', examId);

      if (error) throw error;

      // Refresh exams
      fetchExams();

      // If the deleted exam was selected, reset the form
      if (selectedExam && selectedExam.id === examId) {
        setSelectedExam(null);
        reset();
      }
    } catch (err) {
      console.error('Error deleting exam:', err);
      setError(`Failed to delete exam: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not scheduled';

    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getClassNames = (classIds) => {
    if (!classIds || classIds.length === 0) return 'All classes';

    return classIds.map(id => {
      const cls = classes.find(c => c.id === id);
      return cls ? cls.name : 'Unknown';
    }).join(', ');
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-6">Exam Scheduling</h2>

      {error && (
        <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 text-green-700 p-3 rounded-md mb-4">
          Exam {selectedExam ? 'updated' : 'created'} successfully!
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Exam Form */}
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="font-medium text-gray-800 mb-4">
            {selectedExam ? 'Edit Exam' : 'Create New Exam'}
          </h3>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">
                Exam Title*
              </label>
              <input
                type="text"
                {...register("title", { required: "Exam title is required" })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {errors.title && (
                <p className="text-red-600 text-sm mt-1">{errors.title.message}</p>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">
                Description
              </label>
              <textarea
                {...register("description")}
                rows="2"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Duration (minutes)*
                </label>
                <input
                  type="number"
                  {...register("duration", {
                    required: "Duration is required",
                    min: { value: 1, message: "Duration must be at least 1 minute" }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {errors.duration && (
                  <p className="text-red-600 text-sm mt-1">{errors.duration.message}</p>
                )}
              </div>

              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Scheduled Date/Time
                </label>
                <Controller
                  control={control}
                  name="scheduledAt"
                  render={({ field }) => (
                    <Suspense fallback={<div className="animate-pulse h-10 bg-gray-200 rounded w-full"></div>}>
                      <DatePicker
                        selected={field.value}
                        onChange={(date) => field.onChange(date)}
                        showTimeSelect
                        timeFormat="HH:mm"
                        timeIntervals={15}
                        dateFormat="MMMM d, yyyy h:mm aa"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholderText="Select date and time"
                      />
                    </Suspense>
                  )}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Total Questions*
                </label>
                <input
                  type="number"
                  {...register("totalQuestions", {
                    required: "Total questions is required",
                    min: { value: 1, message: "Must have at least 1 question" }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {errors.totalQuestions && (
                  <p className="text-red-600 text-sm mt-1">{errors.totalQuestions.message}</p>
                )}
              </div>

              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Passing Score (%)*
                </label>
                <input
                  type="number"
                  {...register("passingScore", {
                    required: "Passing score is required",
                    min: { value: 0, message: "Minimum 0%" },
                    max: { value: 100, message: "Maximum 100%" }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {errors.passingScore && (
                  <p className="text-red-600 text-sm mt-1">{errors.passingScore.message}</p>
                )}
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-gray-700 font-medium mb-2">
                Assign to Classes
              </label>
              {loadingClasses ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent"></div>
                  <span className="text-gray-500">Loading classes...</span>
                </div>
              ) : classes.length === 0 ? (
                <div className="bg-yellow-50 p-3 rounded-md mb-2">
                  <p className="text-yellow-700 text-sm">
                    No classes available. Please go to the Class Management tab to create classes first.
                  </p>
                  <button
                    type="button"
                    onClick={() => window.location.hash = '#classes'}
                    className="text-blue-600 hover:text-blue-800 text-sm mt-2 flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Go to Class Management
                  </button>
                </div>
              ) : (
                <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
                  {classes.map((cls) => (
                    <div key={cls.id} className="flex items-center mb-2">
                      <input
                        type="checkbox"
                        id={`class-${cls.id}`}
                        value={cls.id}
                        {...register("classes")}
                        className="mr-2"
                      />
                      <label htmlFor={`class-${cls.id}`} className="text-gray-700">
                        {cls.name}
                      </label>
                    </div>
                  ))}
                </div>
              )}
              <p className="text-gray-500 text-sm mt-1">
                Leave empty to make the exam available to all classes.
              </p>
            </div>

            <div className="flex justify-between">
              {selectedExam && (
                <button
                  type="button"
                  onClick={() => {
                    setSelectedExam(null);
                    reset();
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
                >
                  Cancel
                </button>
              )}
              <button
                type="submit"
                disabled={loading}
                className={`px-4 py-2 rounded-md ${
                  loading
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {loading ? 'Saving...' : selectedExam ? 'Update Exam' : 'Create Exam'}
              </button>
            </div>
          </form>
        </div>

        {/* Exams List */}
        <div>
          <h3 className="font-medium text-gray-800 mb-4">Scheduled Exams</h3>

          {loadingExams ? (
            <div className="text-center py-4">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-500 border-t-transparent"></div>
              <p className="mt-2 text-gray-600">Loading exams...</p>
            </div>
          ) : exams.length === 0 ? (
            <div className="bg-yellow-50 text-yellow-700 p-4 rounded-md">
              No exams found. Create your first exam to get started.
            </div>
          ) : (
            <div className="space-y-4 max-h-[500px] overflow-y-auto pr-2">
              {exams.map((exam) => (
                <div
                  key={exam.id}
                  className={`border rounded-md overflow-hidden ${
                    selectedExam && selectedExam.id === exam.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-blue-300'
                  }`}
                >
                  <div className="p-4">
                    <div className="flex justify-between items-start">
                      <h4 className="font-medium text-gray-800">{exam.title}</h4>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setSelectedExam(exam)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteExam(exam.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          Delete
                        </button>
                      </div>
                    </div>

                    <div className="mt-2 text-sm">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                        <div className="text-gray-500">Scheduled:</div>
                        <div className="text-gray-700">{formatDate(exam.scheduled_at)}</div>

                        <div className="text-gray-500">Duration:</div>
                        <div className="text-gray-700">{exam.duration_minutes} minutes</div>

                        <div className="text-gray-500">Classes:</div>
                        <div className="text-gray-700">{getClassNames(exam.class_ids)}</div>

                        <div className="text-gray-500">Questions:</div>
                        <div className="text-gray-700">{exam.total_questions}</div>

                        <div className="text-gray-500">Passing Score:</div>
                        <div className="text-gray-700">{exam.passing_score}%</div>
                      </div>

                      {exam.description && (
                        <div className="mt-2">
                          <div className="text-gray-500">Description:</div>
                          <div className="text-gray-700">{exam.description}</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
