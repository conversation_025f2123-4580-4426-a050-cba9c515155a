import { useState, useEffect, useRef } from 'react';
import { supabase } from '../../utils/supabaseClient';
import { motion, AnimatePresence } from 'framer-motion';
import { format, parseISO, subDays, startOfDay, endOfDay } from 'date-fns';
import { CSVLink } from 'react-csv';

/**
 * AttendanceLogs Component
 * Displays attendance records with filtering and export capabilities
 */
export default function AttendanceLogs() {
  // State for attendance records
  const [attendanceRecords, setAttendanceRecords] = useState([]);
  const [filteredRecords, setFilteredRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // State for filters
  const [dateRange, setDateRange] = useState({
    startDate: format(subDays(new Date(), 7), 'yyyy-MM-dd'),
    endDate: format(new Date(), 'yyyy-MM-dd')
  });
  const [classFilter, setClassFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  // State for classes and students
  const [classes, setClasses] = useState([]);
  const [students, setStudents] = useState([]);
  
  // State for statistics
  const [stats, setStats] = useState({
    total: 0,
    present: 0,
    absent: 0,
    percentagePresent: 0
  });
  
  // Refs
  const csvLink = useRef();
  
  // Fetch attendance records, classes, and students on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch classes
        const { data: classesData, error: classesError } = await supabase
          .from('classes')
          .select('id, name')
          .order('name');
        
        if (classesError) throw classesError;
        setClasses(classesData || []);
        
        // Fetch students
        const { data: studentsData, error: studentsError } = await supabase
          .from('students')
          .select('id, first_name, last_name, student_id')
          .order('last_name');
        
        if (studentsError) throw studentsError;
        setStudents(studentsData || []);
        
        // Fetch attendance records
        await fetchAttendanceRecords();
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
    
    // Set up real-time subscription for attendance
    const attendanceSubscription = supabase
      .channel('attendance-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'attendance'
      }, (payload) => {
        if (payload.eventType === 'INSERT') {
          // Add new record
          setAttendanceRecords(prev => [payload.new, ...prev]);
        } else if (payload.eventType === 'UPDATE') {
          // Update existing record
          setAttendanceRecords(prev => 
            prev.map(record => record.id === payload.new.id ? payload.new : record)
          );
        } else if (payload.eventType === 'DELETE') {
          // Remove deleted record
          setAttendanceRecords(prev => 
            prev.filter(record => record.id !== payload.old.id)
          );
        }
        
        // Show notification
        setSuccess('Attendance records updated in real-time');
        setTimeout(() => setSuccess(null), 3000);
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(attendanceSubscription);
    };
  }, []);
  
  // Apply filters when attendance records, date range, class filter, status filter, or search query changes
  useEffect(() => {
    applyFilters();
  }, [attendanceRecords, dateRange, classFilter, statusFilter, searchQuery]);
  
  // Fetch attendance records based on date range
  const fetchAttendanceRecords = async () => {
    try {
      setLoading(true);
      
      // Convert dates to ISO strings
      const startDateObj = startOfDay(parseISO(dateRange.startDate));
      const endDateObj = endOfDay(parseISO(dateRange.endDate));
      
      // Fetch attendance records
      const { data, error } = await supabase
        .from('attendance')
        .select(`
          *,
          students (
            id,
            first_name,
            last_name,
            student_id
          ),
          classes (
            id,
            name
          )
        `)
        .gte('created_at', startDateObj.toISOString())
        .lte('created_at', endDateObj.toISOString())
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      setAttendanceRecords(data || []);
    } catch (err) {
      console.error('Error fetching attendance records:', err);
      setError('Failed to load attendance records. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Apply filters to attendance records
  const applyFilters = () => {
    let filtered = [...attendanceRecords];
    
    // Apply class filter
    if (classFilter) {
      filtered = filtered.filter(record => record.class_id === classFilter);
    }
    
    // Apply status filter
    if (statusFilter !== 'all') {
      const isPresent = statusFilter === 'present';
      filtered = filtered.filter(record => record.status === isPresent);
    }
    
    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(record => {
        const student = record.students;
        if (!student) return false;
        
        return (
          student.first_name.toLowerCase().includes(query) ||
          student.last_name.toLowerCase().includes(query) ||
          student.student_id.toLowerCase().includes(query)
        );
      });
    }
    
    // Update filtered records
    setFilteredRecords(filtered);
    
    // Calculate statistics
    const total = filtered.length;
    const present = filtered.filter(record => record.status).length;
    const absent = total - present;
    const percentagePresent = total > 0 ? (present / total) * 100 : 0;
    
    setStats({
      total,
      present,
      absent,
      percentagePresent
    });
  };
  
  // Handle date range change
  const handleDateRangeChange = (e) => {
    const { name, value } = e.target;
    setDateRange(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle filter change
  const handleFilterChange = async () => {
    await fetchAttendanceRecords();
  };
  
  // Handle class filter change
  const handleClassFilterChange = (e) => {
    setClassFilter(e.target.value);
  };
  
  // Handle status filter change
  const handleStatusFilterChange = (e) => {
    setStatusFilter(e.target.value);
  };
  
  // Handle search query change
  const handleSearchQueryChange = (e) => {
    setSearchQuery(e.target.value);
  };
  
  // Handle record deletion
  const handleDeleteRecord = async (id) => {
    try {
      const { error } = await supabase
        .from('attendance')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      // Remove record from state
      setAttendanceRecords(prev => prev.filter(record => record.id !== id));
      setSuccess('Attendance record deleted successfully');
    } catch (err) {
      console.error('Error deleting attendance record:', err);
      setError('Failed to delete attendance record. Please try again.');
    }
  };
  
  // Handle record update
  const handleUpdateRecord = async (id, status) => {
    try {
      const { error } = await supabase
        .from('attendance')
        .update({ status })
        .eq('id', id);
      
      if (error) throw error;
      
      // Update record in state
      setAttendanceRecords(prev => 
        prev.map(record => record.id === id ? { ...record, status } : record)
      );
      setSuccess('Attendance record updated successfully');
    } catch (err) {
      console.error('Error updating attendance record:', err);
      setError('Failed to update attendance record. Please try again.');
    }
  };
  
  // Prepare CSV data for export
  const prepareCSVData = () => {
    return filteredRecords.map(record => {
      const student = record.students || {};
      const className = record.classes?.name || 'N/A';
      
      return {
        Date: format(parseISO(record.created_at), 'yyyy-MM-dd'),
        Time: format(parseISO(record.created_at), 'HH:mm:ss'),
        'Student ID': student.student_id || 'N/A',
        'Student Name': `${student.first_name || ''} ${student.last_name || ''}`.trim() || 'N/A',
        Class: className,
        Status: record.status ? 'Present' : 'Absent',
        'Verification Method': record.verification_method || 'N/A',
        Confidence: record.confidence ? `${(record.confidence * 100).toFixed(2)}%` : 'N/A'
      };
    });
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    return format(parseISO(dateString), 'MMM dd, yyyy HH:mm');
  };
  
  // Get student name
  const getStudentName = (record) => {
    const student = record.students;
    if (!student) return 'Unknown Student';
    
    return `${student.first_name || ''} ${student.last_name || ''}`.trim() || 'Unknown';
  };
  
  // Get class name
  const getClassName = (record) => {
    return record.classes?.name || 'N/A';
  };
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Attendance Logs</h1>
      
      {/* Error and Success Messages */}
      <AnimatePresence>
        {error && (
          <motion.div 
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
          >
            <span className="font-bold">Error:</span> {error}
            <button 
              className="float-right"
              onClick={() => setError(null)}
            >
              &times;
            </button>
          </motion.div>
        )}
        
        {success && (
          <motion.div 
            className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
          >
            <span className="font-bold">Success:</span> {success}
            <button 
              className="float-right"
              onClick={() => setSuccess(null)}
            >
              &times;
            </button>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Filters</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              name="startDate"
              value={dateRange.startDate}
              onChange={handleDateRangeChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              name="endDate"
              value={dateRange.endDate}
              onChange={handleDateRangeChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Class
            </label>
            <select
              value={classFilter}
              onChange={handleClassFilterChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">All Classes</option>
              {classes.map(cls => (
                <option key={cls.id} value={cls.id}>
                  {cls.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={handleStatusFilterChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="all">All</option>
              <option value="present">Present</option>
              <option value="absent">Absent</option>
            </select>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row md:items-end gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search Student
            </label>
            <input
              type="text"
              placeholder="Search by name or ID..."
              value={searchQuery}
              onChange={handleSearchQueryChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={handleFilterChange}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Apply Filters
            </button>
            
            <CSVLink
              data={prepareCSVData()}
              filename={`attendance_${dateRange.startDate}_to_${dateRange.endDate}.csv`}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 inline-block"
              ref={csvLink}
            >
              Export CSV
            </CSVLink>
          </div>
        </div>
      </div>
      
      {/* Statistics */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Attendance Statistics</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gray-100 rounded-md p-4 text-center">
            <p className="text-2xl font-bold text-gray-800">{stats.total}</p>
            <p className="text-sm text-gray-600">Total Records</p>
          </div>
          
          <div className="bg-green-100 rounded-md p-4 text-center">
            <p className="text-2xl font-bold text-green-800">{stats.present}</p>
            <p className="text-sm text-green-600">Present</p>
          </div>
          
          <div className="bg-red-100 rounded-md p-4 text-center">
            <p className="text-2xl font-bold text-red-800">{stats.absent}</p>
            <p className="text-sm text-red-600">Absent</p>
          </div>
          
          <div className="bg-blue-100 rounded-md p-4 text-center">
            <p className="text-2xl font-bold text-blue-800">{stats.percentagePresent.toFixed(2)}%</p>
            <p className="text-sm text-blue-600">Attendance Rate</p>
          </div>
        </div>
        
        {/* Attendance Chart */}
        <div className="mt-6">
          <div className="w-full h-6 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-green-500"
              style={{ width: `${stats.percentagePresent}%` }}
            ></div>
          </div>
          <div className="flex justify-between mt-1 text-xs text-gray-500">
            <span>0%</span>
            <span>25%</span>
            <span>50%</span>
            <span>75%</span>
            <span>100%</span>
          </div>
        </div>
      </div>
      
      {/* Attendance Records */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Attendance Records</h2>
        
        {loading ? (
          <div className="text-center py-8">
            <svg className="animate-spin h-8 w-8 mx-auto text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="mt-2 text-gray-500">Loading attendance records...</p>
          </div>
        ) : filteredRecords.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No attendance records found for the selected filters.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Class
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Verification
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRecords.map(record => (
                  <tr key={record.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(record.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          {record.students?.reference_image_url ? (
                            <img 
                              className="h-10 w-10 rounded-full object-cover" 
                              src={record.students.reference_image_url} 
                              alt={getStudentName(record)} 
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {getStudentName(record)}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {record.students?.student_id || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getClassName(record)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        record.status 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {record.status ? 'Present' : 'Absent'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.verification_method || 'Manual'} 
                      {record.confidence && (
                        <span className="ml-1 text-xs text-gray-400">
                          ({(record.confidence * 100).toFixed(2)}%)
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleUpdateRecord(record.id, !record.status)}
                          className={`text-xs px-2 py-1 rounded ${
                            record.status 
                              ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                              : 'bg-green-100 text-green-700 hover:bg-green-200'
                          }`}
                        >
                          Mark as {record.status ? 'Absent' : 'Present'}
                        </button>
                        <button
                          onClick={() => handleDeleteRecord(record.id)}
                          className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
