import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { supabase } from '../../utils/supabaseClient';
import { useAuth } from '../../contexts/AuthContext';

export default function ClassManagement() {
  const { currentUser } = useAuth();
  console.log('Current user in ClassManagement:', currentUser);
  const [classes, setClasses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [editingClass, setEditingClass] = useState(null);
  const [studentCounts, setStudentCounts] = useState({});
  const [expandedClass, setExpandedClass] = useState(null);
  const [classStudents, setClassStudents] = useState([]);
  const [loadingStudents, setLoadingStudents] = useState(false);

  const { register, handleSubmit, reset, formState: { errors } } = useForm();

  useEffect(() => {
    fetchClasses();
  }, []);

  const fetchClasses = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('classes')
        .select('*')
        .order('name', { ascending: true });

      if (error) throw error;

      setClasses(data || []);

      // Fetch student counts for each class
      const counts = {};
      for (const cls of data || []) {
        const { count, error: countError } = await supabase
          .from('student_classes')
          .select('*', { count: 'exact', head: true })
          .eq('class_id', cls.id);

        if (!countError) {
          counts[cls.id] = count;
        }
      }

      setStudentCounts(counts);
    } catch (err) {
      console.error('Error fetching classes:', err);
      setError('Failed to load classes');
    } finally {
      setLoading(false);
    }
  };

  const fetchClassStudents = async (classId) => {
    try {
      setLoadingStudents(true);

      const { data, error } = await supabase
        .rpc('get_students_in_class', { class_uuid: classId });

      if (error) throw error;

      setClassStudents(data || []);
    } catch (err) {
      console.error('Error fetching class students:', err);
    } finally {
      setLoadingStudents(false);
    }
  };

  const toggleClassExpansion = (classId) => {
    if (expandedClass === classId) {
      setExpandedClass(null);
      setClassStudents([]);
    } else {
      setExpandedClass(classId);
      fetchClassStudents(classId);
    }
  };

  const handleCreateClass = async (data) => {
    try {
      setLoading(true);
      setError(null);

      // Create class data, handle case when currentUser might be null
      const classData = {
        name: data.name,
        description: data.description,
        created_by: currentUser?.id || 'test-user-id' // Fallback to test user ID
      };

      console.log('Class data to save:', classData);

      if (editingClass) {
        // Update existing class
        const { error: updateError } = await supabase
          .from('classes')
          .update(classData)
          .eq('id', editingClass.id);

        if (updateError) throw updateError;
      } else {
        // Create new class
        const { error: insertError } = await supabase
          .from('classes')
          .insert(classData);

        if (insertError) throw insertError;
      }

      // Class created/updated successfully

      // Reset form and refresh classes
      reset();
      setShowForm(false);
      setEditingClass(null);
      fetchClasses();
    } catch (err) {
      console.error('Error saving class:', err);
      setError(`Failed to ${editingClass ? 'update' : 'create'} class: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEditClass = (cls) => {
    setEditingClass(cls);
    reset({
      name: cls.name,
      description: cls.description
    });
    setShowForm(true);
  };

  const handleDeleteClass = async (classId) => {
    if (!confirm('Are you sure you want to delete this class? This will remove all students from the class.')) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Delete class
      const { error } = await supabase
        .from('classes')
        .delete()
        .eq('id', classId);

      if (error) throw error;

      // Refresh classes
      fetchClasses();
    } catch (err) {
      console.error('Error deleting class:', err);
      setError(`Failed to delete class: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Class Management</h2>
        <button
          onClick={() => {
            setEditingClass(null);
            reset({ name: '', description: '' });
            setShowForm(!showForm);
          }}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          {showForm ? 'Cancel' : 'Create New Class'}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4">
          {error}
        </div>
      )}

      {/* Class Form */}
      {showForm && (
        <div className="bg-gray-50 p-4 rounded-md mb-6">
          <h3 className="font-medium text-gray-800 mb-4">
            {editingClass ? 'Edit Class' : 'Create New Class'}
          </h3>
          <form onSubmit={handleSubmit(handleCreateClass)}>
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">
                Class Name*
              </label>
              <input
                type="text"
                {...register("name", { required: "Class name is required" })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {errors.name && (
                <p className="text-red-600 text-sm mt-1">{errors.name.message}</p>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">
                Description
              </label>
              <textarea
                {...register("description")}
                rows="3"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={loading}
                className={`px-4 py-2 rounded-md ${
                  loading
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {loading ? 'Saving...' : editingClass ? 'Update Class' : 'Create Class'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Classes List */}
      {loading && !showForm ? (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-500 border-t-transparent"></div>
          <p className="mt-2 text-gray-600">Loading classes...</p>
        </div>
      ) : classes.length === 0 ? (
        <div className="bg-yellow-50 text-yellow-700 p-4 rounded-md">
          No classes found. Create your first class to get started.
        </div>
      ) : (
        <div className="space-y-4">
          {classes.map((cls) => (
            <div key={cls.id} className="border border-gray-200 rounded-md overflow-hidden">
              <div
                className="bg-gray-50 p-4 flex justify-between items-center cursor-pointer"
                onClick={() => toggleClassExpansion(cls.id)}
              >
                <div>
                  <h3 className="font-medium text-gray-800">{cls.name}</h3>
                  <p className="text-sm text-gray-500">
                    {studentCounts[cls.id] || 0} students
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditClass(cls);
                    }}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    Edit
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteClass(cls.id);
                    }}
                    className="text-red-600 hover:text-red-800"
                  >
                    Delete
                  </button>
                  <svg
                    className={`h-5 w-5 text-gray-500 transform transition-transform ${
                      expandedClass === cls.id ? 'rotate-180' : ''
                    }`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </div>

              {/* Expanded Class Details */}
              {expandedClass === cls.id && (
                <div className="p-4 border-t border-gray-200">
                  {cls.description && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-1">Description:</h4>
                      <p className="text-gray-600">{cls.description}</p>
                    </div>
                  )}

                  <h4 className="text-sm font-medium text-gray-700 mb-2">Students:</h4>

                  {loadingStudents ? (
                    <div className="text-center py-2">
                      <div className="inline-block animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent"></div>
                      <p className="mt-1 text-sm text-gray-600">Loading students...</p>
                    </div>
                  ) : classStudents.length === 0 ? (
                    <p className="text-gray-500 text-sm">No students in this class.</p>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full">
                        <thead className="bg-gray-100">
                          <tr>
                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th className="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          {classStudents.map((student) => (
                            <tr key={student.student_id}>
                              <td className="py-2 px-3 whitespace-nowrap">
                                {student.first_name} {student.last_name}
                              </td>
                              <td className="py-2 px-3 whitespace-nowrap">{student.email}</td>
                              <td className="py-2 px-3 whitespace-nowrap">{student.student_id_text}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
