import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { supabase } from '../../utils/supabaseClient';

export default function StudentForm({ onSuccess }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [classes, setClasses] = useState([]);
  const [loadingClasses, setLoadingClasses] = useState(true);
  const [showNewClassForm, setShowNewClassForm] = useState(false);
  const [newClassName, setNewClassName] = useState('');
  const [newClassDescription, setNewClassDescription] = useState('');

  const { register, handleSubmit, reset, formState: { errors } } = useForm();

  useEffect(() => {
    fetchClasses();
  }, []);

  const fetchClasses = async () => {
    try {
      setLoadingClasses(true);

      const { data, error } = await supabase
        .from('classes')
        .select('id, name')
        .order('name', { ascending: true });

      if (error) throw error;

      setClasses(data || []);
    } catch (err) {
      console.error('Error fetching classes:', err);
      setError('Failed to load classes');
    } finally {
      setLoadingClasses(false);
    }
  };

  // Function to generate a random password
  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  };

  const handleCreateClass = async () => {
    if (!newClassName.trim()) {
      return;
    }

    try {
      setLoading(true);

      // Insert the new class
      const { error } = await supabase
        .from('classes')
        .insert({
          name: newClassName.trim(),
          description: newClassDescription.trim()
        });

      if (error) throw error;

      // Refresh classes
      await fetchClasses();

      // Reset form
      setNewClassName('');
      setNewClassDescription('');
      setShowNewClassForm(false);
    } catch (err) {
      console.error('Error creating class:', err);
      setError(`Failed to create class: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      setError(null);

      // Generate a temporary password
      const temporaryPassword = generatePassword();

      // Create student record
      const studentData = {
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.email.toLowerCase(),
        student_id: data.studentId || `S${Math.floor(10000 + Math.random() * 90000)}`,
        course: data.course || 'General',
        semester: data.semester || '1',
        temporary_password: temporaryPassword
      };

      // Insert the student
      const { error: studentError } = await supabase
        .from('students')
        .insert(studentData);

      if (studentError) throw studentError;

      // Get the newly created student
      const { data: newStudent, error: fetchError } = await supabase
        .from('students')
        .select('*')
        .eq('email', studentData.email)
        .single();

      if (fetchError) {
        console.error('Error fetching new student:', fetchError);
        // Continue without creating the class relationship
      } else if (newStudent && data.classId && classes.length > 0) {
        // Create student_classes relationship
        try {
          const { error: relationError } = await supabase
            .from('student_classes')
            .insert({
              student_id: newStudent.id,
              class_id: data.classId
            });

          if (relationError) {
            console.error('Error creating student-class relationship:', relationError);
            // Continue without throwing an error
          }
        } catch (relationErr) {
          console.error('Exception creating student-class relationship:', relationErr);
          // Continue without throwing an error
        }
      }

      // Reset form
      reset();

      // Notify parent component
      if (onSuccess) {
        // Pass the student data even if we couldn't fetch the newly created student
        onSuccess(newStudent || studentData);
      }
    } catch (err) {
      console.error('Error creating student:', err);
      setError(`Failed to create student: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-6">Add New Student</h2>

      {error && (
        <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 font-medium mb-2">
              First Name*
            </label>
            <input
              type="text"
              {...register("firstName", { required: "First name is required" })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.firstName && (
              <p className="text-red-600 text-sm mt-1">{errors.firstName.message}</p>
            )}
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2">
              Last Name*
            </label>
            <input
              type="text"
              {...register("lastName", { required: "Last name is required" })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.lastName && (
              <p className="text-red-600 text-sm mt-1">{errors.lastName.message}</p>
            )}
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-gray-700 font-medium mb-2">
            Email*
          </label>
          <input
            type="email"
            {...register("email", {
              required: "Email is required",
              pattern: {
                value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                message: "Invalid email format"
              }
            })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.email && (
            <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 font-medium mb-2">
              Student ID (optional)
            </label>
            <input
              type="text"
              {...register("studentId")}
              placeholder="Will be generated if empty"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2">
              Class*
            </label>
            {loadingClasses ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent"></div>
                <span className="text-gray-500">Loading classes...</span>
              </div>
            ) : (
              <div>
                {classes.length > 0 ? (
                <>
                  <select
                    {...register("classId", { required: "Class is required" })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select a class</option>
                    {classes.map((cls) => (
                      <option key={cls.id} value={cls.id}>
                        {cls.name}
                      </option>
                    ))}
                  </select>
                  {errors.classId && (
                    <p className="text-red-600 text-sm mt-1">{errors.classId.message}</p>
                  )}
                </>
              ) : (
                <div className="bg-yellow-50 p-3 rounded-md">
                  <p className="text-yellow-700 text-sm">
                    No classes available. Please create a class below or go to the Class Management tab.
                  </p>
                </div>
              )}

                {!showNewClassForm ? (
                  <button
                    type="button"
                    onClick={() => setShowNewClassForm(true)}
                    className="text-blue-600 hover:text-blue-800 text-sm mt-1 flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Create new class
                  </button>
                ) : (
                  <div className="mt-2 p-3 border border-gray-200 rounded-md">
                    <div className="mb-2">
                      <label className="block text-gray-700 text-sm font-medium mb-1">
                        Class Name*
                      </label>
                      <input
                        type="text"
                        value={newClassName}
                        onChange={(e) => setNewClassName(e.target.value)}
                        className="w-full px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div className="mb-2">
                      <label className="block text-gray-700 text-sm font-medium mb-1">
                        Description
                      </label>
                      <input
                        type="text"
                        value={newClassDescription}
                        onChange={(e) => setNewClassDescription(e.target.value)}
                        className="w-full px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={handleCreateClass}
                        disabled={!newClassName.trim() || loading}
                        className={`px-3 py-1 text-sm rounded-md ${
                          !newClassName.trim() || loading
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-blue-600 text-white hover:bg-blue-700'
                        }`}
                      >
                        {loading ? 'Creating...' : 'Create'}
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowNewClassForm(false)}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-gray-700 font-medium mb-2">
              Course (optional)
            </label>
            <input
              type="text"
              {...register("course")}
              placeholder="e.g., Computer Science"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2">
              Semester (optional)
            </label>
            <input
              type="text"
              {...register("semester")}
              placeholder="e.g., 1"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div className="bg-blue-50 p-3 rounded-md mb-6">
          <p className="text-blue-700 text-sm">
            <strong>Note:</strong> A temporary password will be generated automatically.
            Students will be prompted to change it on first login.
          </p>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className={`px-4 py-2 rounded-md ${
              loading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {loading ? 'Creating...' : 'Create Student'}
          </button>
        </div>
      </form>
    </div>
  );
}
