import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '../../utils/supabaseClient';
import Webcam from 'react-webcam';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  loadFaceModels, 
  detectAndEncodeFace, 
  storeFaceDescriptor,
  verifyFace
} from '../../utils/faceRecognitionUtils';
import { compressImage } from '../../utils/imageUtils';

/**
 * Enhanced Student Management Component with Face Recognition
 * Allows administrators to register students with facial recognition
 */
export default function EnhancedStudentManagement() {
  // State for student data
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // State for face recognition
  const [modelsLoaded, setModelsLoaded] = useState(false);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [capturedImage, setCapturedImage] = useState(null);
  const [faceDetected, setFaceDetected] = useState(false);
  const [faceQuality, setFaceQuality] = useState(0);
  const [processingProgress, setProcessingProgress] = useState(0);
  
  // State for form
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    student_id: '',
    course: '',
    semester: '',
    class_id: ''
  });
  
  // State for classes
  const [classes, setClasses] = useState([]);
  const [selectedClass, setSelectedClass] = useState(null);
  
  // Refs
  const webcamRef = useRef(null);
  
  // Load face models on component mount
  useEffect(() => {
    const initFaceModels = async () => {
      try {
        const loaded = await loadFaceModels();
        setModelsLoaded(loaded);
        if (!loaded) {
          setError('Failed to load face recognition models. Some features may not work properly.');
        }
      } catch (err) {
        console.error('Error loading face models:', err);
        setError('Failed to initialize face recognition. Please refresh the page and try again.');
      }
    };
    
    initFaceModels();
  }, []);
  
  // Fetch students and classes on component mount
  useEffect(() => {
    fetchStudents();
    fetchClasses();
    
    // Set up real-time subscription for students
    const studentsSubscription = supabase
      .channel('students-changes')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'students'
      }, (payload) => {
        setStudents(prevStudents => [...prevStudents, payload.new]);
        setSuccess('New student added: ' + payload.new.first_name + ' ' + payload.new.last_name);
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(studentsSubscription);
    };
  }, []);
  
  // Fetch students from database
  const fetchStudents = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('students')
        .select(`
          *,
          classes (
            id,
            name
          )
        `)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      setStudents(data || []);
    } catch (err) {
      console.error('Error fetching students:', err);
      setError('Failed to load students. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch classes from database
  const fetchClasses = async () => {
    try {
      const { data, error } = await supabase
        .from('classes')
        .select('*')
        .order('name');
      
      if (error) throw error;
      
      setClasses(data || []);
    } catch (err) {
      console.error('Error fetching classes:', err);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle class selection
  const handleClassChange = (e) => {
    const classId = e.target.value;
    setSelectedClass(classId);
    setFormData(prev => ({ ...prev, class_id: classId }));
  };
  
  // Open camera for face capture
  const openCamera = () => {
    setIsCameraOpen(true);
    setCapturedImage(null);
    setFaceDetected(false);
    setFaceQuality(0);
  };
  
  // Capture image from webcam
  const captureImage = useCallback(async () => {
    if (!webcamRef.current) return;
    
    try {
      const imageSrc = webcamRef.current.getScreenshot();
      setCapturedImage(imageSrc);
      
      // Create an image element from the captured image
      const img = new Image();
      img.src = imageSrc;
      await new Promise(resolve => { img.onload = resolve; });
      
      // Detect face in the image
      const faceData = await detectAndEncodeFace(img);
      
      if (faceData) {
        setFaceDetected(true);
        setFaceQuality(faceData.quality * 100);
        setSuccess('Face detected successfully!');
      } else {
        setFaceDetected(false);
        setFaceQuality(0);
        setError('No face detected. Please try again.');
      }
    } catch (err) {
      console.error('Error capturing image:', err);
      setError('Failed to process the captured image. Please try again.');
    }
  }, [webcamRef]);
  
  // Register a new student
  const registerStudent = async (e) => {
    e.preventDefault();
    
    // Validate form data
    if (!formData.first_name || !formData.last_name || !formData.student_id) {
      setError('Please fill in all required fields.');
      return;
    }
    
    if (!capturedImage || !faceDetected) {
      setError('Please capture a valid face image first.');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      setProcessingProgress(10);
      
      // Create an image element from the captured image
      const img = new Image();
      img.src = capturedImage;
      await new Promise(resolve => { img.onload = resolve; });
      
      // Detect face in the image
      const faceData = await detectAndEncodeFace(img);
      
      if (!faceData) {
        throw new Error('No face detected in the image.');
      }
      
      setProcessingProgress(30);
      
      // Convert data URL to blob for upload
      const imageBlob = dataURLtoBlob(capturedImage);
      
      // Compress the image
      const compressedBlob = await compressImage(imageBlob, 0.8);
      
      setProcessingProgress(50);
      
      // Upload the image to Supabase Storage
      const fileName = `${Date.now()}_${formData.student_id}.webp`;
      const filePath = `students/${fileName}`;
      
      const { error: uploadError } = await supabase.storage
        .from('student_photos')
        .upload(filePath, compressedBlob, {
          contentType: 'image/webp',
          upsert: true
        });
      
      if (uploadError) throw uploadError;
      
      setProcessingProgress(70);
      
      // Get the public URL of the uploaded image
      const { data: urlData } = supabase.storage
        .from('student_photos')
        .getPublicUrl(filePath);
      
      // Insert the student into the database
      const { data: studentData, error: insertError } = await supabase
        .from('students')
        .insert([
          {
            ...formData,
            photo_path: filePath,
            reference_image_url: urlData.publicUrl,
            face_quality: faceData.quality
          }
        ])
        .select()
        .single();
      
      if (insertError) throw insertError;
      
      setProcessingProgress(90);
      
      // Store the face descriptor
      await storeFaceDescriptor(studentData.id, faceData.descriptor, faceData.quality);
      
      setProcessingProgress(100);
      
      // If a class is selected, assign the student to the class
      if (formData.class_id) {
        await supabase
          .from('student_classes')
          .insert([
            {
              student_id: studentData.id,
              class_id: formData.class_id
            }
          ]);
      }
      
      // Reset form and state
      setFormData({
        first_name: '',
        last_name: '',
        email: '',
        student_id: '',
        course: '',
        semester: '',
        class_id: ''
      });
      setCapturedImage(null);
      setFaceDetected(false);
      setFaceQuality(0);
      setIsCameraOpen(false);
      
      // Show success message
      setSuccess('Student registered successfully!');
      
      // Refresh students list
      fetchStudents();
    } catch (err) {
      console.error('Error registering student:', err);
      setError('Failed to register student: ' + err.message);
    } finally {
      setLoading(false);
      setProcessingProgress(0);
    }
  };
  
  // Verify a student's face
  const handleVerifyFace = async (studentId) => {
    if (!webcamRef.current) {
      setError('Camera not available. Please refresh the page and try again.');
      return;
    }
    
    try {
      setLoading(true);
      
      // Capture image from webcam
      const imageSrc = webcamRef.current.getScreenshot();
      
      // Create an image element from the captured image
      const img = new Image();
      img.src = imageSrc;
      await new Promise(resolve => { img.onload = resolve; });
      
      // Detect face in the image
      const faceData = await detectAndEncodeFace(img);
      
      if (!faceData) {
        throw new Error('No face detected. Please try again.');
      }
      
      // Verify the face
      const { match, confidence } = await verifyFace(studentId, faceData.descriptor);
      
      if (match) {
        setSuccess(`Identity verified! Confidence: ${(confidence * 100).toFixed(2)}%`);
      } else {
        setError(`Identity verification failed. Confidence: ${(confidence * 100).toFixed(2)}%`);
      }
    } catch (err) {
      console.error('Error verifying face:', err);
      setError('Failed to verify face: ' + err.message);
    } finally {
      setLoading(false);
    }
  };
  
  // Helper function to convert data URL to Blob
  const dataURLtoBlob = (dataURL) => {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
  };
  
  // Get quality message based on face quality score
  const getQualityMessage = (quality) => {
    if (quality > 90) return 'Excellent';
    if (quality > 70) return 'Good';
    if (quality > 50) return 'Acceptable';
    return 'Poor';
  };
  
  // Get quality color based on face quality score
  const getQualityColor = (quality) => {
    if (quality > 90) return 'text-green-600';
    if (quality > 70) return 'text-green-500';
    if (quality > 50) return 'text-yellow-500';
    return 'text-red-500';
  };
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Enhanced Student Management</h1>
      
      {/* Error and Success Messages */}
      <AnimatePresence>
        {error && (
          <motion.div 
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
          >
            <span className="font-bold">Error:</span> {error}
            <button 
              className="float-right"
              onClick={() => setError(null)}
            >
              &times;
            </button>
          </motion.div>
        )}
        
        {success && (
          <motion.div 
            className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
          >
            <span className="font-bold">Success:</span> {success}
            <button 
              className="float-right"
              onClick={() => setSuccess(null)}
            >
              &times;
            </button>
          </motion.div>
        )}
      </AnimatePresence>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Registration Form */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Register New Student</h2>
          
          <form onSubmit={registerStudent}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Student ID <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="student_id"
                  value={formData.student_id}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Course
                </label>
                <input
                  type="text"
                  name="course"
                  value={formData.course}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Semester
                </label>
                <input
                  type="text"
                  name="semester"
                  value={formData.semester}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Class
                </label>
                <select
                  name="class_id"
                  value={formData.class_id}
                  onChange={handleClassChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">-- Select Class --</option>
                  {classes.map(cls => (
                    <option key={cls.id} value={cls.id}>
                      {cls.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Face Photo <span className="text-red-500">*</span>
              </label>
              
              {!capturedImage ? (
                <button
                  type="button"
                  onClick={openCamera}
                  className="w-full p-3 border-2 border-dashed border-gray-300 rounded-md text-center hover:border-blue-500 hover:text-blue-500 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Click to Capture Face Photo
                </button>
              ) : (
                <div className="relative">
                  <img 
                    src={capturedImage} 
                    alt="Captured face" 
                    className="w-full h-48 object-cover rounded-md"
                  />
                  
                  {faceDetected && (
                    <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-sm">
                      Face Detected
                    </div>
                  )}
                  
                  <div className="mt-2 flex justify-between items-center">
                    <div>
                      <span className="text-sm text-gray-700">Quality: </span>
                      <span className={`text-sm font-medium ${getQualityColor(faceQuality)}`}>
                        {getQualityMessage(faceQuality)} ({Math.round(faceQuality)}%)
                      </span>
                    </div>
                    
                    <button
                      type="button"
                      onClick={openCamera}
                      className="text-blue-500 hover:text-blue-700 text-sm"
                    >
                      Retake Photo
                    </button>
                  </div>
                </div>
              )}
            </div>
            
            <button
              type="submit"
              className="w-full bg-blue-600 text-white p-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400"
              disabled={loading || !faceDetected}
            >
              {loading ? 'Registering...' : 'Register Student'}
            </button>
            
            {loading && processingProgress > 0 && (
              <div className="mt-2">
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Processing</span>
                  <span>{processingProgress}%</span>
                </div>
                <div className="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-blue-500"
                    style={{ width: `${processingProgress}%` }}
                  ></div>
                </div>
              </div>
            )}
          </form>
        </div>
        
        {/* Camera Modal */}
        <AnimatePresence>
          {isCameraOpen && (
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="bg-white rounded-lg shadow-lg p-6 max-w-lg w-full"
                initial={{ scale: 0.9, y: 20 }}
                animate={{ scale: 1, y: 0 }}
                exit={{ scale: 0.9, y: 20 }}
              >
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Capture Face Photo</h2>
                  <button
                    type="button"
                    onClick={() => setIsCameraOpen(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <div className="relative">
                  <Webcam
                    audio={false}
                    ref={webcamRef}
                    screenshotFormat="image/jpeg"
                    videoConstraints={{
                      width: 640,
                      height: 480,
                      facingMode: "user"
                    }}
                    className="w-full rounded-lg"
                  />
                  
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-64 h-64 border-4 border-dashed border-blue-500 rounded-full opacity-70"></div>
                  </div>
                </div>
                
                <div className="mt-4 flex justify-center">
                  <button
                    type="button"
                    onClick={captureImage}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    Capture Photo
                  </button>
                </div>
                
                <p className="mt-2 text-sm text-gray-500 text-center">
                  Position your face within the circle and ensure good lighting.
                </p>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Students List */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Registered Students</h2>
          
          {loading && students.length === 0 ? (
            <div className="text-center py-4">
              <svg className="animate-spin h-8 w-8 mx-auto text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="mt-2 text-gray-500">Loading students...</p>
            </div>
          ) : students.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              No students registered yet.
            </div>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {students.map(student => (
                <div key={student.id} className="border border-gray-200 rounded-md p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      {student.reference_image_url ? (
                        <img 
                          src={student.reference_image_url} 
                          alt={`${student.first_name} ${student.last_name}`}
                          className="w-16 h-16 object-cover rounded-full"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                      )}
                    </div>
                    
                    <div className="ml-4 flex-1">
                      <h3 className="text-lg font-medium">{student.first_name} {student.last_name}</h3>
                      <p className="text-sm text-gray-500">ID: {student.student_id}</p>
                      {student.course && (
                        <p className="text-sm text-gray-500">{student.course}, {student.semester}</p>
                      )}
                      {student.classes && student.classes.name && (
                        <p className="text-sm text-gray-500">Class: {student.classes.name}</p>
                      )}
                    </div>
                    
                    <div>
                      <button
                        type="button"
                        onClick={() => handleVerifyFace(student.id)}
                        className="text-blue-600 hover:text-blue-800"
                        disabled={loading}
                      >
                        Verify Face
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
