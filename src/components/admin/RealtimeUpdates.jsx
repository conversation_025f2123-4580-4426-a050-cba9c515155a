import { useState, useEffect } from 'react';
import { supabase } from '../../utils/supabaseClient';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * Component that displays real-time updates from the database
 * Shows notifications for student photo updates, attendance events, etc.
 */
export default function RealtimeUpdates() {
  const [updates, setUpdates] = useState([]);
  const [showNotification, setShowNotification] = useState(false);

  useEffect(() => {
    // Subscribe to photo updates
    const photoSubscription = supabase
      .channel('photo-updates')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'photo_updates'
      }, (payload) => {
        handleUpdate({
          id: payload.new.id,
          type: 'photo',
          subtype: payload.new.update_type,
          studentId: payload.new.student_id,
          timestamp: payload.new.timestamp,
          data: payload.new
        });
      })
      .subscribe();

    // Subscribe to attendance updates
    const attendanceSubscription = supabase
      .channel('attendance-updates')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'attendance'
      }, (payload) => {
        handleUpdate({
          id: payload.new.id,
          type: 'attendance',
          subtype: payload.new.status ? 'present' : 'absent',
          studentId: payload.new.student_id,
          timestamp: payload.new.timestamp,
          data: payload.new
        });
      })
      .subscribe();

    // Cleanup function
    return () => {
      supabase.removeChannel(photoSubscription);
      supabase.removeChannel(attendanceSubscription);
    };
  }, []);

  // Handle new updates
  const handleUpdate = async (update) => {
    // Fetch student details
    const { data: student } = await supabase
      .from('students')
      .select('first_name, last_name')
      .eq('id', update.studentId)
      .single();

    if (student) {
      const newUpdate = {
        ...update,
        studentName: `${student.first_name} ${student.last_name}`
      };

      // Add to updates list
      setUpdates(prevUpdates => [newUpdate, ...prevUpdates].slice(0, 10));
      
      // Show notification
      setShowNotification(true);
      
      // Hide notification after 5 seconds
      setTimeout(() => {
        setShowNotification(false);
      }, 5000);
    }
  };

  // Format update message
  const formatUpdateMessage = (update) => {
    switch (update.type) {
      case 'photo':
        return `${update.studentName}'s photo was ${update.subtype === 'upload' ? 'uploaded' : 'updated'}`;
      case 'attendance':
        return `${update.studentName} marked as ${update.subtype}`;
      default:
        return `Update for ${update.studentName}`;
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="relative">
      {/* Floating notification */}
      <AnimatePresence>
        {showNotification && updates.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="fixed top-4 right-4 bg-white shadow-lg rounded-lg p-4 z-50 max-w-md"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {updates[0].type === 'photo' ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">
                  {formatUpdateMessage(updates[0])}
                </p>
                <p className="text-xs text-gray-500">
                  {formatTimestamp(updates[0].timestamp)}
                </p>
              </div>
              <button
                onClick={() => setShowNotification(false)}
                className="ml-auto text-gray-400 hover:text-gray-500"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Updates list */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <h2 className="text-lg font-semibold mb-4">Recent Updates</h2>
        {updates.length === 0 ? (
          <p className="text-gray-500 text-sm">No recent updates</p>
        ) : (
          <ul className="space-y-3">
            {updates.map(update => (
              <li key={update.id} className="flex items-center p-2 hover:bg-gray-50 rounded-md">
                <div className="flex-shrink-0">
                  {update.type === 'photo' ? (
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {formatUpdateMessage(update)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatTimestamp(update.timestamp)}
                  </p>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
