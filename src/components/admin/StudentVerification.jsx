import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '../../utils/supabaseClient';
import Webcam from 'react-webcam';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  loadFaceModels, 
  detectAndEncodeFace, 
  verifyFace
} from '../../utils/faceRecognitionUtils';

/**
 * Student Verification Component
 * Used for verifying student identity and marking attendance
 */
export default function StudentVerification({ classId = null }) {
  // State for students
  const [students, setStudents] = useState([]);
  const [filteredStudents, setFilteredStudents] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  
  // State for verification
  const [verificationResult, setVerificationResult] = useState(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // State for attendance
  const [attendanceRecords, setAttendanceRecords] = useState([]);
  const [attendanceDate, setAttendanceDate] = useState(new Date().toISOString().split('T')[0]);
  
  // Refs
  const webcamRef = useRef(null);
  
  // Load face models and fetch students on component mount
  useEffect(() => {
    const init = async () => {
      try {
        // Load face models
        await loadFaceModels();
        
        // Fetch students
        await fetchStudents();
        
        // Fetch attendance records for today
        await fetchAttendanceRecords(attendanceDate);
      } catch (err) {
        console.error('Error initializing:', err);
        setError('Failed to initialize. Please refresh the page and try again.');
      }
    };
    
    init();
    
    // Set up real-time subscription for attendance
    const attendanceSubscription = supabase
      .channel('attendance-changes')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'attendance'
      }, (payload) => {
        setAttendanceRecords(prev => [...prev, payload.new]);
        
        // Show success message if it's for the selected student
        if (selectedStudent && payload.new.student_id === selectedStudent.id) {
          setSuccess(`Attendance recorded for ${selectedStudent.first_name} ${selectedStudent.last_name}`);
        }
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(attendanceSubscription);
    };
  }, []);
  
  // Update filtered students when search query changes
  useEffect(() => {
    if (!searchQuery) {
      setFilteredStudents(students);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const filtered = students.filter(student => 
      student.first_name.toLowerCase().includes(query) ||
      student.last_name.toLowerCase().includes(query) ||
      student.student_id.toLowerCase().includes(query)
    );
    
    setFilteredStudents(filtered);
  }, [searchQuery, students]);
  
  // Fetch students from database
  const fetchStudents = async () => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('students')
        .select(`
          *,
          classes (
            id,
            name
          )
        `)
        .order('last_name');
      
      // Filter by class if classId is provided
      if (classId) {
        query = query.eq('class_id', classId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      setStudents(data || []);
      setFilteredStudents(data || []);
    } catch (err) {
      console.error('Error fetching students:', err);
      setError('Failed to load students. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch attendance records for a specific date
  const fetchAttendanceRecords = async (date) => {
    try {
      // Convert date to start and end of day
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const { data, error } = await supabase
        .from('attendance')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());
      
      if (error) throw error;
      
      setAttendanceRecords(data || []);
    } catch (err) {
      console.error('Error fetching attendance records:', err);
      setError('Failed to load attendance records. Please try again.');
    }
  };
  
  // Handle date change
  const handleDateChange = (e) => {
    const newDate = e.target.value;
    setAttendanceDate(newDate);
    fetchAttendanceRecords(newDate);
  };
  
  // Handle student selection
  const handleStudentSelect = (student) => {
    setSelectedStudent(student);
    setVerificationResult(null);
  };
  
  // Open camera for verification
  const openCamera = () => {
    if (!selectedStudent) {
      setError('Please select a student first.');
      return;
    }
    
    setIsCameraOpen(true);
    setVerificationResult(null);
  };
  
  // Verify student's face
  const verifyStudentFace = useCallback(async () => {
    if (!webcamRef.current || !selectedStudent) {
      setError('Camera or student not available.');
      return;
    }
    
    try {
      setLoading(true);
      
      // Capture image from webcam
      const imageSrc = webcamRef.current.getScreenshot();
      
      // Create an image element from the captured image
      const img = new Image();
      img.src = imageSrc;
      await new Promise(resolve => { img.onload = resolve; });
      
      // Detect face in the image
      const faceData = await detectAndEncodeFace(img);
      
      if (!faceData) {
        throw new Error('No face detected. Please try again.');
      }
      
      // Verify the face
      const { match, confidence } = await verifyFace(selectedStudent.id, faceData.descriptor);
      
      // Set verification result
      setVerificationResult({
        match,
        confidence,
        timestamp: new Date().toISOString(),
        imageSrc
      });
      
      // Close camera
      setIsCameraOpen(false);
      
      // Show success or error message
      if (match) {
        setSuccess(`Identity verified for ${selectedStudent.first_name} ${selectedStudent.last_name}!`);
      } else {
        setError(`Identity verification failed for ${selectedStudent.first_name} ${selectedStudent.last_name}.`);
      }
    } catch (err) {
      console.error('Error verifying face:', err);
      setError('Failed to verify face: ' + err.message);
    } finally {
      setLoading(false);
    }
  }, [webcamRef, selectedStudent]);
  
  // Mark attendance
  const markAttendance = async (present = true) => {
    if (!selectedStudent) {
      setError('Please select a student first.');
      return;
    }
    
    if (!verificationResult) {
      setError('Please verify the student\'s identity first.');
      return;
    }
    
    try {
      setLoading(true);
      
      // Check if attendance already recorded for today
      const isAlreadyRecorded = attendanceRecords.some(record => 
        record.student_id === selectedStudent.id
      );
      
      if (isAlreadyRecorded) {
        setError('Attendance already recorded for this student today.');
        return;
      }
      
      // Record attendance
      const { error } = await supabase
        .from('attendance')
        .insert([
          {
            student_id: selectedStudent.id,
            class_id: classId,
            status: present,
            verification_method: 'face',
            confidence: verificationResult.confidence,
            date: attendanceDate
          }
        ]);
      
      if (error) throw error;
      
      // Show success message
      setSuccess(`Attendance ${present ? 'present' : 'absent'} recorded for ${selectedStudent.first_name} ${selectedStudent.last_name}`);
      
      // Reset state
      setVerificationResult(null);
      setSelectedStudent(null);
    } catch (err) {
      console.error('Error recording attendance:', err);
      setError('Failed to record attendance: ' + err.message);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Student Verification & Attendance</h1>
      
      {/* Error and Success Messages */}
      <AnimatePresence>
        {error && (
          <motion.div 
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
          >
            <span className="font-bold">Error:</span> {error}
            <button 
              className="float-right"
              onClick={() => setError(null)}
            >
              &times;
            </button>
          </motion.div>
        )}
        
        {success && (
          <motion.div 
            className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
          >
            <span className="font-bold">Success:</span> {success}
            <button 
              className="float-right"
              onClick={() => setSuccess(null)}
            >
              &times;
            </button>
          </motion.div>
        )}
      </AnimatePresence>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Student Selection */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Select Student</h2>
          
          <div className="mb-4">
            <input
              type="text"
              placeholder="Search by name or ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div className="max-h-96 overflow-y-auto">
            {loading && filteredStudents.length === 0 ? (
              <div className="text-center py-4">
                <svg className="animate-spin h-8 w-8 mx-auto text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p className="mt-2 text-gray-500">Loading students...</p>
              </div>
            ) : filteredStudents.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No students found.
              </div>
            ) : (
              <div className="space-y-2">
                {filteredStudents.map(student => {
                  // Check if attendance is already recorded
                  const isPresent = attendanceRecords.some(record => 
                    record.student_id === student.id && record.status === true
                  );
                  
                  const isAbsent = attendanceRecords.some(record => 
                    record.student_id === student.id && record.status === false
                  );
                  
                  return (
                    <div 
                      key={student.id} 
                      className={`border rounded-md p-3 cursor-pointer transition-colors ${
                        selectedStudent?.id === student.id 
                          ? 'border-blue-500 bg-blue-50' 
                          : isPresent
                            ? 'border-green-500 bg-green-50'
                            : isAbsent
                              ? 'border-red-500 bg-red-50'
                              : 'border-gray-200 hover:border-blue-300'
                      }`}
                      onClick={() => handleStudentSelect(student)}
                    >
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          {student.reference_image_url ? (
                            <img 
                              src={student.reference_image_url} 
                              alt={`${student.first_name} ${student.last_name}`}
                              className="w-12 h-12 object-cover rounded-full"
                            />
                          ) : (
                            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                          )}
                        </div>
                        
                        <div className="ml-3">
                          <h3 className="text-sm font-medium">{student.first_name} {student.last_name}</h3>
                          <p className="text-xs text-gray-500">ID: {student.student_id}</p>
                        </div>
                        
                        {isPresent && (
                          <div className="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                            Present
                          </div>
                        )}
                        
                        {isAbsent && (
                          <div className="ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                            Absent
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
        
        {/* Verification */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Verify Identity</h2>
          
          {selectedStudent ? (
            <div>
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  {selectedStudent.reference_image_url ? (
                    <img 
                      src={selectedStudent.reference_image_url} 
                      alt={`${selectedStudent.first_name} ${selectedStudent.last_name}`}
                      className="w-16 h-16 object-cover rounded-full"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  )}
                </div>
                
                <div className="ml-4">
                  <h3 className="text-lg font-medium">{selectedStudent.first_name} {selectedStudent.last_name}</h3>
                  <p className="text-sm text-gray-500">ID: {selectedStudent.student_id}</p>
                  {selectedStudent.course && (
                    <p className="text-sm text-gray-500">{selectedStudent.course}, {selectedStudent.semester}</p>
                  )}
                </div>
              </div>
              
              {verificationResult ? (
                <div className="mb-4">
                  <div className="border rounded-md p-4">
                    <div className="flex items-center mb-2">
                      <div className={`w-4 h-4 rounded-full ${verificationResult.match ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <h3 className={`ml-2 font-medium ${verificationResult.match ? 'text-green-700' : 'text-red-700'}`}>
                        {verificationResult.match ? 'Identity Verified' : 'Verification Failed'}
                      </h3>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">
                      Confidence: {(verificationResult.confidence * 100).toFixed(2)}%
                    </p>
                    
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={() => markAttendance(true)}
                        className="bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 disabled:bg-gray-400"
                        disabled={loading || !verificationResult.match}
                      >
                        Mark Present
                      </button>
                      
                      <button
                        type="button"
                        onClick={() => markAttendance(false)}
                        className="bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700 disabled:bg-gray-400"
                        disabled={loading}
                      >
                        Mark Absent
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mb-4">
                  <button
                    type="button"
                    onClick={openCamera}
                    className="w-full bg-blue-600 text-white p-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400"
                    disabled={loading}
                  >
                    Verify Identity
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Select a student to verify their identity.
            </div>
          )}
          
          <div className="mt-6">
            <h3 className="text-lg font-medium mb-2">Attendance Date</h3>
            <input
              type="date"
              value={attendanceDate}
              onChange={handleDateChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div className="mt-4">
            <h3 className="text-lg font-medium mb-2">Attendance Summary</h3>
            <div className="flex space-x-4">
              <div className="bg-green-100 rounded-md p-3 flex-1 text-center">
                <p className="text-2xl font-bold text-green-700">
                  {attendanceRecords.filter(record => record.status === true).length}
                </p>
                <p className="text-sm text-green-700">Present</p>
              </div>
              
              <div className="bg-red-100 rounded-md p-3 flex-1 text-center">
                <p className="text-2xl font-bold text-red-700">
                  {attendanceRecords.filter(record => record.status === false).length}
                </p>
                <p className="text-sm text-red-700">Absent</p>
              </div>
              
              <div className="bg-gray-100 rounded-md p-3 flex-1 text-center">
                <p className="text-2xl font-bold text-gray-700">
                  {students.length - attendanceRecords.length}
                </p>
                <p className="text-sm text-gray-700">Not Marked</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Camera Modal */}
      <AnimatePresence>
        {isCameraOpen && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-lg shadow-lg p-6 max-w-lg w-full"
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
            >
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Verify Identity</h2>
                <button
                  type="button"
                  onClick={() => setIsCameraOpen(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="relative">
                <Webcam
                  audio={false}
                  ref={webcamRef}
                  screenshotFormat="image/jpeg"
                  videoConstraints={{
                    width: 640,
                    height: 480,
                    facingMode: "user"
                  }}
                  className="w-full rounded-lg"
                />
                
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-64 h-64 border-4 border-dashed border-blue-500 rounded-full opacity-70"></div>
                </div>
              </div>
              
              <div className="mt-4 flex justify-center">
                <button
                  type="button"
                  onClick={verifyStudentFace}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400"
                  disabled={loading}
                >
                  {loading ? 'Verifying...' : 'Verify Identity'}
                </button>
              </div>
              
              <p className="mt-2 text-sm text-gray-500 text-center">
                Position your face within the circle and ensure good lighting.
              </p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
