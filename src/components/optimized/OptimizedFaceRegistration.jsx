import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Webcam from 'react-webcam';
import { 
  CameraIcon, 
  DocumentArrowUpIcon,
  CheckCircleIcon, 
  XCircleIcon,
  ClockIcon,
  ChartBarIcon,
  BoltIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';
import { 
  registerFaceOptimized, 
  getPerformanceMetrics,
  cleanup
} from '../../utils/optimizedFaceRegistration';

/**
 * OptimizedFaceRegistration Component
 * High-performance face registration with <1s processing target
 */
export default function OptimizedFaceRegistration({ onRegistrationComplete, onCancel }) {
  const [studentData, setStudentData] = useState({
    studentId: '',
    name: '',
    email: ''
  });
  
  const [captureMode, setCaptureMode] = useState('webcam');
  const [capturedPhoto, setCapturedPhoto] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [registering, setRegistering] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [performanceMetrics, setPerformanceMetrics] = useState(null);
  const [realTimeMetrics, setRealTimeMetrics] = useState(null);
  
  const webcamRef = useRef(null);
  const fileInputRef = useRef(null);
  
  // Load performance metrics on mount
  useEffect(() => {
    const metrics = getPerformanceMetrics();
    setPerformanceMetrics(metrics);
    
    return () => {
      cleanup(); // Clean up worker on unmount
    };
  }, []);
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setStudentData(prev => ({ ...prev, [name]: value }));
    setError(null);
  };
  
  // Validate form data
  const validateForm = () => {
    if (!studentData.studentId.trim()) {
      setError('Student ID is required');
      return false;
    }
    
    if (!studentData.name.trim()) {
      setError('Student name is required');
      return false;
    }
    
    if (!studentData.email.trim()) {
      setError('Email is required');
      return false;
    }
    
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(studentData.email)) {
      setError('Please enter a valid email address');
      return false;
    }
    
    return true;
  };
  
  // Capture photo from webcam
  const capturePhoto = () => {
    const imageSrc = webcamRef.current.getScreenshot();
    setCapturedPhoto(imageSrc);
    setUploadedFile(null);
    setError(null);
  };
  
  // Handle file upload
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setUploadedFile(file);
      setCapturedPhoto(null);
      setError(null);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setCapturedPhoto(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Convert data URL to file
  const dataURLtoFile = (dataurl, filename) => {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    
    return new File([u8arr], filename, { type: mime });
  };
  
  // Handle optimized registration
  const handleOptimizedRegistration = async () => {
    if (!validateForm()) return;
    
    if (!capturedPhoto && !uploadedFile) {
      setError('Please capture a photo or upload an image');
      return;
    }
    
    setRegistering(true);
    setError(null);
    setResult(null);
    setRealTimeMetrics(null);
    
    try {
      // Prepare image file
      let imageFile;
      if (uploadedFile) {
        imageFile = uploadedFile;
      } else {
        imageFile = dataURLtoFile(capturedPhoto, `${studentData.studentId}_capture.jpg`);
      }
      
      console.log('🚀 Starting optimized face registration...');
      const startTime = performance.now();
      
      // Use optimized registration pipeline
      const registrationResult = await registerFaceOptimized(studentData, imageFile);
      
      const totalTime = performance.now() - startTime;
      
      // Update real-time metrics
      setRealTimeMetrics({
        totalTime,
        targetAchieved: totalTime < 1000,
        ...registrationResult.performance
      });
      
      setResult(registrationResult);
      
      if (registrationResult.success && onRegistrationComplete) {
        onRegistrationComplete(registrationResult);
      }
      
    } catch (err) {
      console.error('Optimized registration error:', err);
      setError(err.message || 'Registration failed');
    } finally {
      setRegistering(false);
    }
  };
  
  // Reset form
  const resetForm = () => {
    setStudentData({ studentId: '', name: '', email: '' });
    setCapturedPhoto(null);
    setUploadedFile(null);
    setResult(null);
    setError(null);
    setRealTimeMetrics(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Get performance color based on time
  const getPerformanceColor = (time) => {
    if (time < 1000) return 'text-green-600';
    if (time < 2000) return 'text-yellow-600';
    return 'text-red-600';
  };
  
  return (
    <div className="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-2">
          <BoltIcon className="h-8 w-8 text-yellow-500 mr-2" />
          <h2 className="text-2xl font-bold text-gray-900">Optimized Face Registration</h2>
        </div>
        <p className="text-gray-600">High-performance pipeline targeting &lt;1s processing time</p>
        
        {/* Performance Status */}
        {performanceMetrics && (
          <div className="mt-4 flex justify-center space-x-4 text-sm">
            <div className={`flex items-center ${performanceMetrics.workerReady ? 'text-green-600' : 'text-red-600'}`}>
              <div className={`w-2 h-2 rounded-full mr-1 ${performanceMetrics.workerReady ? 'bg-green-500' : 'bg-red-500'}`}></div>
              Web Worker: {performanceMetrics.workerReady ? 'Ready' : 'Not Available'}
            </div>
            <div className={`flex items-center ${performanceMetrics.webpSupported ? 'text-green-600' : 'text-yellow-600'}`}>
              <div className={`w-2 h-2 rounded-full mr-1 ${performanceMetrics.webpSupported ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
              WebP: {performanceMetrics.webpSupported ? 'Supported' : 'Fallback'}
            </div>
          </div>
        )}
      </div>
      
      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4"
          >
            <div className="flex items-center">
              <XCircleIcon className="h-5 w-5 mr-2" />
              {error}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Student Information */}
        <div className="lg:col-span-1 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Student Information</h3>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Student ID *
            </label>
            <input
              type="text"
              name="studentId"
              value={studentData.studentId}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., E22273735500014"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Full Name *
            </label>
            <input
              type="text"
              name="name"
              value={studentData.name}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Anupam"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email Address *
            </label>
            <input
              type="email"
              name="email"
              value={studentData.email}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., <EMAIL>"
              required
            />
          </div>
          
          {/* Real-time Performance Metrics */}
          {realTimeMetrics && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                <ChartBarIcon className="h-4 w-4 mr-1" />
                Performance Metrics
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Total Time:</span>
                  <span className={`font-medium ${getPerformanceColor(realTimeMetrics.totalTime)}`}>
                    {realTimeMetrics.totalTime.toFixed(0)}ms
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Target (&lt;1s):</span>
                  <span className={realTimeMetrics.targetAchieved ? 'text-green-600' : 'text-red-600'}>
                    {realTimeMetrics.targetAchieved ? '✅ Achieved' : '❌ Missed'}
                  </span>
                </div>
                {realTimeMetrics.compressionTime && (
                  <div className="flex justify-between">
                    <span>Compression:</span>
                    <span>{realTimeMetrics.compressionTime.toFixed(0)}ms</span>
                  </div>
                )}
                {realTimeMetrics.detectionTime && (
                  <div className="flex justify-between">
                    <span>Detection:</span>
                    <span>{realTimeMetrics.detectionTime.toFixed(0)}ms</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        
        {/* Image Capture */}
        <div className="lg:col-span-2 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Face Capture</h3>
          
          {/* Capture Mode Selection */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => setCaptureMode('webcam')}
              className={`px-4 py-2 rounded-lg flex items-center ${
                captureMode === 'webcam'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <CameraIcon className="h-4 w-4 mr-2" />
              Webcam
            </button>
            <button
              onClick={() => setCaptureMode('upload')}
              className={`px-4 py-2 rounded-lg flex items-center ${
                captureMode === 'upload'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <DocumentArrowUpIcon className="h-4 w-4 mr-2" />
              Upload
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Capture/Upload Section */}
            <div className="space-y-4">
              {captureMode === 'webcam' ? (
                <>
                  <div className="relative">
                    <Webcam
                      ref={webcamRef}
                      audio={false}
                      screenshotFormat="image/jpeg"
                      className="w-full rounded-lg border-4 border-blue-200"
                      width={320}
                      height={240}
                    />
                    <div className="absolute inset-0 border-2 border-dashed border-blue-400 rounded-lg pointer-events-none">
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 border-2 border-blue-500 rounded-full opacity-50"></div>
                    </div>
                  </div>
                  <button
                    onClick={capturePhoto}
                    disabled={registering}
                    className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
                  >
                    <CameraIcon className="h-5 w-5 mr-2" />
                    Capture Photo
                  </button>
                </>
              ) : (
                <>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <PhotoIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600 mb-2">Upload optimized image</p>
                    <p className="text-xs text-gray-500 mb-3">
                      WebP, JPEG, PNG (auto-compressed)
                    </p>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      disabled={registering}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                    >
                      Choose File
                    </button>
                  </div>
                </>
              )}
            </div>
            
            {/* Preview Section */}
            <div className="space-y-4">
              <div className="w-full h-[240px] bg-gray-100 rounded-lg border-4 border-gray-200 flex items-center justify-center">
                {capturedPhoto ? (
                  <img
                    src={capturedPhoto}
                    alt="Preview"
                    className="max-w-full max-h-full rounded-lg"
                  />
                ) : (
                  <div className="text-center text-gray-500">
                    <PhotoIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No image captured</p>
                  </div>
                )}
              </div>
              
              {capturedPhoto && (
                <button
                  onClick={handleOptimizedRegistration}
                  disabled={registering}
                  className="w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
                >
                  {registering ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <BoltIcon className="h-5 w-5 mr-2" />
                      Register Face (Optimized)
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* Results */}
      <AnimatePresence>
        {result && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="mt-6"
          >
            <div className={`p-6 rounded-lg border ${
              result.success 
                ? 'bg-green-50 border-green-200 text-green-800' 
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <div className="flex items-center mb-4">
                {result.success ? (
                  <CheckCircleIcon className="h-8 w-8 mr-3" />
                ) : (
                  <XCircleIcon className="h-8 w-8 mr-3" />
                )}
                <div>
                  <h4 className="text-lg font-bold">{result.message}</h4>
                  {result.success && (
                    <p className="text-sm">
                      Processed in {result.performance.totalTime.toFixed(0)}ms 
                      {result.performance.targetAchieved && ' ⚡ Target achieved!'}
                    </p>
                  )}
                </div>
              </div>
              
              {result.success && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <div className="font-medium">Detection Score</div>
                    <div>{(result.quality.detectionScore * 100).toFixed(1)}%</div>
                  </div>
                  <div>
                    <div className="font-medium">Quality Score</div>
                    <div>{(result.quality.qualityScore * 100).toFixed(1)}%</div>
                  </div>
                  <div>
                    <div className="font-medium">Compression</div>
                    <div>{result.optimization.compressionRatio.toFixed(1)}%</div>
                  </div>
                  <div>
                    <div className="font-medium">Method</div>
                    <div className="capitalize">{result.optimization.method}</div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Action Buttons */}
      <div className="flex justify-between mt-6">
        <div>
          {onCancel && (
            <button
              onClick={onCancel}
              disabled={registering}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
            >
              Cancel
            </button>
          )}
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={resetForm}
            disabled={registering}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 disabled:opacity-50"
          >
            Reset
          </button>
        </div>
      </div>
    </div>
  );
}
