import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  UserGroupIcon, 
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  EyeIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { CSVLink } from 'react-csv';
import { getVerificationStatistics, getStudentVerificationHistory } from '../../utils/faceVerificationService';

/**
 * VerificationDashboard Component
 * Comprehensive dashboard for face verification analytics and monitoring
 */
export default function VerificationDashboard() {
  const [stats, setStats] = useState(null);
  const [verificationLogs, setVerificationLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState(30);
  const [selectedStudent, setSelectedStudent] = useState('');
  
  useEffect(() => {
    loadDashboardData();
  }, [timeRange]);
  
  // Load dashboard data
  const loadDashboardData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Load verification statistics
      const statsResult = await getVerificationStatistics(timeRange);
      
      if (statsResult.success) {
        setStats(statsResult.stats);
      } else {
        throw new Error(statsResult.error);
      }
      
      // Load mock verification logs
      await loadVerificationLogs();
      
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  // Load verification logs (mock data for development)
  const loadVerificationLogs = async () => {
    try {
      // Generate mock verification logs
      const mockLogs = [];
      const students = ['S1001', 'S1002', 'S1003', 'TEST001', 'TEST002'];
      const names = ['Anupam', 'Prakhar', 'Aakash', 'Test User 1', 'Test User 2'];
      const statuses = ['valid', 'no_face', 'multiple_faces', 'poor_quality'];
      const messages = {
        'valid': 'Student face successfully verified and stored.',
        'no_face': 'No face found, please try again.',
        'multiple_faces': 'Multiple faces detected, please upload a photo with only one face.',
        'poor_quality': 'Face detection quality too low, please try again with better lighting.'
      };
      
      for (let i = 0; i < 25; i++) {
        const studentIndex = Math.floor(Math.random() * students.length);
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const facesDetected = status === 'no_face' ? 0 : 
                            status === 'multiple_faces' ? Math.floor(Math.random() * 3) + 2 : 1;
        const detectionScore = status === 'valid' ? Math.random() * 0.2 + 0.8 : Math.random() * 0.6;
        
        mockLogs.push({
          id: `mock_${i}`,
          student_id: students[studentIndex],
          student_name: names[studentIndex],
          image_url: `mock://verification_${i}.jpg`,
          faces_detected: facesDetected,
          face_detection_score: parseFloat(detectionScore.toFixed(3)),
          image_quality_score: Math.random() * 0.3 + 0.7,
          verification_status: status,
          message: messages[status],
          processing_time_ms: Math.floor(Math.random() * 2000) + 500,
          created_at: new Date(Date.now() - Math.random() * timeRange * 24 * 60 * 60 * 1000).toISOString()
        });
      }
      
      setVerificationLogs(mockLogs.sort((a, b) => new Date(b.created_at) - new Date(a.created_at)));
    } catch (err) {
      console.error('Error loading verification logs:', err);
    }
  };
  
  // Get status color and icon
  const getStatusDisplay = (status) => {
    switch (status) {
      case 'valid':
        return {
          color: 'text-green-600 bg-green-100',
          icon: <CheckCircleIcon className="h-4 w-4" />,
          text: 'Valid'
        };
      case 'no_face':
        return {
          color: 'text-red-600 bg-red-100',
          icon: <XCircleIcon className="h-4 w-4" />,
          text: 'No Face'
        };
      case 'multiple_faces':
        return {
          color: 'text-orange-600 bg-orange-100',
          icon: <ExclamationTriangleIcon className="h-4 w-4" />,
          text: 'Multiple Faces'
        };
      case 'poor_quality':
        return {
          color: 'text-yellow-600 bg-yellow-100',
          icon: <ExclamationTriangleIcon className="h-4 w-4" />,
          text: 'Poor Quality'
        };
      default:
        return {
          color: 'text-gray-600 bg-gray-100',
          icon: <XCircleIcon className="h-4 w-4" />,
          text: 'Unknown'
        };
    }
  };
  
  // Filter logs by selected student
  const filteredLogs = selectedStudent 
    ? verificationLogs.filter(log => log.student_id === selectedStudent)
    : verificationLogs;
  
  // Prepare CSV data
  const csvData = filteredLogs.map(log => ({
    'Date': new Date(log.created_at).toLocaleDateString(),
    'Time': new Date(log.created_at).toLocaleTimeString(),
    'Student ID': log.student_id,
    'Student Name': log.student_name,
    'Faces Detected': log.faces_detected,
    'Detection Score': log.face_detection_score,
    'Quality Score': log.image_quality_score,
    'Status': log.verification_status,
    'Message': log.message,
    'Processing Time (ms)': log.processing_time_ms
  }));
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Face Verification Dashboard</h1>
          <p className="text-gray-600">Monitor face verification performance and analytics</p>
        </div>
        
        <div className="flex space-x-3">
          <CSVLink
            data={csvData}
            filename={`face-verification-logs-${new Date().toISOString().split('T')[0]}.csv`}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
            Export Logs
          </CSVLink>
        </div>
      </div>
      
      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Time Range
            </label>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(parseInt(e.target.value))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={7}>Last 7 days</option>
              <option value={30}>Last 30 days</option>
              <option value={90}>Last 90 days</option>
              <option value={365}>Last year</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Student Filter
            </label>
            <select
              value={selectedStudent}
              onChange={(e) => setSelectedStudent(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Students</option>
              <option value="S1001">Anupam (S1001)</option>
              <option value="S1002">Prakhar (S1002)</option>
              <option value="S1003">Aakash (S1003)</option>
              <option value="TEST001">Test User 1 (TEST001)</option>
              <option value="TEST002">Test User 2 (TEST002)</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* Statistics Cards */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      ) : stats ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-md p-6"
          >
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <ChartBarIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Attempts</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_attempts}</p>
              </div>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-lg shadow-md p-6"
          >
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircleIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">{stats.success_rate}%</p>
              </div>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-lg shadow-md p-6"
          >
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <UserGroupIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Detection</p>
                <p className="text-2xl font-bold text-gray-900">{(stats.avg_detection_score * 100).toFixed(1)}%</p>
              </div>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-lg shadow-md p-6"
          >
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <ClockIcon className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Time</p>
                <p className="text-2xl font-bold text-gray-900">{stats.avg_processing_time_ms}ms</p>
              </div>
            </div>
          </motion.div>
        </div>
      ) : null}
      
      {/* Detailed Statistics */}
      {stats && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Verification Breakdown</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="bg-green-50 p-3 rounded">
              <div className="font-medium text-green-700">Valid Verifications</div>
              <div className="text-lg font-bold text-green-600">{stats.valid_attempts}</div>
            </div>
            <div className="bg-red-50 p-3 rounded">
              <div className="font-medium text-red-700">No Face Detected</div>
              <div className="text-lg font-bold text-red-600">{stats.no_face_attempts}</div>
            </div>
            <div className="bg-orange-50 p-3 rounded">
              <div className="font-medium text-orange-700">Multiple Faces</div>
              <div className="text-lg font-bold text-orange-600">{stats.multiple_faces_attempts}</div>
            </div>
            <div className="bg-yellow-50 p-3 rounded">
              <div className="font-medium text-yellow-700">Poor Quality</div>
              <div className="text-lg font-bold text-yellow-600">{stats.poor_quality_attempts}</div>
            </div>
          </div>
        </div>
      )}
      
      {/* Verification Logs */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Recent Verification Attempts ({filteredLogs.length})
          </h3>
        </div>
        
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading verification logs...</span>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Faces
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Detection Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Processing Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredLogs.slice(0, 20).map((log, index) => {
                  const statusDisplay = getStatusDisplay(log.verification_status);
                  return (
                    <motion.tr
                      key={log.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="hover:bg-gray-50"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {log.student_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {log.student_id}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {log.faces_detected}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {(log.face_detection_score * 100).toFixed(1)}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${statusDisplay.color}`}>
                          {statusDisplay.icon}
                          <span className="ml-1">{statusDisplay.text}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {log.processing_time_ms}ms
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(log.created_at).toLocaleDateString()} {new Date(log.created_at).toLocaleTimeString()}
                      </td>
                    </motion.tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
        
        {!loading && filteredLogs.length === 0 && (
          <div className="text-center py-12">
            <EyeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No verification logs found for the selected filters.</p>
          </div>
        )}
      </div>
    </div>
  );
}
