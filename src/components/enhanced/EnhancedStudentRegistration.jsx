import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  UserPlusIcon, 
  CameraIcon,
  DocumentArrowUpIcon,
  CheckCircleIcon, 
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  AcademicCapIcon,
  PhotoIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import {
  initializeCamera,
  validateRegistrationForm,
  registerStudentEnhanced,
  getCoursesAndClasses,
  logRegistrationStep,
  cleanupCamera
} from '../../utils/enhancedRegistrationService';

/**
 * EnhancedStudentRegistration Component
 * Reliable face registration flow with optimized camera handling
 */
export default function EnhancedStudentRegistration({ onRegistrationComplete, onCancel }) {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    student_id: '',
    course: '',
    semester: 1,
    class: ''
  });
  
  const [coursesData, setCoursesData] = useState({ courses: [] });
  const [availableClasses, setAvailableClasses] = useState([]);
  const [step, setStep] = useState(1); // 1: Form, 2: Camera, 3: Results
  const [errors, setErrors] = useState([]);
  const [registering, setRegistering] = useState(false);
  const [result, setResult] = useState(null);
  
  // Camera states
  const [cameraInitialized, setCameraInitialized] = useState(false);
  const [cameraError, setCameraError] = useState(null);
  const [capturedPhoto, setCapturedPhoto] = useState(null);
  const [captureMode, setCaptureMode] = useState('webcam');
  const [uploadedFile, setUploadedFile] = useState(null);
  
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);
  
  // Load courses and classes on mount
  useEffect(() => {
    loadCoursesAndClasses();
    
    return () => {
      cleanupCamera(videoRef);
    };
  }, []);
  
  // Update available classes when course or semester changes
  useEffect(() => {
    updateAvailableClasses();
  }, [formData.course, formData.semester, coursesData]);
  
  const loadCoursesAndClasses = async () => {
    try {
      const data = await getCoursesAndClasses();
      if (data.success) {
        setCoursesData(data);
      }
    } catch (error) {
      console.error('Failed to load courses:', error);
    }
  };
  
  const updateAvailableClasses = () => {
    if (!formData.course || !formData.semester) {
      setAvailableClasses([]);
      return;
    }
    
    const selectedCourse = coursesData.courses.find(c => c.name === formData.course);
    if (selectedCourse) {
      const classes = selectedCourse.classes.filter(cl => cl.semester === parseInt(formData.semester));
      setAvailableClasses(classes);
    }
  };
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setErrors([]);
  };
  
  const validateAndProceed = async () => {
    const validation = validateRegistrationForm(formData);
    
    if (!validation.valid) {
      setErrors(validation.errors);
      return;
    }
    
    // Log form submission
    await logRegistrationStep(formData.student_id, 'form_submitted', formData);
    
    setErrors([]);
    setStep(2);
    
    // Initialize camera
    setTimeout(() => {
      initializeCameraWithRetry();
    }, 500);
  };
  
  const initializeCameraWithRetry = async () => {
    setCameraError(null);
    setCameraInitialized(false);
    
    await logRegistrationStep(formData.student_id, 'camera_initialized');
    
    const result = await initializeCamera(videoRef, (error) => {
      setCameraError(error);
      logRegistrationStep(formData.student_id, 'failed', null, error.message);
    });
    
    if (result.success) {
      setCameraInitialized(true);
    }
  };
  
  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) return;
    
    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    ctx.drawImage(video, 0, 0);
    
    canvas.toBlob((blob) => {
      const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
      setCapturedPhoto(dataUrl);
      setUploadedFile(blob);
      
      logRegistrationStep(formData.student_id, 'face_captured', {
        width: canvas.width,
        height: canvas.height,
        size: blob.size
      });
    }, 'image/jpeg', 0.9);
  };
  
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setUploadedFile(file);
      setCapturedPhoto(null);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setCapturedPhoto(e.target.result);
      };
      reader.readAsDataURL(file);
      
      logRegistrationStep(formData.student_id, 'face_captured', {
        fileName: file.name,
        size: file.size,
        type: file.type
      });
    }
  };
  
  const retakePhoto = () => {
    setCapturedPhoto(null);
    setUploadedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const handleRegistration = async () => {
    if (!uploadedFile) {
      setErrors(['Please capture a photo or upload an image']);
      return;
    }
    
    setRegistering(true);
    setErrors([]);
    
    try {
      await logRegistrationStep(formData.student_id, 'face_processed');
      
      const registrationResult = await registerStudentEnhanced(formData, uploadedFile);
      
      if (registrationResult.success) {
        await logRegistrationStep(formData.student_id, 'completed', {
          processingTime: registrationResult.processingTime,
          faceData: registrationResult.faceData
        });
        
        setResult(registrationResult);
        setStep(3);
        
        if (onRegistrationComplete) {
          onRegistrationComplete(registrationResult);
        }
      } else {
        await logRegistrationStep(formData.student_id, 'failed', null, registrationResult.error);
        setErrors([registrationResult.error]);
      }
    } catch (error) {
      await logRegistrationStep(formData.student_id, 'failed', null, error.message);
      setErrors([error.message]);
    } finally {
      setRegistering(false);
    }
  };
  
  const resetForm = () => {
    setFormData({
      first_name: '',
      last_name: '',
      email: '',
      student_id: '',
      course: '',
      semester: 1,
      class: ''
    });
    setCapturedPhoto(null);
    setUploadedFile(null);
    setResult(null);
    setErrors([]);
    setStep(1);
    setCameraError(null);
    setCameraInitialized(false);
    cleanupCamera(videoRef);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  return (
    <div className="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <AcademicCapIcon className="h-12 w-12 text-blue-600 mx-auto mb-2" />
        <h2 className="text-2xl font-bold text-gray-900">Enhanced Student Registration</h2>
        <p className="text-gray-600">Reliable face registration with optimized camera handling</p>
      </div>
      
      {/* Progress Indicator */}
      <div className="flex items-center justify-center mb-8">
        {[1, 2, 3].map((stepNumber) => (
          <div key={stepNumber} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step >= stepNumber 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-600'
            }`}>
              {stepNumber}
            </div>
            {stepNumber < 3 && (
              <div className={`w-16 h-1 mx-2 ${
                step > stepNumber ? 'bg-blue-600' : 'bg-gray-200'
              }`} />
            )}
          </div>
        ))}
      </div>
      
      {/* Error Messages */}
      <AnimatePresence>
        {errors.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4"
          >
            <div className="flex items-start">
              <XCircleIcon className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                {errors.map((error, index) => (
                  <div key={index}>{error}</div>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Step Content */}
      <AnimatePresence mode="wait">
        {step === 1 && (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Student Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name *
                </label>
                <input
                  type="text"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter first name"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name *
                </label>
                <input
                  type="text"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter last name"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter email address"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Student ID *
                </label>
                <input
                  type="text"
                  name="student_id"
                  value={formData.student_id}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., E22273735500014"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Format: Letter followed by 12-14 digits</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Course *
                </label>
                <select
                  name="course"
                  value={formData.course}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Select Course</option>
                  {coursesData.courses.map(course => (
                    <option key={course.id} value={course.name}>
                      {course.name} ({course.code})
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Semester *
                </label>
                <select
                  name="semester"
                  value={formData.semester}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  {[1, 2, 3, 4, 5, 6, 7, 8].map(sem => (
                    <option key={sem} value={sem}>Semester {sem}</option>
                  ))}
                </select>
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Class *
                </label>
                <select
                  name="class"
                  value={formData.class}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                  disabled={availableClasses.length === 0}
                >
                  <option value="">
                    {availableClasses.length === 0 ? 'Select course and semester first' : 'Select Class'}
                  </option>
                  {availableClasses.map(cls => (
                    <option key={cls.id} value={cls.name}>
                      {cls.name} (Academic Year: {cls.academic_year})
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">📋 Registration Requirements</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• All fields marked with * are required</li>
                <li>• Student ID must follow format: Letter + 12-14 digits</li>
                <li>• Email must be from an approved domain</li>
                <li>• Face photo will be captured in the next step</li>
              </ul>
            </div>
          </motion.div>
        )}
        
        {step === 2 && (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Face Registration</h3>
            
            {/* Camera Error */}
            {cameraError && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                <div className="flex items-start">
                  <ExclamationTriangleIcon className="h-5 w-5 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium">{cameraError.type === 'camera_error' ? 'Camera Error' : 'Error'}</p>
                    <p className="text-sm">{cameraError.message}</p>
                    {cameraError.canRetry && (
                      <button
                        onClick={initializeCameraWithRetry}
                        className="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                      >
                        Retry Camera
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            {/* Capture Mode Selection */}
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setCaptureMode('webcam')}
                className={`px-4 py-2 rounded-lg flex items-center ${
                  captureMode === 'webcam'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <CameraIcon className="h-4 w-4 mr-2" />
                Webcam
              </button>
              <button
                onClick={() => setCaptureMode('upload')}
                className={`px-4 py-2 rounded-lg flex items-center ${
                  captureMode === 'upload'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <DocumentArrowUpIcon className="h-4 w-4 mr-2" />
                Upload Photo
              </button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Camera/Upload Section */}
              <div className="space-y-4">
                {captureMode === 'webcam' ? (
                  <>
                    <h4 className="font-medium text-gray-900">Live Camera</h4>
                    <div className="relative">
                      <video
                        ref={videoRef}
                        autoPlay
                        playsInline
                        className="w-full rounded-lg border-4 border-blue-200"
                        style={{ maxHeight: '400px' }}
                      />
                      <canvas ref={canvasRef} className="hidden" />
                      
                      {!cameraInitialized && !cameraError && (
                        <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                            <p className="text-gray-600">Initializing camera...</p>
                          </div>
                        </div>
                      )}
                      
                      {cameraInitialized && (
                        <div className="absolute inset-0 border-2 border-dashed border-blue-400 rounded-lg pointer-events-none">
                          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 border-2 border-blue-500 rounded-full opacity-50"></div>
                        </div>
                      )}
                    </div>
                    
                    <button
                      onClick={capturePhoto}
                      disabled={!cameraInitialized || registering}
                      className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
                    >
                      <CameraIcon className="h-5 w-5 mr-2" />
                      Capture Photo
                    </button>
                  </>
                ) : (
                  <>
                    <h4 className="font-medium text-gray-900">Upload Photo</h4>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                      <PhotoIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">Upload a clear photo of your face</p>
                      <p className="text-sm text-gray-500 mb-4">
                        Minimum 500x500px, good lighting required
                      </p>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        disabled={registering}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                      >
                        Choose File
                      </button>
                    </div>
                  </>
                )}
              </div>
              
              {/* Preview Section */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Preview</h4>
                <div className="w-full h-[300px] bg-gray-100 rounded-lg border-4 border-gray-200 flex items-center justify-center">
                  {capturedPhoto ? (
                    <img
                      src={capturedPhoto}
                      alt="Captured face"
                      className="max-w-full max-h-full rounded-lg"
                    />
                  ) : (
                    <div className="text-center text-gray-500">
                      <PhotoIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>No image captured</p>
                    </div>
                  )}
                </div>
                
                {capturedPhoto && (
                  <div className="flex space-x-3">
                    <button
                      onClick={retakePhoto}
                      disabled={registering}
                      className="flex-1 bg-gray-600 text-white px-4 py-3 rounded-lg hover:bg-gray-700 disabled:opacity-50 flex items-center justify-center"
                    >
                      <ArrowPathIcon className="h-5 w-5 mr-2" />
                      Retake
                    </button>
                    
                    <button
                      onClick={handleRegistration}
                      disabled={registering}
                      className="flex-1 bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
                    >
                      {registering ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Registering...
                        </>
                      ) : (
                        <>
                          <UserPlusIcon className="h-5 w-5 mr-2" />
                          Register Student
                        </>
                      )}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
        
        {step === 3 && result && (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="text-center"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-6">Registration Results</h3>
            
            <div className={`p-6 rounded-lg border ${
              result.success 
                ? 'bg-green-50 border-green-200 text-green-800' 
                : 'bg-red-50 border-red-200 text-red-800'
            } mb-6`}>
              <div className="flex items-center justify-center mb-4">
                {result.success ? (
                  <CheckCircleIcon className="h-8 w-8" />
                ) : (
                  <XCircleIcon className="h-8 w-8" />
                )}
              </div>
              
              <h4 className="text-xl font-bold mb-2">{result.message}</h4>
              
              {result.success && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 text-sm">
                  <div>
                    <div className="font-medium">Student ID</div>
                    <div>{result.studentId}</div>
                  </div>
                  <div>
                    <div className="font-medium">Database ID</div>
                    <div>{result.databaseId}</div>
                  </div>
                  <div>
                    <div className="font-medium">Status</div>
                    <div className="capitalize">{result.registrationStatus}</div>
                  </div>
                  <div>
                    <div className="font-medium">Processing Time</div>
                    <div>{result.processingTime}ms</div>
                  </div>
                </div>
              )}
              
              {result.faceData && (
                <div className="mt-4 text-sm">
                  <p>Face Detection Score: {(result.faceData.detectionScore * 100).toFixed(1)}%</p>
                  <p>Image Quality Score: {(result.faceData.qualityScore * 100).toFixed(1)}%</p>
                </div>
              )}
            </div>
            
            <div className="flex space-x-4 justify-center">
              <button
                onClick={resetForm}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center"
              >
                <UserPlusIcon className="h-5 w-5 mr-2" />
                Register Another Student
              </button>
              
              {onCancel && (
                <button
                  onClick={onCancel}
                  className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700"
                >
                  Back to Dashboard
                </button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Navigation Buttons */}
      {step < 3 && (
        <div className="flex justify-between mt-8">
          <div>
            {step > 1 && (
              <button
                onClick={() => {
                  setStep(step - 1);
                  if (step === 2) {
                    cleanupCamera(videoRef);
                    setCameraInitialized(false);
                    setCameraError(null);
                  }
                }}
                disabled={registering}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
              >
                ← Previous
              </button>
            )}
            
            {onCancel && step === 1 && (
              <button
                onClick={onCancel}
                disabled={registering}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
              >
                Cancel
              </button>
            )}
          </div>
          
          <div>
            {step === 1 && (
              <button
                onClick={validateAndProceed}
                disabled={registering}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                Next →
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
