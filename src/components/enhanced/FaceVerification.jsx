import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ShieldCheckIcon,
  CameraIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import {
  initializeCamera,
  processFaceFromImage,
  cleanupCamera,
  logRegistrationStep
} from '../../utils/enhancedRegistrationService';

/**
 * FaceVerification Component
 * Verify student identity using face recognition
 */
export default function FaceVerification({ 
  studentId, 
  studentName,
  onVerificationComplete,
  onRetry,
  similarityThreshold = 0.6,
  maxAttempts = 3
}) {
  const [cameraInitialized, setCameraInitialized] = useState(false);
  const [cameraError, setCameraError] = useState(null);
  const [verifying, setVerifying] = useState(false);
  const [result, setResult] = useState(null);
  const [attempts, setAttempts] = useState(0);
  const [capturedPhoto, setCapturedPhoto] = useState(null);
  
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  
  useEffect(() => {
    initializeCameraForVerification();
    
    return () => {
      cleanupCamera(videoRef);
    };
  }, []);
  
  const initializeCameraForVerification = async () => {
    setCameraError(null);
    setCameraInitialized(false);
    
    const result = await initializeCamera(videoRef, (error) => {
      setCameraError(error);
      logRegistrationStep(studentId, 'failed', null, `Camera error: ${error.message}`);
    });
    
    if (result.success) {
      setCameraInitialized(true);
      await logRegistrationStep(studentId, 'camera_initialized', {
        purpose: 'verification',
        studentId: studentId
      });
    }
  };
  
  const captureAndVerify = async () => {
    if (!videoRef.current || !canvasRef.current) return;
    
    setVerifying(true);
    setResult(null);
    
    try {
      // Capture photo
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0);
      
      const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
      setCapturedPhoto(dataUrl);
      
      await logRegistrationStep(studentId, 'face_captured', {
        purpose: 'verification',
        attempt: attempts + 1,
        maxAttempts: maxAttempts
      });
      
      // Create image element for processing
      const imageElement = await new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = dataUrl;
      });
      
      // Process face
      const faceResult = await processFaceFromImage(imageElement);
      
      if (!faceResult.success) {
        throw new Error(faceResult.error);
      }
      
      // Simulate face comparison (in real implementation, compare with stored descriptor)
      const mockSimilarityScore = Math.random() * 0.4 + 0.6; // 0.6 to 1.0
      const isMatch = mockSimilarityScore >= similarityThreshold;
      
      const verificationResult = {
        success: isMatch,
        studentId: studentId,
        studentName: studentName,
        similarityScore: mockSimilarityScore,
        threshold: similarityThreshold,
        detectionScore: faceResult.detectionScore,
        attempt: attempts + 1,
        capturedImage: dataUrl,
        message: isMatch 
          ? `Identity verified successfully! Similarity: ${(mockSimilarityScore * 100).toFixed(1)}%`
          : `Identity verification failed. Similarity: ${(mockSimilarityScore * 100).toFixed(1)}% (Required: ${(similarityThreshold * 100).toFixed(1)}%)`
      };
      
      setResult(verificationResult);
      setAttempts(prev => prev + 1);
      
      await logRegistrationStep(studentId, isMatch ? 'completed' : 'failed', {
        purpose: 'verification',
        similarityScore: mockSimilarityScore,
        threshold: similarityThreshold,
        attempt: attempts + 1,
        success: isMatch
      }, isMatch ? null : verificationResult.message);
      
      if (onVerificationComplete) {
        onVerificationComplete(verificationResult);
      }
      
    } catch (error) {
      console.error('Verification error:', error);
      
      const errorResult = {
        success: false,
        error: error.message,
        attempt: attempts + 1,
        studentId: studentId
      };
      
      setResult(errorResult);
      setAttempts(prev => prev + 1);
      
      await logRegistrationStep(studentId, 'failed', {
        purpose: 'verification',
        attempt: attempts + 1
      }, error.message);
      
    } finally {
      setVerifying(false);
    }
  };
  
  const retryVerification = () => {
    setResult(null);
    setCapturedPhoto(null);
    
    if (onRetry) {
      onRetry();
    }
  };
  
  const canRetry = attempts < maxAttempts && (!result || !result.success);
  
  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <ShieldCheckIcon className="h-12 w-12 text-green-600 mx-auto mb-2" />
        <h3 className="text-xl font-bold text-gray-900">Face Verification</h3>
        <p className="text-gray-600">
          Verify identity for: <span className="font-medium">{studentName || studentId}</span>
        </p>
        <p className="text-sm text-gray-500 mt-1">
          Attempt {attempts + 1} of {maxAttempts}
        </p>
      </div>
      
      {/* Camera Error */}
      <AnimatePresence>
        {cameraError && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4"
          >
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 mr-2 mt-0.5" />
              <div>
                <p className="font-medium">Camera Error</p>
                <p className="text-sm">{cameraError.message}</p>
                {cameraError.canRetry && (
                  <button
                    onClick={initializeCameraForVerification}
                    className="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                  >
                    Retry Camera
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Verification Result */}
      <AnimatePresence>
        {result && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className={`px-4 py-3 rounded-lg mb-4 ${
              result.success 
                ? 'bg-green-50 border border-green-200 text-green-700'
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}
          >
            <div className="flex items-center">
              {result.success ? (
                <CheckCircleIcon className="h-5 w-5 mr-2" />
              ) : (
                <XCircleIcon className="h-5 w-5 mr-2" />
              )}
              <div>
                <p className="font-medium">
                  {result.success ? 'Verification Successful' : 'Verification Failed'}
                </p>
                <p className="text-sm">{result.message || result.error}</p>
                {result.similarityScore && (
                  <p className="text-xs mt-1">
                    Detection Score: {(result.detectionScore * 100).toFixed(1)}%
                  </p>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Camera Section */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Live Camera</h4>
          
          <div className="relative">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              className="w-full rounded-lg border-4 border-green-200"
              style={{ maxHeight: '400px' }}
            />
            <canvas ref={canvasRef} className="hidden" />
            
            {!cameraInitialized && !cameraError && (
              <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-2"></div>
                  <p className="text-gray-600">Initializing camera...</p>
                </div>
              </div>
            )}
            
            {cameraInitialized && (
              <div className="absolute inset-0 border-2 border-dashed border-green-400 rounded-lg pointer-events-none">
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 border-2 border-green-500 rounded-full opacity-50"></div>
              </div>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={captureAndVerify}
              disabled={!cameraInitialized || verifying || (result && result.success)}
              className="flex-1 bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
            >
              {verifying ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Verifying...
                </>
              ) : (
                <>
                  <ShieldCheckIcon className="h-5 w-5 mr-2" />
                  Verify Now
                </>
              )}
            </button>
            
            {canRetry && (
              <button
                onClick={retryVerification}
                disabled={verifying}
                className="bg-gray-600 text-white px-4 py-3 rounded-lg hover:bg-gray-700 disabled:opacity-50 flex items-center justify-center"
              >
                <ArrowPathIcon className="h-5 w-5 mr-2" />
                Retry
              </button>
            )}
          </div>
        </div>
        
        {/* Preview/Result Section */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Verification Preview</h4>
          
          <div className="w-full h-[300px] bg-gray-100 rounded-lg border-4 border-gray-200 flex items-center justify-center">
            {capturedPhoto ? (
              <img
                src={capturedPhoto}
                alt="Verification capture"
                className="max-w-full max-h-full rounded-lg"
              />
            ) : (
              <div className="text-center text-gray-500">
                <UserIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No verification image captured</p>
              </div>
            )}
          </div>
          
          {/* Verification Details */}
          {result && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h5 className="font-medium text-gray-900 mb-2">Verification Details</h5>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Student ID:</span>
                  <span className="font-medium">{result.studentId}</span>
                </div>
                {result.similarityScore && (
                  <>
                    <div className="flex justify-between">
                      <span>Similarity Score:</span>
                      <span className="font-medium">{(result.similarityScore * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Required Threshold:</span>
                      <span className="font-medium">{(result.threshold * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Detection Score:</span>
                      <span className="font-medium">{(result.detectionScore * 100).toFixed(1)}%</span>
                    </div>
                  </>
                )}
                <div className="flex justify-between">
                  <span>Attempt:</span>
                  <span className="font-medium">{result.attempt} of {maxAttempts}</span>
                </div>
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className={`font-medium ${result.success ? 'text-green-600' : 'text-red-600'}`}>
                    {result.success ? 'Verified' : 'Failed'}
                  </span>
                </div>
              </div>
            </div>
          )}
          
          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="font-medium text-blue-900 mb-2">Verification Instructions</h5>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Position your face in the center of the frame</li>
              <li>• Ensure good lighting on your face</li>
              <li>• Remove sunglasses or face coverings</li>
              <li>• Look directly at the camera</li>
              <li>• Keep your face steady during capture</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Max Attempts Reached */}
      {attempts >= maxAttempts && (!result || !result.success) && (
        <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircleIcon className="h-5 w-5 text-red-600 mr-2" />
            <div>
              <p className="font-medium text-red-900">Maximum Attempts Reached</p>
              <p className="text-sm text-red-800">
                Verification failed after {maxAttempts} attempts. Please contact support for assistance.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
