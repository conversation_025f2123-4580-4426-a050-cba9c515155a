import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  DocumentArrowUpIcon,
  PhotoIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowUpTrayIcon
} from '@heroicons/react/24/outline';
import { 
  validateFaceImageQuality,
  processFaceFromImage,
  uploadFaceImage,
  logRegistrationStep
} from '../../utils/enhancedRegistrationService';

/**
 * FallbackPhotoUpload Component
 * Handles offline face processing when camera fails
 */
export default function FallbackPhotoUpload({ 
  studentId, 
  onUpload, 
  onCancel,
  maxFileSize = 5 * 1024 * 1024, // 5MB
  acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
}) {
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [dragOver, setDragOver] = useState(false);
  
  const fileInputRef = useRef(null);
  const dropZoneRef = useRef(null);
  
  // Handle file selection
  const handleFileSelect = (file) => {
    setError(null);
    setResult(null);
    
    // Validate file type
    if (!acceptedFormats.includes(file.type)) {
      setError(`Invalid file type. Please upload: ${acceptedFormats.join(', ')}`);
      return;
    }
    
    // Validate file size
    if (file.size > maxFileSize) {
      setError(`File too large. Maximum size: ${(maxFileSize / (1024 * 1024)).toFixed(1)}MB`);
      return;
    }
    
    setSelectedFile(file);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target.result);
    };
    reader.readAsDataURL(file);
    
    // Log file selection
    logRegistrationStep(studentId, 'face_captured', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      method: 'fallback_upload'
    });
  };
  
  // Handle file input change
  const handleFileInputChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };
  
  // Handle drag and drop
  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };
  
  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };
  
  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };
  
  // Process uploaded face
  const processUploadedFace = async () => {
    if (!selectedFile) return;
    
    setProcessing(true);
    setError(null);
    setResult(null);
    
    try {
      console.log('🔄 Processing uploaded face image...');
      
      // Create image element for processing
      const imageElement = await new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = URL.createObjectURL(selectedFile);
      });
      
      // Validate image quality
      console.log('📏 Validating image quality...');
      const qualityValidation = await validateFaceImageQuality(imageElement);
      
      if (!qualityValidation.valid) {
        throw new Error(qualityValidation.error);
      }
      
      // Process face
      console.log('🔍 Processing face...');
      const faceResult = await processFaceFromImage(imageElement);
      
      if (!faceResult.success) {
        throw new Error(faceResult.error);
      }
      
      // Upload image
      console.log('☁️ Uploading image...');
      const uploadResult = await uploadFaceImage(selectedFile, studentId);
      
      if (!uploadResult.success) {
        throw new Error('Failed to upload image: ' + uploadResult.error);
      }
      
      const processResult = {
        success: true,
        descriptor: faceResult.descriptor,
        detectionScore: faceResult.detectionScore,
        qualityScore: qualityValidation.sharpness,
        imageUrl: uploadResult.url,
        imagePath: uploadResult.path,
        faceBox: faceResult.faceBox,
        imageDimensions: qualityValidation.dimensions
      };
      
      setResult(processResult);
      
      // Log successful processing
      await logRegistrationStep(studentId, 'face_processed', {
        detectionScore: faceResult.detectionScore,
        qualityScore: qualityValidation.sharpness,
        imageDimensions: qualityValidation.dimensions,
        method: 'fallback_upload'
      });
      
      console.log('✅ Face processing completed successfully');
      
      // Call upload callback
      if (onUpload) {
        onUpload(processResult);
      }
      
    } catch (error) {
      console.error('❌ Face processing failed:', error);
      setError(error.message);
      
      // Log processing error
      await logRegistrationStep(studentId, 'failed', null, error.message);
    } finally {
      setProcessing(false);
    }
  };
  
  // Clear selection
  const clearSelection = () => {
    setSelectedFile(null);
    setPreview(null);
    setResult(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  return (
    <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <DocumentArrowUpIcon className="h-12 w-12 text-orange-600 mx-auto mb-2" />
        <h3 className="text-xl font-bold text-gray-900">Fallback Photo Upload</h3>
        <p className="text-gray-600">Camera unavailable - Upload a photo instead</p>
      </div>
      
      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4"
          >
            <div className="flex items-center">
              <XCircleIcon className="h-5 w-5 mr-2" />
              {error}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Success Message */}
      <AnimatePresence>
        {result && result.success && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4"
          >
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              Face processed successfully! Detection score: {(result.detectionScore * 100).toFixed(1)}%
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {!selectedFile ? (
        /* Upload Zone */
        <div
          ref={dropZoneRef}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragOver 
              ? 'border-blue-400 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <PhotoIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">
            Upload Face Photo
          </h4>
          <p className="text-gray-600 mb-4">
            Drag and drop your photo here, or click to browse
          </p>
          
          <div className="space-y-2 text-sm text-gray-500 mb-6">
            <p>• Minimum size: 500x500 pixels</p>
            <p>• Formats: JPEG, PNG, WebP</p>
            <p>• Maximum size: {(maxFileSize / (1024 * 1024)).toFixed(1)}MB</p>
            <p>• Ensure good lighting and clear face visibility</p>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            accept={acceptedFormats.join(',')}
            onChange={handleFileInputChange}
            className="hidden"
          />
          
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={processing}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 inline-flex items-center"
          >
            <ArrowUpTrayIcon className="h-5 w-5 mr-2" />
            Choose File
          </button>
        </div>
      ) : (
        /* Preview and Process */
        <div className="space-y-6">
          {/* Image Preview */}
          <div className="text-center">
            <h4 className="font-medium text-gray-900 mb-4">Preview</h4>
            <div className="inline-block border-4 border-gray-200 rounded-lg overflow-hidden">
              <img
                src={preview}
                alt="Selected face"
                className="max-w-full max-h-80 object-contain"
              />
            </div>
            
            <div className="mt-4 text-sm text-gray-600">
              <p>File: {selectedFile.name}</p>
              <p>Size: {(selectedFile.size / 1024).toFixed(1)} KB</p>
              <p>Type: {selectedFile.type}</p>
            </div>
          </div>
          
          {/* Quality Requirements */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h5 className="font-medium text-yellow-900">Quality Requirements</h5>
                <ul className="text-sm text-yellow-800 mt-1 space-y-1">
                  <li>• Exactly one face must be visible</li>
                  <li>• Face should be well-lit and in focus</li>
                  <li>• Minimum 70% sharpness score required</li>
                  <li>• No sunglasses or face coverings</li>
                </ul>
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex space-x-4">
            <button
              onClick={clearSelection}
              disabled={processing}
              className="flex-1 bg-gray-600 text-white px-4 py-3 rounded-lg hover:bg-gray-700 disabled:opacity-50"
            >
              Choose Different Photo
            </button>
            
            <button
              onClick={processUploadedFace}
              disabled={processing || (result && result.success)}
              className="flex-1 bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
            >
              {processing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : result && result.success ? (
                <>
                  <CheckCircleIcon className="h-5 w-5 mr-2" />
                  Processed
                </>
              ) : (
                <>
                  <DocumentArrowUpIcon className="h-5 w-5 mr-2" />
                  Process Face
                </>
              )}
            </button>
          </div>
        </div>
      )}
      
      {/* Cancel Button */}
      {onCancel && (
        <div className="text-center mt-6">
          <button
            onClick={onCancel}
            disabled={processing}
            className="text-gray-600 hover:text-gray-800 disabled:opacity-50"
          >
            Cancel Upload
          </button>
        </div>
      )}
    </div>
  );
}
