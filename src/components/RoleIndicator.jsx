import { useAuth } from '../contexts/AuthContext';
import { 
  ShieldCheckIcon, 
  AcademicCapIcon, 
  BuildingLibraryIcon 
} from '@heroicons/react/24/outline';

/**
 * Component to display the current user's role with an appropriate icon
 */
export default function RoleIndicator() {
  const { userRole, isAdmin, isInstitute, isStudent } = useAuth();
  
  if (!userRole) return null;
  
  // Define role-specific styles and icons
  const getRoleConfig = () => {
    if (isAdmin) {
      return {
        icon: ShieldCheckIcon,
        bgColor: 'bg-red-100',
        textColor: 'text-red-800',
        borderColor: 'border-red-200',
        label: 'Admin'
      };
    }
    
    if (isInstitute) {
      return {
        icon: BuildingLibraryIcon,
        bgColor: 'bg-purple-100',
        textColor: 'text-purple-800',
        borderColor: 'border-purple-200',
        label: 'Institute'
      };
    }
    
    if (isStudent) {
      return {
        icon: AcademicCapIcon,
        bgColor: 'bg-blue-100',
        textColor: 'text-blue-800',
        borderColor: 'border-blue-200',
        label: 'Student'
      };
    }
    
    // Default fallback
    return {
      icon: AcademicCapIcon,
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-800',
      borderColor: 'border-gray-200',
      label: userRole
    };
  };
  
  const { icon: Icon, bgColor, textColor, borderColor, label } = getRoleConfig();
  
  return (
    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor} border ${borderColor}`}>
      <Icon className="mr-1 h-3 w-3" aria-hidden="true" />
      {label}
    </div>
  );
}
