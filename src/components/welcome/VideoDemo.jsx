import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';

export default function VideoDemo() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const videoRef = useRef(null);
  
  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };
  
  const handleVideoLoaded = () => {
    setIsLoaded(true);
  };

  return (
    <motion.div 
      className="max-w-4xl mx-auto rounded-xl overflow-hidden shadow-2xl"
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative">
        {/* Video Poster (shown until video loads) */}
        {!isLoaded && (
          <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
          </div>
        )}
        
        {/* Video Element */}
        <video
          ref={videoRef}
          className="w-full aspect-video object-cover"
          poster="/video-poster.jpg"
          onLoadedData={handleVideoLoaded}
          onEnded={() => setIsPlaying(false)}
          playsInline
        >
          <source src="/demo-video.mp4" type="video/mp4" />
          <track 
            kind="subtitles" 
            src="/subtitles.vtt" 
            srcLang="en" 
            label="English" 
            default 
          />
          Your browser does not support the video tag.
        </video>
        
        {/* Play/Pause Button Overlay */}
        <div 
          className="absolute inset-0 flex items-center justify-center cursor-pointer"
          onClick={handlePlayPause}
        >
          {!isPlaying && (
            <motion.div 
              className="bg-blue-600 bg-opacity-80 rounded-full p-5 text-white"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 5v14l11-7z" />
              </svg>
            </motion.div>
          )}
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 p-6">
        <h3 className="text-xl font-bold mb-2">See how Examino works</h3>
        <p className="text-gray-600 dark:text-gray-300">
          Watch our demo to see facial recognition attendance and secure exam proctoring in action.
        </p>
        
        {/* Video Controls */}
        <div className="mt-4 flex items-center">
          <button 
            onClick={handlePlayPause}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {isPlaying ? (
              <>
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
                </svg>
                Pause
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 5v14l11-7z" />
                </svg>
                Play
              </>
            )}
          </button>
          
          <a 
            href="/demo-video.mp4" 
            download
            className="ml-4 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            Download
          </a>
        </div>
      </div>
    </motion.div>
  );
}
