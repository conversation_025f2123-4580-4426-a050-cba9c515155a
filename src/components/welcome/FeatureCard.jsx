import React from 'react';
import { motion } from 'framer-motion';

export default function FeatureCard({ feature, index, controls }) {
  // Animation variants for different icon animations
  const iconAnimations = {
    blink: {
      initial: { opacity: 1 },
      animate: {
        opacity: [1, 0.5, 1],
        transition: {
          repeat: Infinity,
          repeatType: "reverse",
          duration: 2,
          repeatDelay: 1
        }
      }
    },
    pulse: {
      initial: { scale: 1 },
      animate: {
        scale: [1, 1.1, 1],
        transition: {
          repeat: Infinity,
          repeatType: "reverse",
          duration: 1.5,
          ease: "easeInOut"
        }
      }
    },
    grow: {
      initial: { height: "20%" },
      animate: {
        height: ["20%", "40%", "60%", "80%", "60%", "40%", "20%"],
        transition: {
          repeat: Infinity,
          duration: 3,
          ease: "easeInOut"
        }
      }
    }
  };

  // Get the correct animation based on the feature's animation property
  const iconAnimation = iconAnimations[feature.animation] || iconAnimations.pulse;

  return (
    <motion.div
      className="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-lg hover:shadow-xl transform-gpu hover:scale-105 transition-all duration-300"
      style={{
        boxShadow: '0 0 15px rgba(59, 130, 246, 0.3)'
      }}
      initial={{ opacity: 0, y: 50 }}
      animate={controls}
      variants={{
        visible: { 
          opacity: 1, 
          y: 0,
          transition: { duration: 0.5, delay: index * 0.2 }
        },
        hidden: { opacity: 0, y: 50 }
      }}
    >
      <div className="flex flex-col items-center text-center">
        <div className="mb-6 text-5xl">
          {feature.animation === 'blink' && (
            <motion.div
              initial={iconAnimation.initial}
              animate={iconAnimation.animate}
            >
              {feature.icon}
            </motion.div>
          )}
          
          {feature.animation === 'pulse' && (
            <motion.div
              initial={iconAnimation.initial}
              animate={iconAnimation.animate}
            >
              {feature.icon}
            </motion.div>
          )}
          
          {feature.animation === 'grow' && (
            <div className="relative h-16 w-16 flex items-end justify-center">
              <motion.div
                className="absolute bottom-0 w-4 bg-blue-500 dark:bg-blue-400 rounded-t-md"
                initial={iconAnimation.initial}
                animate={iconAnimation.animate}
              />
              <motion.div
                className="absolute bottom-0 w-4 bg-purple-500 dark:bg-purple-400 rounded-t-md ml-5"
                initial={iconAnimation.initial}
                animate={{
                  height: ["40%", "60%", "80%", "60%", "40%", "20%", "40%"],
                  transition: {
                    repeat: Infinity,
                    duration: 3,
                    ease: "easeInOut"
                  }
                }}
              />
              <motion.div
                className="absolute bottom-0 w-4 bg-green-500 dark:bg-green-400 rounded-t-md ml-10"
                initial={iconAnimation.initial}
                animate={{
                  height: ["60%", "80%", "60%", "40%", "20%", "40%", "60%"],
                  transition: {
                    repeat: Infinity,
                    duration: 3,
                    ease: "easeInOut"
                  }
                }}
              />
              {feature.icon}
            </div>
          )}
        </div>
        
        <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
        <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
      </div>
    </motion.div>
  );
}
