import React, { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { useGLTF, Float, Environment } from '@react-three/drei';

export default function HeroScene() {
  // This is a simplified version - in a real implementation, you would load an actual 3D model
  // For now, we'll create a simple dashboard-like object
  const dashboardRef = useRef();
  
  useFrame((state) => {
    const t = state.clock.getElapsedTime();
    dashboardRef.current.rotation.y = Math.sin(t / 4) / 8;
    dashboardRef.current.rotation.x = Math.sin(t / 4) / 8;
  });

  return (
    <>
      <Environment preset="city" />
      
      <Float
        speed={2} // Animation speed
        rotationIntensity={0.5} // Rotation intensity
        floatIntensity={0.5} // Float intensity
      >
        <group ref={dashboardRef} position={[0, 0, 0]}>
          {/* Dashboard base */}
          <mesh position={[0, 0, 0]} castShadow receiveShadow>
            <boxGeometry args={[5, 3, 0.2]} />
            <meshStandardMaterial color="#1e40af" metalness={0.5} roughness={0.2} />
          </mesh>
          
          {/* Screen */}
          <mesh position={[0, 0, 0.11]} castShadow>
            <boxGeometry args={[4.8, 2.8, 0.05]} />
            <meshStandardMaterial color="#f8fafc" metalness={0.1} roughness={0.2} />
          </mesh>
          
          {/* Camera circle */}
          <mesh position={[0, 1.2, 0.2]} castShadow>
            <cylinderGeometry args={[0.2, 0.2, 0.05, 32]} />
            <meshStandardMaterial color="#1e40af" metalness={0.8} roughness={0.2} />
          </mesh>
          
          {/* Camera lens */}
          <mesh position={[0, 1.2, 0.23]} castShadow>
            <cylinderGeometry args={[0.15, 0.15, 0.05, 32]} />
            <meshStandardMaterial color="#020617" metalness={0.9} roughness={0.1} />
          </mesh>
          
          {/* UI Elements - Charts */}
          <mesh position={[-1.5, -0.5, 0.15]} castShadow>
            <boxGeometry args={[1.5, 1, 0.05]} />
            <meshStandardMaterial color="#3b82f6" metalness={0.1} roughness={0.2} />
          </mesh>
          
          {/* UI Elements - Face Recognition Area */}
          <mesh position={[1.5, 0.5, 0.15]} castShadow>
            <boxGeometry args={[1.5, 1.5, 0.05]} />
            <meshStandardMaterial color="#8b5cf6" metalness={0.1} roughness={0.2} />
          </mesh>
          
          {/* UI Elements - Timer */}
          <mesh position={[1.5, -0.8, 0.15]} castShadow>
            <boxGeometry args={[1.5, 0.4, 0.05]} />
            <meshStandardMaterial color="#ef4444" metalness={0.1} roughness={0.2} />
          </mesh>
        </group>
      </Float>
    </>
  );
}
