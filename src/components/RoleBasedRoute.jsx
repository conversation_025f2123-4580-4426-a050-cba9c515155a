import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

/**
 * Role-based route component that restricts access based on user roles
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if authorized
 * @param {string|string[]} props.allowedRoles - Single role or array of roles allowed to access this route
 * @param {string} props.redirectTo - Path to redirect to if unauthorized (default: "/dashboard")
 */
export default function RoleBasedRoute({ children, allowedRoles, redirectTo = "/dashboard" }) {
  const { currentUser, userRole, loading } = useAuth();
  
  // Show loading skeleton while authentication state is being determined
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="w-full max-w-md p-6">
          <Skeleton height={40} className="mb-6" />
          <Skeleton count={3} className="mb-2" />
          <Skeleton height={100} className="mb-6" />
          <Skeleton height={40} width="50%" />
        </div>
      </div>
    );
  }
  
  // Redirect to login if not authenticated
  if (!currentUser) {
    return <Navigate to="/login" />;
  }
  
  // Check if user has the required role
  const hasRequiredRole = Array.isArray(allowedRoles)
    ? allowedRoles.includes(userRole)
    : userRole === allowedRoles;
  
  // Render children if user has the required role, otherwise redirect
  return hasRequiredRole ? children : <Navigate to={redirectTo} />;
}
