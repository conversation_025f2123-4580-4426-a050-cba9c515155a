import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import PrivateRoute from './components/PrivateRoute';
import RoleBasedRoute from './components/RoleBasedRoute';

// Pages
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import Signup from './pages/Signup';
import SignupWithFace from './pages/SignupWithFace';
import InviteSignup from './pages/InviteSignup';
import Profile from './pages/Profile';
import Exams from './pages/Exams';
import Exam from './pages/Exam';
import Results from './pages/Results';
import Attendance from './pages/Attendance';
import AdminDashboard from './pages/AdminDashboard';
import InstituteDashboard from './pages/InstituteDashboard';
import LandingPage from './pages/LandingPage';
import Welcome from './pages/Welcome';
import CatchyLandingPage from './pages/CatchyLandingPage';
import Forbidden from './pages/Forbidden';
import Test from './pages/Test';
import Simple from './pages/Simple';

function App() {
  return (
    <Router>
      <AuthProvider>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<Welcome />} />
          <Route path="/test" element={<Test />} />
          <Route path="/catchy" element={<CatchyLandingPage />} />
          <Route path="/old" element={<LandingPage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/signup-with-face" element={<SignupWithFace />} />
          <Route path="/invite-signup" element={<InviteSignup />} />
          <Route path="/forbidden" element={<Forbidden />} />

          {/* Protected Routes */}
          <Route path="/dashboard" element={
            <PrivateRoute>
              <Dashboard />
            </PrivateRoute>
          } />

          <Route path="/profile" element={
            <PrivateRoute>
              <Profile />
            </PrivateRoute>
          } />

          <Route path="/exams" element={
            <PrivateRoute>
              <Exams />
            </PrivateRoute>
          } />

          <Route path="/results" element={
            <PrivateRoute>
              <Results />
            </PrivateRoute>
          } />

          <Route path="/attendance" element={
            <PrivateRoute>
              <Attendance />
            </PrivateRoute>
          } />

          <Route path="/exam/:examId" element={
            <PrivateRoute>
              <Exam />
            </PrivateRoute>
          } />

          {/* Role-Based Routes */}
          {/* Admin Dashboard - Only accessible by admins */}
          <Route path="/admin/*" element={
            <RoleBasedRoute allowedRoles="admin" redirectTo="/forbidden">
              <AdminDashboard />
            </RoleBasedRoute>
          } />

          {/* Institute Dashboard - Only accessible by institutes */}
          <Route path="/institute/*" element={
            <RoleBasedRoute allowedRoles="institute" redirectTo="/forbidden">
              <InstituteDashboard />
            </RoleBasedRoute>
          } />

          {/* Student Routes - Only accessible by students */}
          <Route path="/student/*" element={
            <RoleBasedRoute allowedRoles="student" redirectTo="/forbidden">
              <Dashboard />
            </RoleBasedRoute>
          } />

          {/* Redirect any unknown routes to Landing Page */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </AuthProvider>
    </Router>
  );
}

export default App;
