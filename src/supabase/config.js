// Mock Supabase client for development
// For production, use the real Supabase client from src/lib/supabase.js
// import { createClient } from '@supabase/supabase-js';
import { hashPassword, verifyPassword } from '../utils/passwordUtils';

// This file is used for development and testing purposes only.
// It provides mock data and functions to simulate Supabase functionality
// without requiring an actual Supabase instance.

// Mock data
const mockData = {
  user_roles: [
    { role_id: 1, role_name: 'admin' },
    { role_id: 2, role_name: 'institute' },
    { role_id: 3, role_name: 'student' }
  ],
  users: [
    {
      id: 'test-user-id',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      student_id: 'S12345',
      course: 'Computer Science',
      semester: '3',
      reference_image: 'https://via.placeholder.com/150',
      reference_image_url: 'https://via.placeholder.com/150',
      face_descriptor: 'mock-face-descriptor-data',
      role_id: 1, // Admin role
      is_verified: true,
      institute_id: null,
      password_hash: 'hashed:password123',
      user_roles: { role_name: 'admin' } // Join data
    },
    {
      id: 'institute-user-id',
      email: '<EMAIL>',
      first_name: 'Institute',
      last_name: 'Manager',
      role_id: 2, // Institute role
      is_verified: true,
      institute_id: 'INST001',
      password_hash: 'hashed:password123',
      user_roles: { role_name: 'institute' } // Join data
    },
    {
      id: 'student-user-id',
      email: '<EMAIL>',
      first_name: 'Student',
      last_name: 'User',
      student_id: 'S67890',
      course: 'Computer Science',
      semester: '2',
      reference_image_url: 'https://via.placeholder.com/150',
      face_descriptor: 'mock-face-descriptor-data',
      role_id: 3, // Student role
      is_verified: true,
      institute_id: 'INST001',
      password_hash: 'hashed:password123',
      user_roles: { role_name: 'student' } // Join data
    }
  ],
  invite_codes: [
    {
      id: 'invite-1',
      code: 'ADMIN123',
      role_id: 1,
      email: '<EMAIL>',
      created_by: 'test-user-id',
      created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      expires_at: new Date(Date.now() + 6 * 86400000).toISOString(), // 6 days from now
      used_at: null,
      used_by: null,
      user_roles: { role_name: 'admin' } // Join data
    },
    {
      id: 'invite-2',
      code: 'INST456',
      role_id: 2,
      email: '<EMAIL>',
      created_by: 'test-user-id',
      created_at: new Date(Date.now() - 2 * 86400000).toISOString(), // 2 days ago
      expires_at: new Date(Date.now() + 5 * 86400000).toISOString(), // 5 days from now
      used_at: null,
      used_by: null,
      user_roles: { role_name: 'institute' } // Join data
    }
  ],
  exams: [
    {
      id: 1,
      title: 'Mock Exam 1',
      description: 'This is a mock exam for testing',
      start_time: new Date().toISOString(),
      end_time: new Date(Date.now() + 3600000).toISOString(),
      created_by: 'test-user-id'
    }
  ]
};

// Mock implementation of Supabase methods
const supabase = {
  auth: {
    signInWithPassword: ({ email, password }) => {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          // Find user by email
          const user = mockData.users.find(u => u.email === email);

          // For test accounts, allow password123 to work regardless of stored password
          const isTestAccount = ['<EMAIL>', '<EMAIL>', '<EMAIL>'].includes(email);
          const passwordMatches = isTestAccount ?
            (password === 'password123') :
            (user && verifyPassword(password, user.password_hash || 'hashed:password123'));

          if (user && passwordMatches) {
            resolve({
              data: {
                user: {
                  id: user.id,
                  email: user.email,
                }
              },
              error: null
            });
          } else {
            resolve({ data: null, error: { message: 'Invalid login credentials' } });
          }
        }, 500);
      });
    },
    signUp: ({ email, password, options }) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          // Check if email already exists
          const existingUser = mockData.users.find(u => u.email === email);
          if (existingUser) {
            return resolve({
              data: null,
              error: { message: 'Email already registered' }
            });
          }

          // Hash the password
          const password_hash = hashPassword(password);

          // Create a new user
          const newUser = {
            id: `user-${Date.now()}`,
            email,
            password_hash,
            role_id: 3, // Default to student role
            is_verified: false,
            ...options?.data
          };

          // Add to mock data
          mockData.users.push({
            ...newUser,
            user_roles: { role_name: 'student' } // Default role
          });

          resolve({
            data: {
              user: newUser
            },
            error: null
          });
        }, 500);
      });
    },
    signOut: () => {
      return Promise.resolve({ error: null });
    },
    getSession: () => {
      return Promise.resolve({
        data: {
          session: {
            user: {
              id: 'test-user-id',
              email: '<EMAIL>',
            }
          }
        }
      });
    },
    onAuthStateChange: (callback) => {
      // Mock auth state change listener
      return { data: { subscription: { unsubscribe: () => {} } } };
    }
  },
  from: (table) => {
    return {
      select: (fields) => {
        return {
          eq: (field, value) => {
            return {
              single: () => {
                const item = mockData[table]?.find(item => item[field] === value);
                return Promise.resolve({ data: item || null, error: null });
              },
              order: (orderField, { ascending }) => {
                const items = mockData[table]?.filter(item => item[field] === value) || [];
                const sortedItems = [...items].sort((a, b) => {
                  if (ascending) {
                    return a[orderField] > b[orderField] ? 1 : -1;
                  } else {
                    return a[orderField] < b[orderField] ? 1 : -1;
                  }
                });
                return {
                  limit: (num) => {
                    return Promise.resolve({ data: sortedItems.slice(0, num), error: null });
                  }
                };
              }
            };
          },
          order: (orderField, { ascending }) => {
            const items = mockData[table] || [];
            const sortedItems = [...items].sort((a, b) => {
              if (ascending) {
                return a[orderField] > b[orderField] ? 1 : -1;
              } else {
                return a[orderField] < b[orderField] ? 1 : -1;
              }
            });
            return {
              limit: (num) => {
                return Promise.resolve({ data: sortedItems.slice(0, num), error: null });
              }
            };
          }
        };
      },
      update: (data) => {
        return {
          eq: (field, value) => {
            return Promise.resolve({ data, error: null });
          }
        };
      },
      insert: (data) => {
        return Promise.resolve({ data, error: null });
      },
      upsert: (data) => {
        return Promise.resolve({ data, error: null });
      },
      delete: () => {
        return {
          eq: () => {
            return Promise.resolve({ error: null });
          }
        };
      }
    };
  },
  storage: {
    from: (bucket) => {
      return {
        upload: (path, file) => {
          return Promise.resolve({ data: { path }, error: null });
        },
        getPublicUrl: (path) => {
          return { data: { publicUrl: `https://via.placeholder.com/150?text=${path}` } };
        }
      };
    }
  },
  rpc: (functionName, params) => {
    // Mock RPC functions
    if (functionName === 'verify_invite_code') {
      const { code } = params;
      const invite = mockData.invite_codes.find(i => i.code === code);

      if (invite && !invite.used_at && new Date(invite.expires_at) > new Date()) {
        return Promise.resolve({
          data: [{
            is_valid: true,
            role_name: mockData.user_roles.find(r => r.role_id === invite.role_id)?.role_name,
            email: invite.email
          }],
          error: null
        });
      } else {
        return Promise.resolve({
          data: [{
            is_valid: false,
            role_name: null,
            email: null
          }],
          error: null
        });
      }
    } else if (functionName === 'use_invite_code') {
      const { code, user_id } = params;
      const invite = mockData.invite_codes.find(i => i.code === code);

      if (invite && !invite.used_at && new Date(invite.expires_at) > new Date()) {
        // Mark invite as used
        invite.used_at = new Date().toISOString();
        invite.used_by = user_id;

        return Promise.resolve({
          data: true,
          error: null
        });
      } else {
        return Promise.resolve({
          data: false,
          error: null
        });
      }
    } else if (functionName === 'generate_invite_code') {
      const { role_name, email } = params;
      const roleId = mockData.user_roles.find(r => r.role_name === role_name)?.role_id;

      if (!roleId) {
        return Promise.resolve({
          data: null,
          error: { message: 'Invalid role name' }
        });
      }

      // Generate a random code
      const code = Math.random().toString(36).substring(2, 8).toUpperCase();

      // Add to mock data
      mockData.invite_codes.push({
        id: `invite-${Date.now()}`,
        code,
        role_id: roleId,
        email,
        created_by: 'test-user-id',
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 7 * 86400000).toISOString(), // 7 days from now
        used_at: null,
        used_by: null,
        user_roles: { role_name }
      });

      return Promise.resolve({
        data: code,
        error: null
      });
    } else if (functionName === 'check_user_role') {
      const { user_id, role_name } = params;
      const user = mockData.users.find(u => u.id === user_id);
      const userRole = user ? mockData.user_roles.find(r => r.role_id === user.role_id)?.role_name : null;

      return Promise.resolve({
        data: userRole === role_name,
        error: null
      });
    } else if (functionName === 'get_user_role') {
      const { user_id } = params;
      const user = mockData.users.find(u => u.id === user_id);
      const userRole = user ? mockData.user_roles.find(r => r.role_id === user.role_id)?.role_name : null;

      return Promise.resolve({
        data: userRole,
        error: null
      });
    } else {
      return Promise.resolve({
        data: null,
        error: { message: `Function ${functionName} not implemented in mock` }
      });
    }
  }
};

export default supabase;
