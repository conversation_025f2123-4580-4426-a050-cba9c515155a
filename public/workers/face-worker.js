/**
 * Face Processing Web Worker
 * Dedicated worker for face detection and descriptor generation
 * Offloads heavy computation from main thread
 */

// Import face-api.js in worker context
importScripts('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js');

let modelsLoaded = false;
let loadingPromise = null;

/**
 * Load face-api.js models in worker
 */
async function loadModels() {
  if (modelsLoaded) return true;
  if (loadingPromise) return loadingPromise;
  
  loadingPromise = (async () => {
    try {
      const MODEL_URL = '/models';
      
      // Load only essential models for speed
      await Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),
        faceapi.nets.faceLandmark68TinyNet.loadFromUri(MODEL_URL), // Faster than full landmarks
        faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL)
      ]);
      
      modelsLoaded = true;
      console.log('✅ Face-api.js models loaded in worker');
      return true;
    } catch (error) {
      console.error('❌ Failed to load models in worker:', error);
      return false;
    }
  })();
  
  return loadingPromise;
}

/**
 * Process face detection and descriptor generation
 */
async function processFace(imageData) {
  const startTime = performance.now();
  
  try {
    // Ensure models are loaded
    const loaded = await loadModels();
    if (!loaded) {
      throw new Error('Failed to load face detection models');
    }
    
    // Create image element from blob
    const img = new Image();
    const imageUrl = URL.createObjectURL(imageData.blob);
    
    return new Promise((resolve, reject) => {
      img.onload = async () => {
        try {
          URL.revokeObjectURL(imageUrl); // Clean up
          
          // Optimized detection options for speed
          const detectionOptions = new faceapi.TinyFaceDetectorOptions({
            inputSize: 224, // Smaller input size for speed
            scoreThreshold: 0.5 // Lower threshold for better detection
          });
          
          // Detect faces with landmarks and descriptors
          const detections = await faceapi
            .detectAllFaces(img, detectionOptions)
            .withFaceLandmarks(true) // Use tiny landmarks for speed
            .withFaceDescriptors();
          
          const processingTime = performance.now() - startTime;
          
          if (detections.length === 0) {
            resolve({
              success: false,
              error: 'No face detected',
              processingTime
            });
            return;
          }
          
          if (detections.length > 1) {
            resolve({
              success: false,
              error: 'Multiple faces detected',
              processingTime,
              facesDetected: detections.length
            });
            return;
          }
          
          const detection = detections[0];
          
          // Extract face descriptor (128-dimensional vector)
          const descriptor = Array.from(detection.descriptor);
          
          // Calculate quality metrics
          const detectionScore = detection.detection.score;
          const faceBox = detection.detection.box;
          const imageArea = img.width * img.height;
          const faceArea = faceBox.width * faceBox.height;
          const faceRatio = faceArea / imageArea;
          
          // Quality assessment
          const qualityScore = Math.min(
            detectionScore * 1.2, // Boost detection score
            faceRatio > 0.1 ? 1.0 : faceRatio * 10 // Penalize small faces
          );
          
          resolve({
            success: true,
            descriptor,
            detectionScore,
            qualityScore,
            faceBox: {
              x: faceBox.x,
              y: faceBox.y,
              width: faceBox.width,
              height: faceBox.height
            },
            facesDetected: 1,
            processingTime,
            imageSize: {
              width: img.width,
              height: img.height
            }
          });
          
        } catch (error) {
          reject(new Error(`Face processing failed: ${error.message}`));
        }
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(imageUrl);
        reject(new Error('Failed to load image in worker'));
      };
      
      img.src = imageUrl;
    });
    
  } catch (error) {
    const processingTime = performance.now() - startTime;
    return {
      success: false,
      error: error.message,
      processingTime
    };
  }
}

/**
 * Compress and resize image for optimal processing
 */
async function compressImage(imageBlob, maxWidth = 640, maxHeight = 480, quality = 0.8) {
  return new Promise((resolve) => {
    const img = new Image();
    const canvas = new OffscreenCanvas(maxWidth, maxHeight);
    const ctx = canvas.getContext('2d');
    
    img.onload = () => {
      // Calculate optimal dimensions maintaining aspect ratio
      const aspectRatio = img.width / img.height;
      let { width, height } = img;
      
      if (width > maxWidth) {
        width = maxWidth;
        height = width / aspectRatio;
      }
      
      if (height > maxHeight) {
        height = maxHeight;
        width = height * aspectRatio;
      }
      
      // Resize canvas to optimal dimensions
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      
      // Convert to WebP for better compression
      canvas.convertToBlob({
        type: 'image/webp',
        quality: quality
      }).then(resolve);
    };
    
    img.src = URL.createObjectURL(imageBlob);
  });
}

/**
 * Batch process multiple operations
 */
async function batchProcess(operations) {
  const results = await Promise.allSettled(operations.map(op => {
    switch (op.type) {
      case 'face_detection':
        return processFace(op.data);
      case 'image_compression':
        return compressImage(op.data.blob, op.data.maxWidth, op.data.maxHeight, op.data.quality);
      default:
        return Promise.reject(new Error(`Unknown operation: ${op.type}`));
    }
  }));
  
  return results.map((result, index) => ({
    operation: operations[index].type,
    success: result.status === 'fulfilled',
    data: result.status === 'fulfilled' ? result.value : null,
    error: result.status === 'rejected' ? result.reason.message : null
  }));
}

/**
 * Worker message handler
 */
self.onmessage = async (event) => {
  const { id, type, data } = event.data;
  
  try {
    let result;
    
    switch (type) {
      case 'LOAD_MODELS':
        result = await loadModels();
        break;
        
      case 'PROCESS_FACE':
        result = await processFace(data);
        break;
        
      case 'COMPRESS_IMAGE':
        result = await compressImage(data.blob, data.maxWidth, data.maxHeight, data.quality);
        break;
        
      case 'BATCH_PROCESS':
        result = await batchProcess(data.operations);
        break;
        
      case 'HEALTH_CHECK':
        result = {
          status: 'healthy',
          modelsLoaded,
          timestamp: Date.now()
        };
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
    
    // Send success response
    self.postMessage({
      id,
      type: `${type}_SUCCESS`,
      data: result
    });
    
  } catch (error) {
    // Send error response
    self.postMessage({
      id,
      type: `${type}_ERROR`,
      error: error.message
    });
  }
};

/**
 * Worker initialization
 */
self.postMessage({
  type: 'WORKER_READY',
  data: {
    timestamp: Date.now(),
    capabilities: [
      'face_detection',
      'image_compression',
      'batch_processing',
      'model_loading'
    ]
  }
});

// Handle worker errors
self.onerror = (error) => {
  console.error('Worker error:', error);
  self.postMessage({
    type: 'WORKER_ERROR',
    error: error.message
  });
};

// Handle unhandled promise rejections
self.onunhandledrejection = (event) => {
  console.error('Worker unhandled rejection:', event.reason);
  self.postMessage({
    type: 'WORKER_ERROR',
    error: event.reason.message || 'Unhandled promise rejection'
  });
};
