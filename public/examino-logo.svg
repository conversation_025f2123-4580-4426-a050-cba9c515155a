<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="80" viewBox="0 0 300 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#8B5CF6" />
    </linearGradient>
  </defs>
  
  <!-- Text "EXAMINO" -->
  <path d="M30 20H60V30H30V40H60V50H30V60H60V20H30Z" fill="url(#logoGradient)"/>
  <path d="M70 20H80V60H70V20Z" fill="url(#logoGradient)"/>
  <path d="M90 20H120L130 60H120L118 50H92L90 60H80L90 20ZM95 40H115L112.5 30L97.5 30L95 40Z" fill="url(#logoGradient)"/>
  <path d="M140 20H150V30H170V20H180V60H170V40H150V60H140V20Z" fill="url(#logoGradient)"/>
  <path d="M190 20H200V60H190V20Z" fill="url(#logoGradient)"/>
  <path d="M210 20H220V50H250V20H260V60H210V20Z" fill="url(#logoGradient)"/>
  <path d="M270 20H300L290 40L300 60H270V20ZM280 30V50H290L285 40L290 30H280Z" fill="url(#logoGradient)"/>
  
  <!-- Eye icon in the "O" -->
  <circle cx="285" cy="40" r="5" fill="#FFFFFF"/>
  <circle cx="285" cy="40" r="2" fill="#3B82F6"/>
  
  <!-- Scan line across the logo -->
  <rect x="30" y="38" width="240" height="4" fill="#10B981" opacity="0.3">
    <animate attributeName="x" from="-240" to="300" dur="2s" repeatCount="indefinite"/>
  </rect>
</svg>
