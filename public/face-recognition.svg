<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="faceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#8B5CF6" />
    </linearGradient>
    <linearGradient id="scanGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#10B981" stop-opacity="0" />
      <stop offset="50%" stop-color="#10B981" stop-opacity="0.7" />
      <stop offset="100%" stop-color="#10B981" stop-opacity="0" />
    </linearGradient>
  </defs>
  
  <!-- Isometric face outline -->
  <path d="M150 50 L75 100 L75 200 L150 250 L225 200 L225 100 Z" fill="#EEF2FF" stroke="url(#faceGradient)" stroke-width="3"/>
  
  <!-- Face features -->
  <circle cx="115" cy="140" r="10" fill="#3B82F6"/> <!-- Left eye -->
  <circle cx="185" cy="140" r="10" fill="#3B82F6"/> <!-- Right eye -->
  <path d="M130 180 Q150 200 170 180" stroke="#3B82F6" stroke-width="3" fill="none"/> <!-- Smile -->
  
  <!-- Facial recognition points -->
  <circle cx="115" cy="140" r="2" fill="#FFFFFF"/>
  <circle cx="185" cy="140" r="2" fill="#FFFFFF"/>
  <circle cx="150" cy="120" r="2" fill="#10B981"/>
  <circle cx="130" cy="180" r="2" fill="#10B981"/>
  <circle cx="170" cy="180" r="2" fill="#10B981"/>
  <circle cx="100" cy="120" r="2" fill="#10B981"/>
  <circle cx="200" cy="120" r="2" fill="#10B981"/>
  <circle cx="90" cy="150" r="2" fill="#10B981"/>
  <circle cx="210" cy="150" r="2" fill="#10B981"/>
  
  <!-- Connection lines -->
  <line x1="115" y1="140" x2="185" y2="140" stroke="#10B981" stroke-width="1" stroke-dasharray="5,5"/>
  <line x1="115" y1="140" x2="150" y2="120" stroke="#10B981" stroke-width="1" stroke-dasharray="5,5"/>
  <line x1="185" y1="140" x2="150" y2="120" stroke="#10B981" stroke-width="1" stroke-dasharray="5,5"/>
  <line x1="130" y1="180" x2="170" y2="180" stroke="#10B981" stroke-width="1" stroke-dasharray="5,5"/>
  <line x1="115" y1="140" x2="130" y2="180" stroke="#10B981" stroke-width="1" stroke-dasharray="5,5"/>
  <line x1="185" y1="140" x2="170" y2="180" stroke="#10B981" stroke-width="1" stroke-dasharray="5,5"/>
  
  <!-- Scanning lines (animated in CSS) -->
  <rect x="75" y="100" width="150" height="4" fill="url(#scanGradient)" class="scan-line">
    <animate attributeName="y" from="100" to="200" dur="1.5s" repeatCount="indefinite"/>
  </rect>
  
  <!-- Recognition status -->
  <rect x="95" y="220" width="110" height="20" rx="10" fill="#10B981"/>
  <text x="150" y="235" font-family="Arial" font-size="12" fill="white" text-anchor="middle">98% MATCH</text>
</svg>
