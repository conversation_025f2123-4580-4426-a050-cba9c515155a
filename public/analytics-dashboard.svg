<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="rgba(255,255,255,0.1)" />
      <stop offset="100%" stop-color="rgba(255,255,255,0.2)" />
    </linearGradient>
    <linearGradient id="barGradient1" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#60A5FA" />
    </linearGradient>
    <linearGradient id="barGradient2" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#8B5CF6" />
      <stop offset="100%" stop-color="#A78BFA" />
    </linearGradient>
    <linearGradient id="barGradient3" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#10B981" />
      <stop offset="100%" stop-color="#34D399" />
    </linearGradient>
    <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#8B5CF6" />
    </linearGradient>
    <filter id="blur">
      <feGaussianBlur stdDeviation="5" />
    </filter>
  </defs>
  
  <!-- Background with glassmorphism effect -->
  <rect x="50" y="50" width="200" height="200" rx="10" fill="#1E293B" />
  <rect x="50" y="50" width="200" height="200" rx="10" fill="url(#cardGradient)" style="backdrop-filter: blur(10px);" />
  
  <!-- Dashboard header -->
  <rect x="60" y="60" width="180" height="30" rx="5" fill="rgba(255,255,255,0.1)" />
  <text x="80" y="80" font-family="Arial" font-size="14" fill="white">Analytics Dashboard</text>
  
  <!-- Chart area -->
  <rect x="60" y="100" width="180" height="100" rx="5" fill="rgba(255,255,255,0.05)" />
  
  <!-- Bar chart -->
  <rect x="80" y="170" width="20" height="0" fill="url(#barGradient1)">
    <animate attributeName="height" from="0" to="60" dur="1s" fill="freeze" />
    <animate attributeName="y" from="170" to="110" dur="1s" fill="freeze" />
  </rect>
  
  <rect x="110" y="170" width="20" height="0" fill="url(#barGradient2)">
    <animate attributeName="height" from="0" to="40" dur="1s" fill="freeze" begin="0.2s" />
    <animate attributeName="y" from="170" to="130" dur="1s" fill="freeze" begin="0.2s" />
  </rect>
  
  <rect x="140" y="170" width="20" height="0" fill="url(#barGradient3)">
    <animate attributeName="height" from="0" to="70" dur="1s" fill="freeze" begin="0.4s" />
    <animate attributeName="y" from="170" to="100" dur="1s" fill="freeze" begin="0.4s" />
  </rect>
  
  <!-- Line chart -->
  <path d="M180 150 L200 130 L220 140" stroke="url(#lineGradient)" stroke-width="3" fill="none">
    <animate attributeName="opacity" from="0" to="1" dur="1s" fill="freeze" begin="0.6s" />
  </path>
  <circle cx="180" cy="150" r="3" fill="#3B82F6">
    <animate attributeName="opacity" from="0" to="1" dur="1s" fill="freeze" begin="0.6s" />
  </circle>
  <circle cx="200" cy="130" r="3" fill="#8B5CF6">
    <animate attributeName="opacity" from="0" to="1" dur="1s" fill="freeze" begin="0.7s" />
  </circle>
  <circle cx="220" cy="140" r="3" fill="#10B981">
    <animate attributeName="opacity" from="0" to="1" dur="1s" fill="freeze" begin="0.8s" />
  </circle>
  
  <!-- Student avatars -->
  <circle cx="70" y="210" r="10" fill="#3B82F6" />
  <text x="70" y="214" font-family="Arial" font-size="10" fill="white" text-anchor="middle">S1</text>
  
  <circle cx="100" y="210" r="10" fill="#8B5CF6" />
  <text x="100" y="214" font-family="Arial" font-size="10" fill="white" text-anchor="middle">S2</text>
  
  <circle cx="130" y="210" r="10" fill="#10B981" />
  <text x="130" y="214" font-family="Arial" font-size="10" fill="white" text-anchor="middle">S3</text>
  
  <!-- Stats -->
  <text x="170" y="205" font-family="Arial" font-size="10" fill="white">Avg. Score: 85%</text>
  <text x="170" y="220" font-family="Arial" font-size="10" fill="white">Attendance: 92%</text>
  
  <!-- Glow effects -->
  <circle cx="150" cy="150" r="70" fill="#3B82F6" opacity="0.1" filter="url(#blur)" />
</svg>
