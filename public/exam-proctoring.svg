<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="laptopGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#8B5CF6" />
    </linearGradient>
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#10B981" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
  </defs>
  
  <!-- Laptop base -->
  <rect x="50" y="180" width="200" height="10" rx="2" fill="#1E293B"/>
  <path d="M70 180 L70 100 L230 100 L230 180 Z" fill="#1E293B"/>
  
  <!-- Laptop screen -->
  <rect x="80" y="110" width="140" height="60" rx="2" fill="#F8FAFC"/>
  
  <!-- Browser tabs -->
  <rect x="80" y="110" width="140" height="15" rx="2" fill="#E2E8F0"/>
  <rect x="85" y="112" width="30" height="11" rx="2" fill="#CBD5E1"/>
  <rect x="120" y="112" width="30" height="11" rx="2" fill="#CBD5E1"/>
  <rect x="155" y="112" width="30" height="11" rx="2" fill="#CBD5E1"/>
  
  <!-- Exam content -->
  <rect x="90" y="135" width="120" height="5" rx="1" fill="#CBD5E1"/>
  <rect x="90" y="145" width="120" height="5" rx="1" fill="#CBD5E1"/>
  <rect x="90" y="155" width="80" height="5" rx="1" fill="#CBD5E1"/>
  
  <!-- Shield overlay -->
  <path d="M150 80 L100 100 L100 150 C100 180 150 200 150 200 C150 200 200 180 200 150 L200 100 Z" fill="url(#shieldGradient)" fill-opacity="0.7"/>
  
  <!-- Shield icon -->
  <path d="M150 90 L115 105 L115 140 C115 160 150 175 150 175 C150 175 185 160 185 140 L185 105 Z" fill="none" stroke="white" stroke-width="3"/>
  <text x="150" y="145" font-family="Arial" font-size="24" font-weight="bold" fill="white" text-anchor="middle">✓</text>
  
  <!-- Blocked tabs (red X) -->
  <circle cx="190" cy="112" r="8" fill="#EF4444" class="blocked-tab">
    <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
  </circle>
  <path d="M186 108 L194 116 M194 108 L186 116" stroke="white" stroke-width="2" class="blocked-tab">
    <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
  </path>
  
  <!-- Alert message -->
  <rect x="100" y="60" width="100" height="30" rx="5" fill="#EF4444" class="alert">
    <animate attributeName="opacity" values="0;0;1;1;0" dur="4s" repeatCount="indefinite"/>
  </rect>
  <text x="150" y="80" font-family="Arial" font-size="12" fill="white" text-anchor="middle" class="alert">
    <animate attributeName="opacity" values="0;0;1;1;0" dur="4s" repeatCount="indefinite"/>
    Tab switching detected!
  </text>
</svg>
