const fs = require('fs');
const path = require('path');
const https = require('https');

// Create models directory if it doesn't exist
const modelsDir = path.join(__dirname, 'public', 'models');
if (!fs.existsSync(modelsDir)) {
  fs.mkdirSync(modelsDir, { recursive: true });
  console.log('Created models directory:', modelsDir);
}

// List of model files to download
const modelFiles = [
  'ssd_mobilenetv1_model-weights_manifest.json',
  'ssd_mobilenetv1_model-shard1',
  'face_landmark_68_model-weights_manifest.json',
  'face_landmark_68_model-shard1',
  'face_recognition_model-weights_manifest.json',
  'face_recognition_model-shard1'
];

// Base URL for the model files
const baseUrl = 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/';

// Download each model file
modelFiles.forEach(file => {
  const url = baseUrl + file;
  const filePath = path.join(modelsDir, file);
  
  console.log(`Downloading ${file}...`);
  
  const fileStream = fs.createWriteStream(filePath);
  
  https.get(url, response => {
    if (response.statusCode !== 200) {
      console.error(`Failed to download ${file}: ${response.statusCode} ${response.statusMessage}`);
      fs.unlinkSync(filePath); // Remove the file if download failed
      return;
    }
    
    response.pipe(fileStream);
    
    fileStream.on('finish', () => {
      fileStream.close();
      console.log(`Downloaded ${file} successfully`);
    });
  }).on('error', err => {
    fs.unlinkSync(filePath); // Remove the file if download failed
    console.error(`Error downloading ${file}:`, err.message);
  });
});

console.log('Download process started. Please wait for all files to complete downloading.');
