# Face Detection Models Setup for Examino

This guide explains how to set up the face detection models required for the facial recognition features in Examino.

## Required Model Files

The following face-api.js model files are required:

1. `ssd_mobilenetv1_model-weights_manifest.json`
2. `ssd_mobilenetv1_model-shard1`
3. `face_landmark_68_model-weights_manifest.json`
4. `face_landmark_68_model-shard1`
5. `face_recognition_model-weights_manifest.json`
6. `face_recognition_model-shard1`

These files need to be placed in the `public/models` directory of your project.

## Automatic Setup (Recommended)

We've provided a script to automatically download the required model files:

```bash
node download-face-models.js
```

This script will:
1. Create the `public/models` directory if it doesn't exist
2. Download all required model files from the face-api.js GitHub repository
3. Place them in the correct location

## Manual Setup

If you prefer to set up the models manually:

1. Create a `models` directory inside the `public` folder if it doesn't exist:
   ```bash
   mkdir -p public/models
   ```

2. Download the model files from the face-api.js GitHub repository:
   - [ssd_mobilenetv1_model-weights_manifest.json](https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/ssd_mobilenetv1_model-weights_manifest.json)
   - [ssd_mobilenetv1_model-shard1](https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/ssd_mobilenetv1_model-shard1)
   - [face_landmark_68_model-weights_manifest.json](https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_landmark_68_model-weights_manifest.json)
   - [face_landmark_68_model-shard1](https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_landmark_68_model-shard1)
   - [face_recognition_model-weights_manifest.json](https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_recognition_model-weights_manifest.json)
   - [face_recognition_model-shard1](https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_recognition_model-shard1)

3. Place all downloaded files in the `public/models` directory.

## Verifying the Setup

To verify that the models are correctly set up:

1. Make sure all model files are in the `public/models` directory
2. Start the development server:
   ```bash
   npm run dev
   ```
3. Navigate to the signup page with face enrollment
4. The webcam should initialize and face detection should work without errors

## Troubleshooting

If you encounter the error "Failed to load face detection models":

1. Check that all model files are in the `public/models` directory
2. Ensure the file names match exactly as listed above
3. Restart the development server
4. Clear your browser cache and refresh the page

If the issue persists, try running the automatic setup script again:
```bash
node download-face-models.js
```
