# Admin Interface Setup for Examino

This guide will help you set up the secure admin interface for Examino, which enables student data management, class assignment, and exam scheduling.

## 1. Database Schema Setup

### Run the Migration Script

1. Connect to your Supabase project
2. Go to the SQL Editor
3. Run the migration script located at `supabase/migrations/04_classes_schema.sql`

This will create the necessary tables and security policies for:
- Classes
- Student-class relationships
- Exam scheduling
- Student data management

## 2. Row-Level Security (RLS) Policies

The migration script sets up the following RLS policies:

- **Classes**: Only admins can create, update, and delete classes. Everyone can view classes.
- **Student Classes**: Only admins can manage student-class relationships. Students can view their own classes.
- **Students**: Only admins can manage student data. Students can view their own data.
- **Exams**: Only admins can manage exams. Students can view exams assigned to their classes.

## 3. Admin Interface Features

### CSV Upload

The CSV Upload feature allows administrators to import student data in bulk:

1. Prepare a CSV file with the following columns:
   - `name` or `first_name` and `last_name` (required)
   - `email` (required, must be unique)
   - `class_id` (required, must match an existing class name)
   - `student_id` (optional, will be generated if not provided)
   - `course` (optional)
   - `semester` (optional)

2. Upload the CSV file using the drag-and-drop interface
3. Preview the data before importing
4. Confirm the import to create student records and assign them to classes

A sample CSV file is provided at `public/sample_students.csv` for testing.

### Manual Student Entry

The Manual Student Entry form allows administrators to add individual students:

1. Fill in the required fields (first name, last name, email, class)
2. Optionally provide additional information (student ID, course, semester)
3. Submit the form to create a new student record
4. The system will generate a temporary password for the student

### Class Management

The Class Management interface allows administrators to:

1. Create new classes with names and descriptions
2. View existing classes and the number of students in each class
3. View the list of students in a class
4. Edit class details
5. Delete classes (this will remove all students from the class)

### Exam Scheduling

The Exam Scheduling interface allows administrators to:

1. Create new exams with titles, descriptions, and durations
2. Schedule exams for specific dates and times
3. Assign exams to specific classes
4. Set passing scores and total questions
5. View, edit, and delete existing exams

## 4. Security Considerations

### Authentication

- All admin features are protected by authentication
- Only users with admin privileges can access these features
- Row-Level Security (RLS) policies ensure that users can only access data they are authorized to see

### Data Validation

- All input is validated on both the client and server sides
- Email formats are validated using regular expressions
- Required fields are enforced
- Duplicate emails are detected and handled appropriately

### Password Security

- Temporary passwords are generated securely
- Students are prompted to change their temporary passwords on first login
- Passwords are never stored in plain text

## 5. Testing

### Test CSV Upload

1. Use the sample CSV file at `public/sample_students.csv`
2. Upload it using the CSV Upload interface
3. Verify that the students are imported correctly
4. Check that they are assigned to the correct classes

### Test Manual Student Entry

1. Create a new student using the Manual Student Entry form
2. Verify that the student is created correctly
3. Check that they are assigned to the selected class

### Test Class Management

1. Create a new class
2. Add students to the class
3. View the list of students in the class
4. Edit the class details
5. Delete the class and verify that students are removed from it

### Test Exam Scheduling

1. Create a new exam
2. Schedule it for a future date
3. Assign it to specific classes
4. Edit the exam details
5. Delete the exam

## 6. Troubleshooting

### CSV Upload Issues

- Ensure that the CSV file has the correct format
- Check that the class names in the CSV file match existing classes
- Verify that email addresses are unique

### Class Management Issues

- If a class cannot be deleted, check if there are exams assigned to it
- If students are not appearing in a class, verify that they are correctly assigned

### Exam Scheduling Issues

- If an exam cannot be scheduled, check that the date and time are valid
- If classes are not appearing in the class selector, verify that they exist

## 7. Next Steps

After setting up the admin interface, you may want to:

1. Create a student notification system for new accounts and exam schedules
2. Implement email verification for new student accounts
3. Add reporting features for exam results and attendance
4. Enhance the security with two-factor authentication for admin accounts
