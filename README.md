# Examino - Student Dashboard with Face Recognition

Examino is a modern student dashboard application with facial recognition for attendance tracking, secure exam interfaces, and real-time updates.

## Features

- **Face Recognition Attendance**: Mark attendance securely using facial recognition
- **Secure Exam Interface**: Take exams with anti-cheat features
- **Real-time Updates**: Get instant feedback on attendance and exam results
- **Modern UI**: Clean, responsive interface built with React and Tailwind CSS

## Setup

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/examino.git
cd examino

# Install dependencies
npm install

# Set up face detection models
node download-face-models.js

# Start the development server
npm run dev
```

### Face Detection Models

This project requires face-api.js model files for facial recognition features. To set up the models:

1. Run the provided script to download the models:
   ```bash
   node download-face-models.js
   ```

2. Verify that the models are in the `public/models` directory.

For detailed instructions, see [FACE_MODELS_SETUP.md](./FACE_MODELS_SETUP.md).

## Technology Stack

- **Frontend**: React, Tailwind CSS, Framer Motion
- **Authentication**: Supabase Auth
- **Database**: Supabase
- **Face Recognition**: face-api.js

## Documentation

- [Face Models Setup](./FACE_MODELS_SETUP.md)
- [Face Recognition Setup](./FACE_RECOGNITION_SETUP.md)
- [Supabase Setup](./SUPABASE_SETUP.md)
- [Exam System](./EXAM_SYSTEM_README.md)
- [Student Dashboard](./STUDENT_DASHBOARD_README.md)
- [Secure Login System](./SECURE_LOGIN_SYSTEM.md)
- [Class Management](./CLASS_MANAGEMENT_README.md)
- [Enhanced Features](./ENHANCED_FEATURES_README.md)
- [Enhanced Face Recognition](./FACE_RECOGNITION_ENHANCED_README.md)
- [Attendance Logs System](./ATTENDANCE_LOGS_README.md)
