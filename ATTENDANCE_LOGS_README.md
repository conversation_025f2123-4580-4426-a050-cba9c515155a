# Attendance Logs System for Examino

This document provides detailed information about the Attendance Logs system implemented in Examino, including real-time tracking, filtering, and reporting capabilities.

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Database Schema](#database-schema)
4. [Components](#components)
5. [Usage Guide](#usage-guide)
6. [Integration with Face Recognition](#integration-with-face-recognition)
7. [Reporting Capabilities](#reporting-capabilities)
8. [Security Considerations](#security-considerations)
9. [Performance Optimizations](#performance-optimizations)
10. [Troubleshooting](#troubleshooting)

## Overview

The Attendance Logs system provides a comprehensive solution for tracking and managing student attendance. It integrates with the face recognition system to provide secure and reliable attendance tracking, with features for filtering, exporting, and visualizing attendance data.

## Features

- **Real-time Attendance Tracking**: View attendance records as they are created
- **Filtering Capabilities**: Filter by date range, class, status, and student
- **Export to CSV**: Export attendance data for reporting and analysis
- **Calendar View**: View attendance records by date in a calendar format
- **List View**: View detailed attendance records in a list format
- **Statistics**: View attendance statistics including attendance rate
- **Face Verification Integration**: Mark attendance using face recognition
- **Manual Attendance Management**: Manually mark students as present or absent
- **Role-Based Access Control**: Different views for admin and institute users

## Database Schema

The attendance system uses the following database schema:

```sql
CREATE TABLE IF NOT EXISTS attendance (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
  class_id UUID REFERENCES classes(id) ON DELETE SET NULL,
  status BOOLEAN NOT NULL DEFAULT true, -- true = present, false = absent
  verification_method TEXT, -- 'face', 'manual', etc.
  confidence FLOAT, -- confidence score for face verification (0-1)
  device_info JSONB, -- device information for audit
  date DATE DEFAULT CURRENT_DATE, -- date of attendance
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Indexes

```sql
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_class_id ON attendance(class_id);
CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(date);
CREATE INDEX IF NOT EXISTS idx_attendance_created_at ON attendance(created_at);
```

### Database Functions

The system includes several database functions for retrieving and analyzing attendance data:

1. **get_attendance_by_date_range**: Retrieves attendance records for a specific date range and class
2. **get_attendance_statistics**: Calculates attendance statistics for a specific date range and class

## Components

### AttendanceLogs Component

The main component for displaying and managing attendance records in the admin dashboard. Features include:

- Filtering by date range, class, status, and student
- Exporting attendance data to CSV
- Viewing attendance statistics
- Managing attendance records (marking present/absent, deleting records)

### InstituteAttendanceLogs Component

A specialized version of the AttendanceLogs component for institute users. Includes additional features:

- Calendar view for visualizing attendance by date
- Enhanced statistics for institute-specific reporting
- Class-specific attendance tracking

### StudentVerification Component

Used for verifying student identities and marking attendance. Features include:

- Face recognition for identity verification
- Real-time attendance marking
- Integration with the attendance logs system

## Usage Guide

### Viewing Attendance Logs

1. Navigate to the "Attendance Logs" tab in the Admin or Institute Dashboard
2. Use the filters to select a date range, class, status, or search for a specific student
3. Click "Apply Filters" to update the displayed records
4. Use the "Export CSV" button to download the filtered records

### Marking Attendance

#### Using Face Recognition

1. Navigate to the "Face Verification" tab
2. Select a student from the list
3. Click "Verify Identity" to open the camera
4. Capture the student's face
5. If verification is successful, click "Mark Present" to record attendance

#### Manually

1. Navigate to the "Attendance Logs" tab
2. Find the student in the list
3. Click "Mark as Present" or "Mark as Absent" to update their attendance status

### Viewing Attendance Statistics

1. Navigate to the "Attendance Logs" tab
2. View the statistics section at the top of the page
3. See total records, present count, absent count, and attendance rate
4. Use the attendance chart to visualize the attendance rate

## Integration with Face Recognition

The Attendance Logs system integrates with the Face Recognition system to provide secure and reliable attendance tracking:

1. **Face Verification**: Students' identities are verified using face recognition
2. **Confidence Score**: Each verification includes a confidence score
3. **Audit Trail**: All verifications are logged with device information
4. **Real-time Updates**: Attendance records are updated in real-time

## Reporting Capabilities

The system includes several reporting capabilities:

1. **CSV Export**: Export attendance data for further analysis
2. **Attendance Statistics**: View attendance statistics by date range and class
3. **Calendar View**: Visualize attendance by date
4. **Class-specific Reports**: View attendance for specific classes

## Security Considerations

The system implements several security measures:

1. **Row-Level Security**: Restrict access to attendance records
2. **Audit Logging**: Track all attendance operations
3. **Role-Based Access Control**: Different views for admin and institute users
4. **Verification Method Tracking**: Record how attendance was verified

## Performance Optimizations

The system includes several performance optimizations:

1. **Indexed Queries**: Use database indexes for faster queries
2. **Lazy Loading**: Load attendance records only when needed
3. **Pagination**: Limit the number of records displayed at once
4. **Caching**: Cache frequently accessed data

## Troubleshooting

### Common Issues

1. **No Attendance Records**: Ensure that the date range and filters are correct
2. **Export Not Working**: Ensure that there are records to export
3. **Face Verification Failed**: Check lighting and positioning
4. **Real-time Updates Not Working**: Refresh the page to reconnect to the real-time channel
