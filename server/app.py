from flask import Flask, request, jsonify
from flask_cors import CORS
import cv2
import numpy as np
import face_recognition
import requests
from io import BytesIO
import os
import logging
import time
from dotenv import load_dotenv
import supabase

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Supabase client
supabase_url = os.getenv('SUPABASE_URL')
supabase_key = os.getenv('SUPABASE_KEY')
supabase_client = supabase.create_client(supabase_url, supabase_key)

# Rate limiting configuration
RATE_LIMIT = 10  # requests per minute
rate_limit_dict = {}

def is_rate_limited(ip_address):
    """Check if an IP address is rate limited"""
    current_time = time.time()
    if ip_address in rate_limit_dict:
        # Get the list of request timestamps
        timestamps = rate_limit_dict[ip_address]
        # Remove timestamps older than 1 minute
        timestamps = [ts for ts in timestamps if current_time - ts < 60]
        # Update the dictionary
        rate_limit_dict[ip_address] = timestamps
        # Check if the number of requests exceeds the limit
        if len(timestamps) >= RATE_LIMIT:
            return True
        # Add the current timestamp
        timestamps.append(current_time)
        return False
    else:
        # First request from this IP
        rate_limit_dict[ip_address] = [current_time]
        return False

def download_image(url):
    """Download an image from a URL"""
    try:
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        return BytesIO(response.content)
    except Exception as e:
        logger.error(f"Error downloading image: {e}")
        return None

def process_image(image_data):
    """Process an image for face recognition"""
    try:
        # Convert to numpy array
        image_array = np.asarray(bytearray(image_data.read()), dtype=np.uint8)
        # Decode the image
        image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
        # Convert BGR to RGB (face_recognition uses RGB)
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        return rgb_image
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        return None

def get_face_encoding(image):
    """Get face encoding from an image"""
    try:
        # Find all faces in the image
        face_locations = face_recognition.face_locations(image)
        if not face_locations:
            return None
        
        # Get face encodings
        face_encodings = face_recognition.face_encodings(image, face_locations)
        if not face_encodings:
            return None
        
        # Return the first face encoding
        return face_encodings[0]
    except Exception as e:
        logger.error(f"Error getting face encoding: {e}")
        return None

def calculate_face_quality(image, face_location):
    """Calculate the quality of a face image"""
    try:
        # Image dimensions
        height, width = image.shape[:2]
        
        # Face dimensions
        top, right, bottom, left = face_location
        face_width = right - left
        face_height = bottom - top
        
        # Calculate face size relative to image
        face_area = face_width * face_height
        image_area = width * height
        face_ratio = face_area / image_area
        
        # Calculate face position (center is best)
        face_center_x = (left + right) / 2
        face_center_y = (top + bottom) / 2
        image_center_x = width / 2
        image_center_y = height / 2
        
        # Calculate distance from center (normalized)
        max_distance = np.sqrt((width / 2) ** 2 + (height / 2) ** 2)
        distance = np.sqrt((face_center_x - image_center_x) ** 2 + (face_center_y - image_center_y) ** 2)
        normalized_distance = 1 - (distance / max_distance)
        
        # Calculate aspect ratio score (closer to 1:1 is better)
        aspect_ratio = face_width / face_height
        aspect_ratio_score = 1 - min(abs(aspect_ratio - 1), 0.5) * 2
        
        # Calculate final quality score (weighted average)
        size_weight = 0.5
        position_weight = 0.3
        aspect_ratio_weight = 0.2
        
        # Size score: optimal is 15-25% of image area
        if face_ratio < 0.05:
            # Face too small
            size_score = face_ratio * 20
        elif face_ratio > 0.6:
            # Face too large
            size_score = max(0, 1 - ((face_ratio - 0.6) * 2.5))
        else:
            # Face size is good
            size_score = 1
        
        return (
            size_score * size_weight +
            normalized_distance * position_weight +
            aspect_ratio_score * aspect_ratio_weight
        )
    except Exception as e:
        logger.error(f"Error calculating face quality: {e}")
        return 0

@app.route('/verify_face', methods=['POST'])
def verify_face():
    """Verify a face against a reference image"""
    try:
        # Check rate limiting
        ip_address = request.remote_addr
        if is_rate_limited(ip_address):
            return jsonify({"error": "Rate limit exceeded"}), 429
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        student_id = data.get('student_id')
        captured_image_url = data.get('captured_image_url')
        reference_image_url = data.get('reference_image_url')
        
        if not student_id or not captured_image_url or not reference_image_url:
            return jsonify({"error": "Missing required parameters"}), 400
        
        # Download images
        captured_image_data = download_image(captured_image_url)
        reference_image_data = download_image(reference_image_url)
        
        if not captured_image_data or not reference_image_data:
            return jsonify({"error": "Failed to download images"}), 400
        
        # Process images
        captured_image = process_image(captured_image_data)
        reference_image = process_image(reference_image_data)
        
        if captured_image is None or reference_image is None:
            return jsonify({"error": "Failed to process images"}), 400
        
        # Get face encodings
        captured_encoding = get_face_encoding(captured_image)
        reference_encoding = get_face_encoding(reference_image)
        
        if captured_encoding is None:
            return jsonify({"error": "No face detected in captured image"}), 400
        
        if reference_encoding is None:
            return jsonify({"error": "No face detected in reference image"}), 400
        
        # Compare faces
        # face_distance returns a numpy array containing a distance for each face in the reference
        # Lower distance means more similar
        face_distance = face_recognition.face_distance([reference_encoding], captured_encoding)[0]
        
        # Convert distance to similarity (0-1)
        # 0.6 is a good threshold for face recognition
        threshold = 0.6
        similarity = max(0, 1 - (face_distance / threshold))
        
        # Determine if it's a match
        is_match = similarity > 0.7  # 70% confidence threshold
        
        # Calculate face quality
        face_locations = face_recognition.face_locations(captured_image)
        quality = calculate_face_quality(captured_image, face_locations[0]) if face_locations else 0
        
        # Log the verification attempt
        logger.info(f"Face verification: student_id={student_id}, match={is_match}, confidence={similarity:.2f}, quality={quality:.2f}")
        
        # Return the result
        return jsonify({
            "is_match": is_match,
            "confidence": float(similarity),
            "quality": float(quality)
        })
    
    except Exception as e:
        logger.error(f"Error in face verification: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "ok"})

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_ENV') == 'development'
    app.run(host='0.0.0.0', port=port, debug=debug)
