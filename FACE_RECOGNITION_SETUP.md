# Face Recognition Setup for Examino

This guide will help you set up the face recognition system for Examino, which enables secure attendance tracking and exam authentication.

## 1. Supabase Setup

### Create a Supabase Project

1. Go to [https://supabase.com](https://supabase.com) and sign up or log in
2. Create a new project
3. Choose a name for your project (e.g., "examino")
4. Set a secure database password
5. Choose a region closest to your users
6. Wait for your project to be created (this may take a few minutes)

### Database Schema Setup

1. Go to the SQL Editor in your Supabase dashboard
2. Create a new query
3. Copy and paste the contents of the `supabase/migrations/03_face_recognition_schema.sql` file
4. Run the query to create the necessary tables and security policies

### Storage Buckets Setup

1. Go to Storage in your Supabase dashboard
2. Create two buckets:
   - `reference`: For storing reference face images (private)
   - `attendance`: For storing attendance verification images (public)

3. Set up bucket policies:
   - For `reference` bucket: Only allow authenticated users to upload to their own folder
   - For `attendance` bucket: Allow authenticated users to upload to their own folder

## 2. Face Recognition Models Setup

1. Download the required face-api.js models:

```bash
mkdir -p public/models
cd public/models
```

2. Download the following model files from [face-api.js GitHub repository](https://github.com/justadudewhohacks/face-api.js/tree/master/weights):

   - SSD Mobilenet V1 Model:
     - `ssd_mobilenetv1_model-weights_manifest.json`
     - `ssd_mobilenetv1_model-shard1`
   
   - Face Landmark Model:
     - `face_landmark_68_model-weights_manifest.json`
     - `face_landmark_68_model-shard1`
   
   - Face Recognition Model:
     - `face_recognition_model-weights_manifest.json`
     - `face_recognition_model-shard1`

## 3. Environment Configuration

1. Copy the `.env.example` file to `.env`:

```bash
cp .env.example .env
```

2. Update the `.env` file with your Supabase credentials:

```
VITE_SUPABASE_URL=https://your-project-url.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_FACE_MATCH_THRESHOLD=0.7
VITE_FACE_ENCRYPTION_KEY=your-encryption-key
```

## 4. Testing the Face Recognition System

### Admin: Registering Student Face Data

1. Log in as an admin user
2. Navigate to the Admin Dashboard
3. Go to Student Management
4. Select a student and click "Register Face"
5. Use the webcam to capture a clear image of the student's face
6. Save the face data

### Student: Marking Attendance

1. Log in as a student
2. Click on "Mark Attendance" from the dashboard
3. Position your face in the circular guide
4. Capture your photo
5. The system will verify your face against the stored reference
6. If successful, your attendance will be recorded

## 5. Troubleshooting

### Face Detection Issues

- Ensure adequate lighting when capturing face images
- Position the face clearly in the center of the frame
- Remove glasses, hats, or other items that may obscure facial features
- Check that all face-api.js model files are correctly placed in the `/public/models` directory

### Database Connection Issues

- Verify that your Supabase URL and anon key are correct in the `.env` file
- Check that the database schema has been properly set up
- Ensure that the Row-Level Security (RLS) policies are correctly configured

### Storage Issues

- Verify that the storage buckets have been created with the correct names
- Check that the bucket policies allow the appropriate access
- Ensure that the file paths used in the code match the bucket structure

## 6. Security Considerations

- Face descriptors are encrypted before being stored in the database
- The encryption key should be kept secure and not exposed in client-side code
- Consider implementing server-side verification for high-security applications
- Regularly audit attendance logs for suspicious patterns
- Implement IP address logging and device fingerprinting for additional security
