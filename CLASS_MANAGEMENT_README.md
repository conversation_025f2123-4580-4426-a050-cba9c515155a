# Class Management System

The Class Management System in Examino allows administrators and institute users to create, manage, and organize classes for students.

## Features

- **Create Classes**: Create new classes with names and descriptions
- **Manage Classes**: Edit or delete existing classes
- **View Students**: See which students are enrolled in each class
- **Student Assignment**: Assign students to classes during student creation
- **Exam Assignment**: Assign exams to specific classes

## Database Schema

The class management system uses the following database tables:

### Classes Table

| Column      | Type        | Description                       |
|-------------|-------------|-----------------------------------|
| id          | UUID        | Primary key                       |
| name        | TEXT        | Class name                        |
| description | TEXT        | Optional class description        |
| created_by  | UUID        | Reference to the creating user    |
| created_at  | TIMESTAMPTZ | Creation timestamp                |
| updated_at  | TIMESTAMPTZ | Last update timestamp             |

### Student-Class Relationship Table

| Column     | Type        | Description                       |
|------------|-------------|-----------------------------------|
| id         | UUID        | Primary key                       |
| student_id | UUID        | Reference to student              |
| class_id   | UUID        | Reference to class                |
| joined_at  | TIMESTAMPTZ | When the student joined the class |

## Access Control

- **Administrators** and **Institute Users** can create, view, edit, and delete classes
- **Students** can only view classes they are enrolled in
- Row-Level Security (RLS) policies enforce these permissions at the database level

## Integration Points

The Class Management System integrates with:

1. **Student Management**: When creating students, they can be assigned to classes
2. **Exam Scheduling**: Exams can be assigned to specific classes
3. **Attendance Tracking**: Attendance can be tracked per class

## Usage

### Creating a Class

1. Navigate to the **Class Management** tab in the Admin Dashboard
2. Click the **Create New Class** button
3. Enter a class name (required) and description (optional)
4. Click **Create Class**

### Editing a Class

1. Find the class in the list
2. Click the **Edit** button
3. Update the class details
4. Click **Update Class**

### Deleting a Class

1. Find the class in the list
2. Click the **Delete** button
3. Confirm the deletion

### Viewing Students in a Class

1. Click on a class in the list to expand it
2. The list of enrolled students will be displayed

## Implementation Details

The Class Management System is implemented using:

- React components for the UI
- Supabase for data storage and retrieval
- Row-Level Security for access control
- SQL functions for complex queries

## Future Enhancements

Planned enhancements for the Class Management System include:

- Bulk student assignment to classes
- Class attendance reports
- Class performance analytics
- Class schedules and timetables
